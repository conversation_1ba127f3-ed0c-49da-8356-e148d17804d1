'use client';

import type { CompleteTopRank } from '@/types/common';

type RankingCrownBadgeProps = {
  rank: CompleteTopRank;
  color: string;
  className?: string;
};

export const RankingCrownBadge = ({ rank, color, className = '' }: RankingCrownBadgeProps) => {
  const getSVGContent = () => {
    const crown = (
      <path
        d="M16.3045 1.93911C15.9081 1.93911 15.5882 2.24355 15.5882 2.62078C15.5882 2.79947 15.6577 2.95831 15.776 3.07743L13.0705 5.41363L10.4208 1.35672C10.6364 1.22435 10.7824 0.999338 10.7824 0.734613C10.7824 0.330907 10.4347 0 10.0104 0C9.58619 0 9.23845 0.330907 9.23845 0.734613C9.23845 0.999338 9.3845 1.22435 9.6001 1.35672L6.95032 5.41363L4.24491 3.07743C4.36314 2.95831 4.43269 2.79947 4.43269 2.62078C4.43269 2.24355 4.11277 1.93911 3.71634 1.93911C3.31992 1.93911 3 2.24355 3 2.62078C3 2.99801 3.31992 3.30245 3.71634 3.30245C3.75112 3.30245 3.79285 3.30245 3.82762 3.29583L4.89866 10H15.1013L16.1724 3.29583C16.2072 3.29583 16.2489 3.30245 16.2837 3.30245C16.6801 3.30245 17 2.99801 17 2.62078C17 2.24355 16.6801 1.93911 16.2837 1.93911H16.3045Z"
        fill="white"
      />
    );

    switch (rank) {
      case 1:
        return (
          <>
            {crown}
            <circle cx="10" cy="18" r="9.5" fill="white" stroke={color} />
            <path
              d="M11.2715 13.0127V23H9.62402V14.9678L7.18359 15.7949V14.4346L11.0732 13.0127H11.2715Z"
              fill={color}
            />
          </>
        );
      case 2:
        return (
          <>
            {crown}
            <circle cx="10" cy="18" r="9.5" fill="white" stroke={color} />
            <path
              d="M13.4453 21.6875V23H6.77344V21.8721L10.0137 18.3379C10.3691 17.9368 10.6494 17.5905 10.8545 17.2988C11.0596 17.0072 11.2031 16.7451 11.2852 16.5127C11.3717 16.2757 11.415 16.0456 11.415 15.8223C11.415 15.5078 11.3558 15.2321 11.2373 14.9951C11.1234 14.7536 10.9548 14.5645 10.7314 14.4277C10.5081 14.2865 10.237 14.2158 9.91797 14.2158C9.54883 14.2158 9.23893 14.2956 8.98828 14.4551C8.73763 14.6146 8.5485 14.8356 8.4209 15.1182C8.29329 15.3962 8.22949 15.7152 8.22949 16.0752H6.58203C6.58203 15.4964 6.71419 14.9678 6.97852 14.4893C7.24284 14.0062 7.62565 13.6234 8.12695 13.3408C8.62826 13.0537 9.2321 12.9102 9.93848 12.9102C10.6038 12.9102 11.1689 13.0218 11.6338 13.2451C12.0986 13.4684 12.4518 13.7852 12.6934 14.1953C12.9395 14.6055 13.0625 15.0908 13.0625 15.6514C13.0625 15.9613 13.0124 16.2689 12.9121 16.5742C12.8118 16.8796 12.6683 17.1849 12.4814 17.4902C12.2992 17.791 12.0827 18.0941 11.832 18.3994C11.5814 18.7002 11.3057 19.0055 11.0049 19.3154L8.85156 21.6875H13.4453Z"
              fill={color}
            />
          </>
        );
      case 3:
        return (
          <>
            {crown}
            <circle cx="10" cy="18" r="9.5" fill="white" stroke={color} />
            <path
              d="M8.70801 17.2783H9.69238C10.0752 17.2783 10.3919 17.2122 10.6426 17.0801C10.8978 16.9479 11.0869 16.7656 11.21 16.5332C11.333 16.3008 11.3945 16.0342 11.3945 15.7334C11.3945 15.4189 11.3376 15.1501 11.2236 14.9268C11.1143 14.6989 10.9456 14.5234 10.7178 14.4004C10.4945 14.2773 10.2096 14.2158 9.86328 14.2158C9.57161 14.2158 9.30729 14.2751 9.07031 14.3936C8.83789 14.5075 8.65332 14.6715 8.5166 14.8857C8.37988 15.0954 8.31152 15.346 8.31152 15.6377H6.65723C6.65723 15.109 6.79622 14.6396 7.07422 14.2295C7.35221 13.8193 7.73047 13.498 8.20898 13.2656C8.69206 13.0286 9.23438 12.9102 9.83594 12.9102C10.4785 12.9102 11.0391 13.0173 11.5176 13.2314C12.0007 13.4411 12.3766 13.7555 12.6455 14.1748C12.9144 14.5941 13.0488 15.1136 13.0488 15.7334C13.0488 16.016 12.9827 16.3031 12.8506 16.5947C12.7184 16.8864 12.5225 17.153 12.2627 17.3945C12.0029 17.6315 11.6794 17.8252 11.292 17.9756C10.9046 18.1214 10.4557 18.1943 9.94531 18.1943H8.70801V17.2783ZM8.70801 18.5635V17.6611H9.94531C10.5286 17.6611 11.0254 17.7295 11.4355 17.8662C11.8503 18.0029 12.1875 18.1921 12.4473 18.4336C12.707 18.6706 12.8962 18.9417 13.0146 19.2471C13.1377 19.5524 13.1992 19.876 13.1992 20.2178C13.1992 20.6826 13.1149 21.0973 12.9463 21.4619C12.7822 21.8219 12.5475 22.1273 12.2422 22.3779C11.9368 22.6286 11.5791 22.8177 11.1689 22.9453C10.7633 23.0729 10.3213 23.1367 9.84277 23.1367C9.41439 23.1367 9.00423 23.0775 8.6123 22.959C8.22038 22.8405 7.86947 22.665 7.55957 22.4326C7.24967 22.1956 7.00358 21.9017 6.82129 21.5508C6.64355 21.1953 6.55469 20.7852 6.55469 20.3203H8.20215C8.20215 20.6165 8.27051 20.8786 8.40723 21.1064C8.5485 21.3298 8.74447 21.5052 8.99512 21.6328C9.25033 21.7604 9.54199 21.8242 9.87012 21.8242C10.2165 21.8242 10.515 21.7627 10.7656 21.6396C11.0163 21.5166 11.2077 21.3343 11.3398 21.0928C11.4766 20.8512 11.5449 20.5596 11.5449 20.2178C11.5449 19.8304 11.4697 19.516 11.3193 19.2744C11.1689 19.0329 10.9548 18.8551 10.6768 18.7412C10.3988 18.6227 10.0706 18.5635 9.69238 18.5635H8.70801Z"
              fill={color}
            />
          </>
        );
    }
  };

  return (
    <svg
      width="20"
      height="28"
      viewBox="0 0 20 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {getSVGContent()}
    </svg>
  );
};
