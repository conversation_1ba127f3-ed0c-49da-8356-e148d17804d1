import ItemIcon from '@/components/atoms/itemIcon';
type CircleBorder = {
  color: 'light' | 'dark';
  width: number;
  style: 'solid' | 'dashed';
};
type CircleItemIconProps = {
  thumbnail: string;
  title: string;
  circleBgUrl: string;
  circleBorder: CircleBorder;
  width: number;
};
const getBorderStyle = (border: CircleBorder) => {
  const borderWidth = `border-${border.width / 4}`;
  const borderColor = border.color === 'light' ? 'border-white' : 'border-secondary';
  const borderStyle = `border-${border.style}`;
  return `${borderWidth} ${borderColor} ${borderStyle}`;
};
const CircleItemIcon = ({ thumbnail, title, circleBgUrl, circleBorder, width }: CircleItemIconProps) => {
  const borderStyle = getBorderStyle(circleBorder);
  const widthCss = `w-${width / 4}`;
  return (
    <div
      className={`flex h-full shrink-0 items-center justify-center bg-cover bg-center bg-repeat ${widthCss}`}
      style={{ background: circleBgUrl }}
    >
      <div className={`flex size-26 items-center justify-center rounded-full bg-white ${borderStyle}`}>
        <ItemIcon thumbnail={thumbnail} thumbnailRatio={1} title={title} size={96} radius="full" />
      </div>
    </div>
  );
};

export default CircleItemIcon;
