import React from 'react';

interface RightArrowIconProps {
  color?: string;
  width?: number;
  height?: number;
  className?: string;
}

export const RightArrowIcon = ({ color = 'white', width = 14, height = 14, className = '' }: RightArrowIconProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10.5 7.00001L5.28441 12.8333L4.66666 12.2232L9.33682 7.00001L4.66666 1.77684L5.28441 1.16668L10.5 7.00001Z"
        fill={color}
      />
    </svg>
  );
};
