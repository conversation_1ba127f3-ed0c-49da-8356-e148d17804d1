'use client';
import { FC, ReactNode, useState } from 'react';
import clsx from 'clsx';

interface RainbowButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

const RainbowButton: FC<RainbowButtonProps> = ({
  children,
  onClick,
  className = '',
  disabled = false,
  type = 'button',
  ...otherProps
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const handleMouseDown = () => {
    setIsPressed(true);
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (type !== 'submit') {
      e.preventDefault();
    }
    if (onClick && !disabled) {
      onClick();
    }
  };

  return (
    <button
      className={clsx(
        'relative h-auto w-81 rounded-4xl py-3 text-center font-bold text-gray-800',
        'transition-all duration-200 ease-in-out',
        {
          'cursor-pointer': !disabled,
          'cursor-not-allowed opacity-60': disabled,
        },
        className,
      )}
      type={type}
      disabled={disabled}
      onClick={handleClick}
      onMouseDown={!disabled ? handleMouseDown : undefined}
      onMouseUp={!disabled ? handleMouseUp : undefined}
      onMouseLeave={!disabled ? () => setIsPressed(false) : undefined}
      onTouchStart={!disabled ? handleMouseDown : undefined}
      onTouchEnd={!disabled ? handleMouseUp : undefined}
      {...otherProps}
    >
      <div
        className={clsx(
          'gradient-purple-pink-yellow absolute inset-0 top-5 h-12 -translate-y-[7px] translate-x-[1px] rounded-full',
          'transition-all duration-200',
          {
            'h-10 -translate-y-[6px] scale-[0.96]': isPressed,
          },
        )}
      />

      <div
        className={clsx(
          'gradient-blue-teal-green absolute inset-0 h-12 translate-x-[7px] translate-y-[20px] rounded-full',
          'transition-all duration-200',
          {
            'h-10 translate-x-[0px] translate-y-[1px] scale-[0.96]': isPressed,
          },
        )}
      />

      <div
        className={clsx(
          'relative z-10 flex h-full w-full translate-x-[4px] translate-y-[4px] items-center justify-center rounded-full bg-white px-6 py-3',
          'transition-all duration-200 hover:bg-gray-50',
          {
            'translate-x-[2px] translate-y-[2px]': isPressed,
          },
        )}
      >
        {children}
      </div>
    </button>
  );
};

export default RainbowButton;
