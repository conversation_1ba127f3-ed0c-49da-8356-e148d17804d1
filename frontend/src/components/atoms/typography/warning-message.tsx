import React from 'react';
import ShopPublicImage from '@/components/ShopImage';

type WarningMessageProps = {
  message: string;
};

const WarningMessage: React.FC<WarningMessageProps> = ({ message }) => {
  return (
    <span className="flex items-center gap-1 text-medium-11 text-orange-200">
      <ShopPublicImage src="/images/icons/orangeExclamation.svg" width={12} height={12} alt="warning" />
      {message}
    </span>
  );
};

export default WarningMessage;
