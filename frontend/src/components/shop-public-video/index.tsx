import { VideoHTMLAttributes, forwardRef } from 'react';

type ShopPublicVideoProps = VideoHTMLAttributes<HTMLVideoElement>;

const ShopPublicVideo = forwardRef<HTMLVideoElement, ShopPublicVideoProps>((props, ref) => {
  const src = ('/shop' + props.src) as string;

  return <video {...props} ref={ref} src={src} />;
});

ShopPublicVideo.displayName = 'ShopPublicVideo';

export default ShopPublicVideo;
