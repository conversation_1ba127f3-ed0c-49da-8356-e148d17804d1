'use client';
import clsx from 'clsx';
import Accordion from '@/components/atoms/accordion';
import ShopPublicImage from '@/components/ShopImage';
import { roboto } from '@/app/fonts';
import { AWARD_TYPE, GachaItemFile } from '@/types/gacha';
type AwardRank = 'S' | 'A' | 'B' | 'C';
type CompleteProgressBarProps = {
  awardType: AwardRank;
  count: number;
  total: number;
};

const CompleteProgressBar = ({ awardType, count, total }: CompleteProgressBarProps) => {
  const getAward = (awardType: AwardRank) => {
    switch (awardType) {
      case 'S':
        return 'RarityS';
      case 'A':
        return 'RarityA';
      case 'B':
        return 'RarityB';
      case 'C':
        return 'RarityC';
      default:
        return 'RarityS';
    }
  };
  // Get the appropriate CSS class for the progress bar based on award type and progress percentage
  const getProgressBarClass = (awardType: AwardRank, percentage: number): string => {
    switch (awardType) {
      case 'S':
        if (percentage <= 20) return 'progress-bar-s-20';
        if (percentage <= 40) return 'progress-bar-s-40';
        if (percentage <= 60) return 'progress-bar-s-60';
        if (percentage <= 80) return 'progress-bar-s-80';
        return 'progress-bar-s-100';
      case 'A':
        return percentage <= 50 ? 'progress-bar-a-50' : 'progress-bar-a-100';
      case 'B':
        return percentage <= 50 ? 'progress-bar-b-50' : 'progress-bar-b-100';
      case 'C':
        return percentage <= 50 ? 'progress-bar-c-50' : 'progress-bar-c-100';
      default:
        return 'progress-bar-s-100';
    }
  };

  // Calculate progress percentage
  const progressPercentage = Math.min(100, Math.round((count / total) * 100));
  const isCompleted = count === total;

  return (
    <div className="flex items-center justify-between gap-2.5">
      <ShopPublicImage
        src={`/images/gacha/icons/${getAward(awardType)}.svg`}
        alt={getAward(awardType)}
        width={54}
        height={32}
        className="object-contain"
      />
      <div className="relative h-4 w-9/12 overflow-hidden rounded-full bg-gray-600">
        {/* Progress bar with gradient - using appropriate gradient for current progress */}
        <div
          className={`absolute left-0 top-0 h-full rounded-full ${getProgressBarClass(awardType, progressPercentage)}`}
          style={{ width: `${progressPercentage}%` }}
        >
          {/* White circle at the end of progress bar */}
          {progressPercentage > 0 && (
            <div className="absolute right-px top-2 flex h-3.5 w-3.5 -translate-y-1/2 items-center justify-center rounded-full bg-white-mask">
              <div className="size-2.5 rounded-full bg-white"></div>
            </div>
          )}
        </div>

        {/* Completed!! text */}
        {isCompleted && (
          <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
            <span className={clsx('glow-white text-bold-14', roboto.className)}>Complete!!</span>
          </div>
        )}
      </div>
      <div className={clsx('w-20 text-right', !count ? 'text-gray-550' : 'text-white')}>
        <span className="text-medium-22">{count} </span> <span className="text-regular-16">/ {total}</span>
      </div>
    </div>
  );
};

type AwardData = {
  [key in AwardRank]: {
    count: number;
    total: number;
  };
};
export type GachaCompleteProgressBarProps = {
  itemFiles: GachaItemFile[];
  isPreview: boolean;
  duplicatedCount?: number;
};

const GachaCompleteProgressBar = ({ props }: { props: GachaCompleteProgressBarProps }) => {
  const { itemFiles, isPreview } = props;

  // Calculate progress for each rank
  const progressBarData: Partial<AwardData> = {};

  // Process items to build progressBarData
  itemFiles?.forEach((item) => {
    const rank: AwardRank =
      item.awardType === AWARD_TYPE.S
        ? 'S'
        : item.awardType === AWARD_TYPE.A
          ? 'A'
          : item.awardType === AWARD_TYPE.B
            ? 'B'
            : 'C';

    // Initialize the rank data if it doesn't exist
    if (!progressBarData[rank]) {
      progressBarData[rank] = { count: 0, total: 0 };
    }

    // Update counts
    progressBarData[rank]!.total += 1;
    if (item.receivedFileCount) {
      progressBarData[rank]!.count += 1;
    }
  });

  // Sort ranks in order: S, A, B, C
  const rankOrder: AwardRank[] = ['S', 'A', 'B', 'C'];
  const sortedEntries = Object.entries(progressBarData).sort(([rankA], [rankB]) => {
    return rankOrder.indexOf(rankA as AwardRank) - rankOrder.indexOf(rankB as AwardRank);
  });

  // Custom title component for the accordion
  const accordionTitle = (title: string) => <div className="text-bold-16 text-white">{title}</div>;

  return (
    <div className="flex flex-col gap-4 rounded-b-2xl bg-secondary p-4">
      <Accordion
        title={accordionTitle('ランク別コンプリート数')}
        type="transparent-free"
        defaultOpen={true}
        arrowColor="white"
      >
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-4 rounded-lg bg-gray-700 px-2 py-4">
            {sortedEntries.map(([rank, { count, total }]) => (
              <CompleteProgressBar
                key={rank}
                awardType={rank as AwardRank}
                count={isPreview ? total : count}
                total={total}
              />
            ))}
          </div>
          {/*1stリリース以降で、重複したアイテムの数に応じた特典の配布をするときのアンコメントする*/}
          {/*<div className="flex h-10 items-center justify-between rounded-lg bg-gray-700 px-2">*/}
          {/*  <div className="text-bold-16 text-white">同じ絵柄が被った数</div>*/}
          {/*  <div className={clsx('w-15 text-right text-white')}>*/}
          {/*    <span className="text-medium-22">{duplicatedCount} </span>*/}
          {/*    <span className="text-regular-16">/ {itemFiles.length}</span>*/}
          {/*  </div>*/}
          {/*</div>*/}
        </div>
      </Accordion>
      <div className="flex items-center justify-center">
        <ShopPublicImage src="/images/icons/zukan.svg" alt="zukan" width={181} height={24} />
      </div>
    </div>
  );
};

export default GachaCompleteProgressBar;
