import React from 'react';

type CvsPaymentUrlProps = {
  convenience: string | null | undefined;
};

const cvsUrl = (convenience: string | undefined) => {
  switch (convenience) {
    case 'ローソン':
      return 'https://static.mul-pay.jp/customer-convenience-store/lawson/14.html';
    case 'ファミリーマート':
      return 'https://www.family.co.jp/services/application/receipt.html';
    case 'セイコーマート':
      return 'https://www.econtext.jp/support/cvs/seicomart.html';
    case 'ミニストップ':
      return 'https://www.econtext.jp/support/cvs/lawson.html';
  }
};

const CvsPaymentUrl = ({ convenience }: CvsPaymentUrlProps) => {
  if (!convenience) return;

  const url = cvsUrl(convenience);
  return (
    <div className="text-regular-12">
      コンビニ決済のやり方は
      <a href={url} target="_blank" rel="noopener noreferrer" className="text-orange-300 underline">
        こちら
      </a>
    </div>
  );
};

export default CvsPaymentUrl;
