import React from 'react';
import Image from 'next/image';
import ShopPublicImage from '@/components/ShopImage';
import { getPurchasedItemReceivedFiles } from '@/lib/server-api/gacha-endpoint/gacha-endpoint';

type ReceivedItemsSectionProps = {
  purchasedItemId: number;
};

const ReceivedItemsSection = async ({ purchasedItemId }: ReceivedItemsSectionProps) => {
  const itemFiles = await getPurchasedItemReceivedFiles(purchasedItemId);

  if (!itemFiles || !itemFiles.data || !itemFiles.data.files || itemFiles.data.files.length === 0) return null;

  return (
    <section className="flex flex-col items-center justify-center gap-3 bg-white px-2 py-6">
      <div className="flex w-full items-center justify-start gap-1 p-1">
        <ShopPublicImage src="/images/icons/Trophy.svg" alt="trophy" width={24} height={24} />
        <h2 className="text-bold-14 text-black">今回獲得した商品</h2>
      </div>
      {itemFiles.data.files.map((file) => (
        <div key={file.id} className="flex h-27.5 w-full items-center gap-3 rounded-base border border-gray-200 p-1">
          <div className="relative h-25 w-25 shrink-0">
            {file.thumbnail && <Image src={file.thumbnail} alt={file.title} fill className="rounded-lg object-cover" />}
          </div>
          <div className="flex min-w-0 flex-1 items-center gap-2 pl-1 pr-5">
            <div className="flex-1">
              <p className="line-clamp-3 text-bold-14 text-gray-900">{file.title}</p>
            </div>
            <div className="ml-2 shrink-0">
              <span className="text-bold-16 text-gray-900">×{file.count}</span>
            </div>
          </div>
        </div>
      ))}
    </section>
  );
};

export default ReceivedItemsSection;
