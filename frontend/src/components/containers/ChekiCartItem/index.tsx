'use client';

import React from 'react';
import { Control, FieldValues, UseFormWatch } from 'react-hook-form';
import SelectInput from '@/components/atoms/inputs/select-input';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import FlatItem from '@/components/containers/FlatItem';
import { CART_ITEM_MAX_QUANTITY, PURCHASER_COMMENT_MAX_LENGTH } from '@/consts/inputLength';

interface IChekiCartItemProps<T extends FieldValues> {
  id: number;
  imageUrl: string;
  title: string;
  available?: boolean;
  price: number;
  currentPrice?: number;
  discountRate?: number;
  isNowOnSale?: boolean;
  purchasableQuantity?: number;
  onRemove?: (id: number) => void;
  thumbnailRatio: number;
  className?: string;
  control: Control<T>;
  watch: UseFormWatch<T>;
  purchaserCommentInputName: string;
  quantityInputName: string;
}

const ChekiCartItem = ({
  id,
  imageUrl,
  title,
  price,
  currentPrice,
  discountRate,
  isNowOnSale,
  purchasableQuantity,
  onRemove,
  thumbnailRatio,
  className,
  control,
  watch,
  purchaserCommentInputName,
  quantityInputName,
}: IChekiCartItemProps<FieldValues>) => {
  return (
    <div className="flex w-full flex-col">
      <FlatItem
        id={id}
        imageUrl={imageUrl}
        title={title}
        price={price}
        currentPrice={currentPrice}
        discountRate={discountRate}
        isNowOnSale={isNowOnSale}
        onRemove={onRemove}
        thumbnailRatio={thumbnailRatio}
        className={className}
        isSet={true}
        showRemoveButton={true}
      />
      <div className="flex w-full flex-col bg-gray-150 p-2">
        <TextAreaInput
          control={control}
          inputName={purchaserCommentInputName}
          labelTitle="備考"
          maxLength={PURCHASER_COMMENT_MAX_LENGTH}
          className="!mb-2"
          textLength={watch ? watch(purchaserCommentInputName)?.length || 0 : 0}
        />
        <div>
          <span className="flex min-w-20 items-center justify-between text-medium-13 text-secondary">数量</span>
          {!!purchasableQuantity && (
            <span className="mb-2 flex w-30 text-medium-11 text-gray-500">*お一人様{purchasableQuantity}点まで</span>
          )}
          <SelectInput
            control={control}
            inputName={quantityInputName}
            options={Array.from({ length: purchasableQuantity || CART_ITEM_MAX_QUANTITY }, (_, i) => ({
              value: i + 1,
              label: (i + 1).toString(),
            }))}
            className="!mb-2 w-12"
          />
        </div>
      </div>
    </div>
  );
};

export default ChekiCartItem;
