import clsx from 'clsx';
import moment from 'moment';
import StateBadge from '@/components/atoms/badges/state-badge';
import ShopPublicImage from '@/components/ShopImage';
import { usePeriodState } from '@/hooks/usePeriodState';
import { PeriodState, Period } from '@/types/shopItem';

type DetailPeriodSectionProps = {
  period: Period;
  isPreview: boolean;
};

const getBadgeColor = (periodState?: PeriodState, isPreview?: boolean) => {
  if (!periodState) return;
  switch (periodState) {
    case PeriodState.BeforeStart:
      if (!isPreview) return 'blue';
      return 'white';
    case PeriodState.Ongoing:
      return 'white';
    case PeriodState.NearlyEnded:
      return 'orange';
    case PeriodState.Ended:
      return 'darkGray';
  }
};
const DetailPeriodSection = ({ period, isPreview }: DetailPeriodSectionProps) => {
  const { periodState, leftTime } = usePeriodState(period);
  const badgeColor = getBadgeColor(periodState, isPreview);

  return (
    !!periodState &&
    !!period && (
      <div className="mt-4 flex items-center justify-start">
        <StateBadge type="half-round-filled" color={badgeColor!}>
          <ShopPublicImage
            src="/images/icons/Time.svg"
            alt="left"
            width={16}
            height={16}
            className={clsx(
              {
                'brightness-1000':
                  periodState !== PeriodState.Ongoing && !(periodState === PeriodState.BeforeStart && isPreview),
              },
              'mr-1.5',
            )}
          />
          {periodState === PeriodState.BeforeStart && !isPreview ? '販売開始' : null}
          {(periodState === PeriodState.Ongoing || (periodState === PeriodState.BeforeStart && isPreview)) &&
            '販売期間'}
          {periodState === PeriodState.NearlyEnded && '終了直近'}
          {periodState === PeriodState.Ended && '販売終了'}
        </StateBadge>
        <div className="ml-3 text-medium-15">
          {periodState === PeriodState.BeforeStart &&
            !isPreview &&
            `${moment(period.start).format('YYYY年MM月DD日 HH:mm')} から`}
          {(periodState === PeriodState.Ongoing || (periodState === PeriodState.BeforeStart && isPreview)) && (
            <>
              {!!period.start && <div>{moment(period.start).format('YYYY年MM月DD日 HH:mm') + 'から'}</div>}
              {!!period.end && <div>{moment(period.end).format('YYYY年MM月DD日 HH:mm')}まで</div>}
            </>
          )}
          {periodState === PeriodState.NearlyEnded && (
            <span className={clsx(periodState === PeriodState.NearlyEnded && 'text-orange-100')}>
              {`残り${leftTime}`}
            </span>
          )}
          <span>{periodState === PeriodState.Ended && '終了'}</span>
        </div>
      </div>
    )
  );
};

export default DetailPeriodSection;
