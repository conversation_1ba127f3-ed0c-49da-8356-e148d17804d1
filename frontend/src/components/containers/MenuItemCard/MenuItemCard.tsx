'use client';
import { useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import { useRouter } from 'next/navigation';
import LoadingOverlay from '@/components/containers/itemForm/LoadingOverlay';
import ShopPublicImage from '@/components/ShopImage';
import { FeatureFlags } from '@/lib/feature';
import { LINE_CONTACT_ID } from '@/consts/url';
import { isIphoneChrome } from '@/utils/userAgent';
import { ITEM_TYPE, ItemTypeString, ItemTypeValue } from '@/types/item';

const ICONS = {
  image: '/images/icons/PhotoIcn.svg',
  audio: '/images/icons/VoiceIcn.svg',
  video: '/images/icons/MovieIcn.svg',
} as const;

type MenuItem = {
  id: ItemTypeValue;
  itemType: ItemTypeString;
  itemTypeValue: number;
  title: string;
  description: string;
  contents: string[];
  src: string;
  alt: string;
  width?: number;
  height?: number;
};

type MenuItemCardProps = {
  isChekiExhibitable: boolean;
  identityId: string;
};
const menuItems = (isChekiExhibitable: boolean): MenuItem[] => [
  {
    id: ITEM_TYPE.DIGITAL_BUNDLE.value,
    itemType: ITEM_TYPE.DIGITAL_BUNDLE.str,
    itemTypeValue: ITEM_TYPE.DIGITAL_BUNDLE.value,
    title: 'デジタルコンテンツ',
    description: '画像/動画/音声を販売',
    contents: ['image', 'audio', 'video'],
    src: '/shop/images/mockDigitalContents.webp',
    alt: 'Digital Contents',
    width: 169,
    height: 99,
  },
  ...[
    {
      id: ITEM_TYPE.DIGITAL_GACHA.value,
      itemType: ITEM_TYPE.DIGITAL_GACHA.str,
      itemTypeValue: ITEM_TYPE.DIGITAL_GACHA.value,
      title: 'デジタルガチャ',
      description: '画像/動画/音声をガチャで販売',
      contents: ['image', 'audio', 'video'],
      src: '/shop/images/mockDigitalGacha.webp',
      alt: 'Digital Gacha',
      width: 120,
      height: 120,
    },
  ],
  ...(isChekiExhibitable
    ? [
        {
          id: ITEM_TYPE.CHEKI.value,
          itemType: ITEM_TYPE.CHEKI.str,
          itemTypeValue: ITEM_TYPE.CHEKI.value,
          title: 'チェキ風ブロマイド',
          description: '画像をチェキ風ブロマイドとして販売',
          contents: ['image'],
          src: '/shop/images/mockCheki.webp',
          alt: 'Cheki',
          width: 121,
          height: 112,
        },
      ]
    : []),
  ...(FeatureFlags.realPhoto()
    ? [
        {
          id: ITEM_TYPE.REAL_PHOTO.value,
          itemType: ITEM_TYPE.REAL_PHOTO.str,
          itemTypeValue: ITEM_TYPE.REAL_PHOTO.value,
          title: 'プリント便',
          description: 'L版のブロマイドを販売',
          contents: ['image'],
          src: '/shop/images/mockPrintbin.webp',
          alt: 'Real Photo',
          width: 125,
          height: 96,
        },
      ]
    : []),
  ...(FeatureFlags.printGacha()
    ? [
        {
          id: ITEM_TYPE.PRINT_GACHA.value,
          itemType: ITEM_TYPE.PRINT_GACHA.str,
          itemTypeValue: ITEM_TYPE.PRINT_GACHA.value,
          title: 'プリントガチャ',
          description: 'L版のブロマイドをガチャ形式で販売',
          contents: ['image'],
          src: '/shop/images/mockPrintGacha.webp',
          alt: 'Digital Gacha',
          width: 139,
          height: 117,
        },
      ]
    : []),
];

export const MenuItemCard = ({ isChekiExhibitable, identityId }: MenuItemCardProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateItem = async (itemType: number, identityId: string) => {
    setIsLoading(true);
    const tempItemId = 'new' + Date.now();
    let eventType = 'fanme_listing_digital_bundle_click';
    let url = `/@${identityId}/item/${tempItemId}/create`;

    switch (itemType) {
      case ITEM_TYPE.DIGITAL_GACHA.value:
        eventType = 'fanme_listing_digital_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.DIGITAL_GACHA.str}`;
        break;
      case ITEM_TYPE.CHEKI.value:
        eventType = 'fanme_listing_cheki_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.CHEKI.str}`;
        break;
      case ITEM_TYPE.PRINT_GACHA.value:
        eventType = 'fanme_listing_print_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.PRINT_GACHA.str}`;
        break;
      case ITEM_TYPE.REAL_PHOTO.value:
        eventType = 'fanme_listing_real_photo_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.REAL_PHOTO.str}`;
        break;
      default:
        eventType = 'fanme_listing_digital_bundle_click';
        url = `/@${identityId}/item/${tempItemId}/create`;
        break;
    }

    sendGTMEvent({ event: eventType, shop_id: identityId });
    try {
      router.push(url);
    } finally {
      setIsLoading(false);
    }
  };

  const contentsIcons = (contents: string[]) => {
    return contents.map((content: string, index: number) => {
      return (
        <ShopPublicImage key={index} src={ICONS[content as keyof typeof ICONS]} width={24} height={24} alt={content} />
      );
    });
  };

  return (
    <>
      {isLoading && <LoadingOverlay />}
      <div className="mx-auto max-w-120 space-y-4 overflow-y-auto p-4" style={{ height: 'calc(100vh - 56px)' }}>
        {menuItems(isChekiExhibitable).map((item) => (
          <div
            key={item.id}
            className="relative flex cursor-pointer flex-row items-center justify-between rounded-md bg-white px-4 py-2"
            onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
            style={{
              backgroundImage: `url(${item.src})`,
              backgroundSize: `${item.width}px ${item.height}px`,
              backgroundPosition: 'right 14px center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <div className="max-100:pl-1 flex min-w-0 flex-col items-start gap-2 p-[0.9vw]">
              <div className="text-bold-16 underline decoration-yellow-100 decoration-10 underline-offset-[-4px]">
                {item.title}
              </div>
              <div className="flex-nowrap text-medium-11">{item.description}</div>
              <div className="flex w-28 justify-center gap-1">{contentsIcons(item.contents)}</div>
              <button
                onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
                className="h-8 w-28 rounded-2xl bg-black text-[12px] font-bold text-white"
              >
                出品する
              </button>
            </div>
          </div>
        ))}
        {/*物販販売の問い合わせリンク*/}
        <div className="relative mx-auto max-w-120 rounded-md bg-white p-4 shadow-md">
          <div className="max-100:pl-1 flex min-w-0 flex-col items-start gap-2 p-[0.9vw]">
            <div className="text-bold-16">
              <span className="underline decoration-yellow-100 decoration-10 underline-offset-[-4px]">
                チェキ風写真、アクリル商品等を
                <br />
                販売したい方はこちら
              </span>
            </div>
            <div className="text-medium-11">
              公式LINEからお問い合わせください。
              <br />
              <a href="https://media.fanme.link/2764/" target="_blank" className="text-blue-500 underline">
                物販のはじめ方
              </a>
              も合わせてご確認ください。
            </div>
            <button
              className="h-8 w-40 rounded-2xl bg-green-400 text-[12px] font-bold text-white"
              onClick={() => {
                const lineUrl = isIphoneChrome()
                  ? `line://ti/p/${LINE_CONTACT_ID}`
                  : `https://line.me/ti/p/${LINE_CONTACT_ID}`;
                window.open(lineUrl, '_blank');
              }}
            >
              お問い合わせはコチラ
            </button>
            <div className="text-medium-11 text-orange-400">※公式LINEが開きます</div>
          </div>
          <div className="absolute right-2 top-12 gap-4">
            <ShopPublicImage src="/images/physicalItemSamples.webp" alt="物販商品サンプル" width={99} height={120} />
          </div>
        </div>
      </div>
    </>
  );
};

export default MenuItemCard;
