'use client';

import React, { useMemo, memo } from 'react';
import { Control } from 'react-hook-form';
import Button from '@/components/atoms/button';
import SelectInput from '@/components/atoms/inputs/select-input';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import UploadedFile from '@/components/containers/UploadedFile';
import UploadFile from '@/components/containers/UploadFile';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { type ConditionType, CONDITION_TYPE, type GachaBenefitFile } from '@/types/gacha';
import { ItemFiles } from '@/types/shopItem';

interface BenefitItemProps {
  benefitId: number;
  index: number;
  condition: ConditionType;
  description: string;
  benefitFiles: GachaBenefitFile[] | null;
  uploadProgress: number;
  availableConditions: { value: ConditionType; label: string }[];
  textLength: number;
  canDelete: boolean;
  isLastItem: boolean;
  control: Control<any>;
  shopLimitation: ShopLimitation;
  onConditionChange: (benefitId: number, condition: ConditionType) => void;
  onDescriptionChange: (benefitId: number, description: string) => void;
  onFileUpload: (benefitId: number, files: ItemFiles) => void;
  onFileDelete: (benefitId: number) => void;
  onDelete: (benefitId: number) => void;
  onUploadProgress: (benefitId: number, progress: number) => void;
  isEdit?: boolean;
  itemFilesCount?: number;
  isDuplicated?: boolean;
}

const DELETE_BUTTON_STYLE = 'w-24 h-8 text-medium-12 gap-1 text-red-600';

const BenefitItem: React.FC<BenefitItemProps> = ({
  benefitId,
  index,
  condition,
  description,
  benefitFiles,
  uploadProgress,
  availableConditions,
  textLength,
  canDelete,
  isLastItem,
  control,
  shopLimitation,
  onConditionChange,
  onDescriptionChange,
  onFileUpload,
  onFileDelete,
  onDelete,
  onUploadProgress,
  isEdit = false,
}) => {
  const benefitItemSrc = useMemo(() => {
    if (benefitFiles?.[0]?.type === 'image' || benefitFiles?.[0]?.type === 'video') {
      return benefitFiles[0]?.thumbnail || '';
    } else if (benefitFiles?.[0]?.type === 'audio') {
      return '/shop/images/voice.png';
    }
    return '';
  }, [benefitFiles]);

  const isFileLoading = useMemo(() => {
    return benefitFiles?.[0]?.isLoading || false;
  }, [benefitFiles]);

  const handleEdit = () => {
    onFileDelete(benefitId);
  };

  return (
    <div className={`${isLastItem ? '' : 'section-double-border'} py-4`}>
      <div>
        <div className="mb-5 flex items-center justify-start gap-2">
          <div className="w-18 rounded-lg bg-secondary text-center text-medium-12 leading-8 text-white">
            特典条件 {index + 1}
          </div>

          <SelectInput
            control={control}
            inputName={`benefits.${benefitId}.condition`}
            options={availableConditions}
            required
            className="!mb-0 flex-1"
            onChange={(value) => onConditionChange(benefitId, value as ConditionType)}
            defaultValue={condition ? condition.toString() : undefined}
            disabled={isEdit}
          />
        </div>

        <div className="mb-4 flex items-center justify-center">
          {benefitFiles && benefitFiles.length > 0 ? (
            <UploadedFile
              id={benefitId.toString()}
              src={benefitItemSrc}
              type={benefitFiles[0]?.type || 'image'}
              setThumbImage={() => {}}
              handleDelete={!isEdit ? handleEdit : undefined}
              progress={uploadProgress}
              uploadType="benefit"
              showTitle={false}
              title={benefitFiles[0]?.title || ''}
              conditionType={condition || CONDITION_TYPE.TIMES_10}
              isLoading={isFileLoading}
            />
          ) : (
            <UploadFile
              setFiles={(files) => onFileUpload(benefitId, files)}
              shopLimitation={shopLimitation}
              isPublic={false}
              onProgress={(_, progress) => onUploadProgress(benefitId, progress)}
            />
          )}
        </div>

        <TextAreaInput
          control={control}
          inputName={`benefits.${benefitId}.description`}
          labelTitle="特典内容"
          placeholder="特典内容を入力してください"
          maxLength={30}
          textLength={textLength}
          defaultValue={description}
          onBlur={(description) => onDescriptionChange(benefitId, description || '')}
          disabled={isEdit}
        />

        {canDelete && !isEdit && (
          <div className="mt-4 flex justify-center">
            <Button
              buttonType="light-shadow"
              buttonSize="free"
              onClick={() => onDelete(benefitId)}
              buttonClassNames={DELETE_BUTTON_STYLE}
              className="h-8"
            >
              <ShopPublicImage src="/images/icons/Delete-error.svg" width={16} height={16} alt="delete" />
              <span className="text-medium-12">特典を削除</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(BenefitItem);
