'use client';

import { useMemo } from 'react';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import WarningMessage from '@/components/atoms/typography/warning-message';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import BenefitItem from './benefit-item';
import { MAX_UPLOAD_GACHA_BENEFITS } from '@/consts/inputLength';
import { useGachaBenefit } from '@/hooks/use-gacha-benefit';
import { ExhibitGachaItem } from '@/types/exhibitItem';
import { ExhibitType } from '@/types/shopItem';

type GachaBenefitProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
  itemType?: ExhibitType;
};

const MODAL_CONFIG = {
  header: '購入特典の設定',
  imageSrc: '/images/gacha/benefitgacha.webp',
  content:
    '購入特典の条件と特典を登録でき、購入者が条件を満たすと受け取れます。特典を付けると、売上金額が上がりやすくなりますので、上手に活用しましょう。',
} as const;

const ADD_BUTTON_STYLE = 'w-30 h-8 text-medium-12 gap-2';

const GachaBenefit = ({ numbering, shopLimitation, isEdit, itemType }: GachaBenefitProps) => {
  const params = useParams();
  const itemId = params.id as string;

  const {
    benefitItems,
    textLengths,
    control,
    handleConditionChange,
    handleDescriptionChange,
    handleFileUpload,
    handleFileDelete,
    handleUploadProgress,
    deleteBenefit,
    addBenefitManually,
    getAvailableConditions,
  } = useGachaBenefit({ itemType });

  const useExhibits = useExhibitsStore();
  const { exhibits } = useExhibits;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const itemFilesCount = (exhibitItem as ExhibitGachaItem)?.itemFiles?.length;
  const isDuplicated = (exhibitItem as ExhibitGachaItem)?.isDuplicated;
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    const popupContent = (
      <div className="flex items-center justify-center gap-1 bg-navy-50 py-5">
        <ShopPublicImage src={MODAL_CONFIG.imageSrc} width={241} height={168} alt="modal" />
      </div>
    );

    openInstructionModal(MODAL_CONFIG.header, popupContent, MODAL_CONFIG.content);
  };

  const canAddBenefit = benefitItems.length < MAX_UPLOAD_GACHA_BENEFITS;

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center justify-start gap-2">
            <SectionTitleWithNumber title="ガチャ特典の設定" numbering={numbering} className="!mb-0" />
            <WarningMessage message="出品後は変更できません" />
          </div>
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>

        {/* 特典項目一覧 */}
        <div className="space-y-6">
          {benefitItems.map((benefitItem, index) => {
            const isShowBenefitItem = isEdit ? benefitItem.benefitFiles && benefitItem.benefitFiles.length > 0 : true;
            if (!isShowBenefitItem) return null;
            return (
              <BenefitItem
                key={`benefit-${benefitItem.id}-${index}`}
                benefitId={benefitItem.id || 0}
                index={index}
                condition={benefitItem.conditionType}
                description={benefitItem.description || ''}
                benefitFiles={benefitItem.benefitFiles}
                uploadProgress={benefitItem.uploadProgress}
                availableConditions={getAvailableConditions(benefitItem.id || 0)}
                textLength={textLengths[benefitItem.id || 0] || 0}
                canDelete={benefitItems.length > 1}
                isLastItem={index === benefitItems.length - 1}
                control={control}
                shopLimitation={shopLimitation}
                onConditionChange={handleConditionChange}
                onDescriptionChange={handleDescriptionChange}
                onFileUpload={handleFileUpload}
                onFileDelete={handleFileDelete}
                onDelete={deleteBenefit}
                onUploadProgress={handleUploadProgress}
                isEdit={isEdit}
                itemFilesCount={itemFilesCount}
                isDuplicated={isDuplicated}
              />
            );
          })}
        </div>
        {canAddBenefit && !isEdit && (
          <div className="mt-2 flex justify-center">
            <Button
              buttonType="light-shadow"
              buttonSize="free"
              onClick={addBenefitManually}
              buttonClassNames={ADD_BUTTON_STYLE}
              className="h-8"
            >
              <ShopPublicImage src="/images/icons/PlusCircle.svg" width={22} height={22} alt="追加" />
              <span className="text-medium-12">特典を追加</span>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default GachaBenefit;
