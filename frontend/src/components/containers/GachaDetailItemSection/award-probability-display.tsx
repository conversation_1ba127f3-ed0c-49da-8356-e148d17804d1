'use client';
import React from 'react';
import ShopPublicImage from '@/components/ShopImage';
import { getRaritySrc } from '@/utils/gacha-helpers';
import { Award } from '@/types/gacha';

type AwardProbabilityDisplayProps = {
  awardType: Award;
  probability?: number;
  iconSize?: { width: number; height: number };
  className?: string;
  isShowProbability?: boolean;
};

/**
 * 賞の確率表示用の共通コンポーネント
 * S賞、A賞、B賞、C賞の確率表示に使用
 */
const AwardProbabilityDisplay: React.FC<AwardProbabilityDisplayProps> = ({
  awardType,
  probability,
  iconSize = { width: 66, height: 40 },
  className = '',
  isShowProbability = true,
}) => {
  return (
    <div className={`flex items-center justify-start gap-4 ${className}`}>
      <ShopPublicImage
        src={getRaritySrc(awardType)}
        alt={`Rarity ${awardType}`}
        width={iconSize.width}
        height={iconSize.height}
      />
      {isShowProbability && !!probability && (
        <div className="flex items-center text-gray-800">
          <span className="mr-0.5 text-medium-13">確率</span>
          <span className="text-medium-20">{probability}</span>
          <span className="text-medium-15">%</span>
        </div>
      )}
    </div>
  );
};

export default AwardProbabilityDisplay;
