'use client';
import { useRef } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import SwiperCore from 'swiper';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import RainbowButton from '@/components/atoms/button/rainbow-button';
import GachaCollection from '@/components/containers/GachaCollection';
import ShopPublicImage from '@/components/ShopImage';
import { FeatureFlags } from '@/lib/feature';
import { revalidateItemDetailCache } from '@/app/actions/revalidateItemDetailCache';
import { getUserIdentityId } from '@/utils/base';
import { GachaItemFile, AWARD_TYPE } from '@/types/gacha';

import 'swiper/css';
import 'swiper/css/navigation';

interface ResultImage extends GachaItemFile {
  bgUrl: string;
  icon: string;
}
type ResultPreviewProps = {
  pulledFiles: GachaItemFile[];
  selectedAwardFileId?: string | null;
  isPrintGacha?: boolean;
};
const getResultImage = (itemFile: GachaItemFile): ResultImage => {
  const resultBg = ['awardcbg', 'awardbbg', 'awardabg', 'awardsbg'];
  const resultIcon = ['getC', 'getB', 'getA', 'getS'];
  const award = itemFile.awardType || AWARD_TYPE.S;
  const itemFileWithBg = {
    ...itemFile,
    bgUrl: `/shop/images/gacha/${resultBg[award - 1]}.webp`,
    icon: resultIcon[award - 1],
  };
  return itemFileWithBg;
};

const GachaResultPreview = ({ props }: { props: ResultPreviewProps }) => {
  const router = useRouter();
  const params = useParams();
  const { pulledFiles, selectedAwardFileId } = props;
  const resultImages = pulledFiles.map((item) => getResultImage(item));
  const identityId = getUserIdentityId(params.identityId as string);
  const swiperRef = useRef<SwiperCore>();

  const handleGoToGachaDetail = async () => {
    await revalidateItemDetailCache(params.id as string);
    router.push(`/@${identityId}/item/${params.id}`);
  };

  // 特定のインデックスにスライドする関数
  const slideTo = (index: number) => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(index);
    }
  };

  // selectedAwardFileIdに該当するindexを初期スライドに設定
  const initialSlide = selectedAwardFileId ? resultImages.findIndex((item) => item.id === selectedAwardFileId) : 0;

  return (
    <div className="pb-10">
      <div className="relative w-full">
        <Swiper
          effect="fade"
          modules={[Navigation]}
          navigation={true}
          className="gacha-review-swiper h-160"
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          initialSlide={initialSlide}
        >
          {resultImages.map((item) => (
            <SwiperSlide key={item.id}>
              <div
                className="relative flex aspect-video h-160 w-full items-start justify-center pt-5"
                style={{ backgroundImage: `url(${item.bgUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
              >
                <div className="flex flex-col items-center">
                  <ShopPublicImage
                    src={`/images/gacha/${item.icon}.webp`}
                    alt={item.title || 'ガチャ結果'}
                    width={287}
                    height={64}
                    className="mb-3"
                  />
                  <div className="flex h-122 w-full max-w-86 justify-center">
                    <img
                      src={
                        FeatureFlags.printGacha() && props.isPrintGacha ? item.watermarkThumbnailUri! : item.thumbnail!
                      }
                      alt={item.title || 'ガチャ結果'}
                      loading="lazy"
                      className="object-contain"
                      width={344}
                      height={488}
                    />
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        {/* scroll icon */}
        <div className="absolute bottom-1 left-1/2 z-10 flex -translate-x-1/2 flex-col items-center">
          <div className="relative mb-3">
            {/* 半透明線 */}
            <div className="absolute left-0 h-6 w-px bg-white opacity-30"></div>
            {/* メイン線 */}
            <div className="relative h-3 w-px bg-white">
              <div className="scroll-ball absolute left-1/2 top-0 size-1.25 -translate-x-1/2 rounded-full bg-white"></div>
            </div>
          </div>
          <div className="mt-1 text-sm text-white">SCROLL</div>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center pt-7.5">
        <ShopPublicImage src="/images/result.webp" alt="ガチャ結果" width={250} height={34} className="mb-3" />
        <GachaCollection
          isPreview={false}
          itemFiles={pulledFiles}
          hasBg={false}
          onItemClick={slideTo}
          isPrintGacha={props.isPrintGacha}
        />
        <RainbowButton onClick={handleGoToGachaDetail}>商品詳細はこちら</RainbowButton>
      </div>
    </div>
  );
};

export default GachaResultPreview;
