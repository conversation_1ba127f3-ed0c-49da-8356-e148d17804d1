'use client';
import { useState, useCallback, ReactNode, useMemo, useEffect, memo } from 'react';
import { toast } from 'react-hot-toast';
import { DragEndEvent, DragStartEvent, MouseSensor, TouchSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import clsx from 'clsx';
import GoodsIcon from '@/components/atoms/badges/goods-icon';
import CustomToast from '@/components/atoms/toast/custom-toast';
import ShopPublicImage from '@/components/ShopImage';
import { roboto } from '@/app/fonts';
import { createMoveValidityMatrix } from '@/utils/gacha-validation-rules';
import type { GachaItemFile, Award } from '@/types/gacha';
import { AWARD_TYPE } from '@/types/gacha';

// Helper function to get ordered awards
const getOrderedAwards = (): Award[] => [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C]; // [4, 3, 2, 1]

// SortableItem component for drag-and-drop functionality
interface SortableItemProps {
  id: string;
  children: ReactNode;
}

// Memoize the SortableItem component to prevent unnecessary re-renders
export const SortableItem = memo(({ id, children }: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    // Add a higher z-index when dragging to ensure it appears above other items
    zIndex: isDragging ? 999 : 'auto',
    // Add a subtle scale effect when dragging
    opacity: isDragging ? 0.9 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </div>
  );
});

// Add display name for React DevTools
SortableItem.displayName = 'SortableItem';

// DragOverlayコンテンツを取得する関数
export const getDragOverlayContent = (id: string, sortableItems: GachaItemFile[]) => {
  const item = sortableItems.find((item) => item.id === id);
  if (!item) return null;

  const src =
    item.type === 'audio'
      ? '/shop/images/voice.png'
      : item.type === 'video'
        ? item.thumbnail
        : item.src || item.thumbnail;

  return (
    <div className="relative">
      <div className="h-29 w-full bg-contain bg-center bg-no-repeat">
        <div className="flex h-full w-full items-center justify-center">
          {item.receivedFileCount && item.receivedFileCount > 0 ? (
            <img
              src={src || ''}
              alt={item.title || ''}
              width={110}
              height={110}
              className="size-27.5 rounded-sm object-cover"
            />
          ) : null}
          <GoodsIcon type={item.type || 'image'} mode="dark" className="absolute bottom-2.5 left-2.5" />
          {item.receivedFileCount && item.receivedFileCount > 1 && (
            <span
              className={clsx(
                'absolute bottom-2.5 right-2.5 flex h-3.5 w-7 items-center justify-center rounded-lg bg-white text-medium-11 text-secondary',
                roboto.className,
              )}
            >
              x {item.receivedFileCount}
            </span>
          )}
          {item.isNew && (
            <span className="absolute left-1.5 top-1.5 flex h-3.5 w-10">
              <ShopPublicImage src="/images/icons/newicon.svg" alt="new" width={39} height={12} />
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

interface UseDraggableGachaParams {
  items: GachaItemFile[];
  onDragComplete: (updatedItems: GachaItemFile[]) => void;
}

export const useDraggableGacha = ({ items, onDragComplete }: UseDraggableGachaParams) => {
  const [sortableItems, setSortableItems] = useState<GachaItemFile[]>(items);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Effect to update sortable items when input items change
  useEffect(() => {
    setSortableItems(items);
  }, [items]);

  // Setup sensors for drag and drop with optimized settings
  const mouseSensor = useSensor(MouseSensor, {
    // Increase the activation distance to reduce accidental drags
    activationConstraint: {
      distance: 10, // 10px movement required to start drag
    },
  });

  const touchSensor = useSensor(TouchSensor, {
    // Optimize for touch devices
    activationConstraint: {
      delay: 150, // Reduced delay for better responsiveness
      tolerance: 8, // Increased tolerance for better detection
    },
  });

  const sensors = useSensors(mouseSensor, touchSensor);

  // Handle drag complete - moved up before handleDragEnd
  const handleComplete = useCallback(() => {
    onDragComplete(sortableItems);
  }, [sortableItems, onDragComplete]);

  // Memoize the drag start handler
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    setIsDragging(true);
  }, []);

  // コンテナ間のドラッグを許可するための関数
  const findContainer = useCallback(
    (id: string): Award | null => {
      if (!id) return null;

      // idがdroppable-で始まる場合は直接コンテナ（賞種）を返す
      if (id.startsWith('droppable-')) {
        const awardType = parseInt(id.split('-')[1]);
        return isNaN(awardType) ? null : (awardType as Award);
      }

      // アイテムIDの場合、そのアイテムが所属するコンテナ（賞種）を返す
      const item = sortableItems.find((item) => item.id === id);
      return item?.awardType || null;
    },
    [sortableItems],
  );

  // 移動ルールの定義（より簡潔に）- useMemoでラップ
  const moveValidityMatrix = useMemo(() => createMoveValidityMatrix(), []);

  // Helper function to convert items to award buckets after a potential move
  const convertToAwardBuckets = useCallback(
    (currentGachaItems: GachaItemFile[], itemIdToMove: string, newAwardType: Award): GachaItemFile[][] => {
      const orderedAwards = getOrderedAwards();
      const itemsAfterMove = currentGachaItems.map((item) =>
        item.id === itemIdToMove ? { ...item, awardType: newAwardType } : item,
      );

      const buckets: GachaItemFile[][] = orderedAwards.map(() => []);
      for (const item of itemsAfterMove) {
        if (item.awardType !== undefined) {
          const awardIndex = orderedAwards.indexOf(item.awardType as Award);
          if (awardIndex !== -1) {
            buckets[awardIndex].push(item);
          }
        }
      }
      return buckets;
    },
    [],
  );

  const isValidAwardDistribution = useCallback(
    (awardBuckets: GachaItemFile[][]): { valid: boolean; message?: string } => {
      const orderedAwards = getOrderedAwards();
      for (let i = 0; i < awardBuckets.length - 1; i++) {
        if (awardBuckets[i].length === 0) {
          let subsequentAwardHasItems = false;
          for (let j = i + 1; j < awardBuckets.length; j++) {
            if (awardBuckets[j].length > 0) {
              subsequentAwardHasItems = true;
              break;
            }
          }
          if (subsequentAwardHasItems) {
            const awardValue = orderedAwards[i] as number;
            let awardKey: keyof typeof AWARD_TYPE | undefined;

            switch (awardValue) {
              case AWARD_TYPE.S: // 4
                awardKey = 'S';
                break;
              case AWARD_TYPE.A: // 3
                awardKey = 'A';
                break;
              case AWARD_TYPE.B: // 2
                awardKey = 'B';
                break;
              case AWARD_TYPE.C: // 1
                awardKey = 'C';
                break;
            }
            const finalEmptyAwardName = awardKey || '不明な';
            return {
              valid: false,
              message: `${finalEmptyAwardName}賞を空にすることはできません。下位の賞に景品を移動してください。`,
            };
          }
        }
      }
      return { valid: true };
    },
    [],
  );

  // 共通の移動処理関数
  const processMove = useCallback(
    (sourceId: string, targetAwardType: Award, customHandler?: (items: GachaItemFile[]) => GachaItemFile[]) => {
      const sourceItem = sortableItems.find((item) => item.id === sourceId);
      if (!sourceItem || !sourceItem.awardType) {
        toast.custom((t) => CustomToast(t, 'error', 'アイテムが見つかりません'));
        return false;
      }

      const sourceAwardType = sourceItem.awardType as Award;

      // 基本的な移動ルールのチェック
      const validityCheck = moveValidityMatrix[sourceAwardType][targetAwardType](sortableItems, sourceId);
      if (!validityCheck.valid) {
        toast.custom((t) => CustomToast(t, 'error', validityCheck.message));
        return false;
      }

      // 新しい分布チェックロジック
      const awardBucketsAfterMove = convertToAwardBuckets(sortableItems, sourceId, targetAwardType);
      const distributionCheck = isValidAwardDistribution(awardBucketsAfterMove);

      if (!distributionCheck.valid) {
        toast.custom((t) => CustomToast(t, 'error', distributionCheck.message || '景品の配置が無効です。'));
        return false;
      }

      // 移動が有効な場合は処理を実行
      setSortableItems((items) => {
        let newItems;
        if (customHandler) {
          newItems = customHandler(items);
        } else {
          const oldIndex = items.findIndex((item) => item.id === sourceId);
          if (oldIndex === -1) return items;

          // アイテムの賞種類を更新
          newItems = [...items];
          newItems[oldIndex] = {
            ...newItems[oldIndex],
            awardType: targetAwardType,
          };
        }

        // 状態更新後に親コンポーネントに通知
        onDragComplete(newItems);
        return newItems;
      });

      return true;
    },
    [sortableItems, moveValidityMatrix, onDragComplete, convertToAwardBuckets, isValidAwardDistribution],
  );

  // Memoize the drag end handler
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      setActiveId(null);
      setIsDragging(false);

      if (!over) return;

      // コンテナの検出
      const activeContainer = findContainer(active.id as string);
      const overContainer = findContainer(over.id as string);

      if (!activeContainer || !overContainer) return;

      // 同じコンテナ内での移動
      if (activeContainer === overContainer) {
        const oldIndex = sortableItems.findIndex((item) => item.id === active.id);
        const newIndex = sortableItems.findIndex((item) => item.id === over.id);

        if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
          setSortableItems((prevItems) => {
            const newItems = arrayMove(prevItems, oldIndex, newIndex);
            // ステート更新後に親コンポーネントに通知
            onDragComplete(newItems);
            return newItems;
          });
        }
        return;
      }

      // 異なるコンテナへの移動
      if (processMove(active.id as string, overContainer)) {
        // processMove成功時のみhandleCompleteを呼び出す
        handleComplete();
      }
    },
    [sortableItems, processMove, handleComplete, onDragComplete, findContainer],
  );

  return {
    sortableItems,
    isDragging,
    activeId,
    handleDragStart,
    handleDragEnd,
    handleComplete,
    sensors,
  };
};
