'use client';
import React from 'react';
import clsx from 'clsx';
import WarningMessage from '@/components/atoms/typography/warning-message';
import ShopPublicImage from '@/components/ShopImage';
import { roboto } from '@/app/fonts';
import { MAX_GACHA_ITEM_COUNT, LEAST_ITEM_COUNT } from '@/consts/sizes';
import type { GachaItemFile } from '@/types/gacha';
import { AWARD_TYPE } from '@/types/gacha';

type GachaTotalCountProps = {
  itemFiles: GachaItemFile[];
  hasEnoughItems: boolean;
  totalGachaCountSectionRef: React.RefObject<HTMLDivElement>;
};

const GachaTotalCount = ({ itemFiles, hasEnoughItems, totalGachaCountSectionRef }: GachaTotalCountProps) => {
  // アクティブな賞の数を計算
  const activeAwardCount =
    itemFiles.length > 0
      ? Object.values(AWARD_TYPE).filter((awardType) => itemFiles.some((file) => file.awardType === awardType)).length
      : 0;

  return (
    <div className="px-4 pb-4" ref={totalGachaCountSectionRef}>
      <div className="mb-2 mt-4 flex items-center gap-2">
        <ShopPublicImage src="/images/gacha/icons/Gacha.svg" width={20} height={20} alt="gacha" />
        <h5 className="text-medium-15">登録したガチャの合計数</h5>
      </div>
      <p className="mb-2 text-medium-13">
        合計{LEAST_ITEM_COUNT[activeAwardCount as keyof typeof LEAST_ITEM_COUNT] || 1}点以上の登録が必要です。
      </p>
      <div
        className={clsx(
          roboto.className,
          'flex h-8 items-center justify-center rounded-md bg-white text-secondary',
          !hasEnoughItems && 'border border-error text-error',
        )}
      >
        <span className={clsx('text-medium-20', hasEnoughItems ? 'text-secondary' : 'text-error')}>
          {itemFiles.length}
        </span>
        <span className="text-regular-14">/{MAX_GACHA_ITEM_COUNT}</span>
      </div>
      <div className="mt-2 flex w-full justify-center">
        <WarningMessage message="出品後は変更できません" />
      </div>
    </div>
  );
};

export default GachaTotalCount;
