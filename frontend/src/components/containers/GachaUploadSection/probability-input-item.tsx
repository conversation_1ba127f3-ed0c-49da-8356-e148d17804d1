'use client';
import React from 'react';
import { Control, Path, useWatch } from 'react-hook-form';
import clsx from 'clsx';
import NumberInput from '@/components/atoms/inputs/number-input';
import ShopPublicImage from '@/components/ShopImage';
import { awardLabels } from '@/utils/gacha-validation-rules';
import { Award } from '@/types/gacha';

// 確率入力フォームの型
export type ProbabilityFormValues = {
  probability4: number;
  probability3: number;
  probability2: number;
  probability1: number;
};

// 確率入力コンポーネントのProps
interface ProbabilityInputItemProps {
  award: Award;
  isShowProbability: boolean;
  probability: number;
  control: Control<ProbabilityFormValues>;
  onBlur: (value?: number) => void;
  onChange?: (value: number) => void;
  minValue?: number;
  disabled?: boolean;
}

const ProbabilityInputItem: React.FC<ProbabilityInputItemProps> = ({
  award,
  isShowProbability,
  probability,
  control,
  onBlur,
  onChange,
  minValue = 0,
  disabled = false,
}) => {
  const inputName = `probability${award}` as Path<ProbabilityFormValues>;
  const currentValue = useWatch({
    control,
    name: inputName,
    defaultValue: isShowProbability ? probability : 0,
  });

  // 値が変更された時のハンドラー
  const handleBlur = () => {
    // ブラー時に最新の値をコンポーネント内部で保持
    if (onChange) {
      onChange(currentValue);
    }
    onBlur(currentValue);
  };

  // 実際に無効化するかどうか
  const isDisabled = !isShowProbability || disabled;
  return (
    <div className="flex items-center justify-between opacity-100">
      <ShopPublicImage src={awardLabels[award].icon} width={54} height={32} alt={awardLabels[award].label} />
      <div className="flex items-center gap-2">
        <span className="text-medium-13">確率</span>
        {isShowProbability ? (
          <NumberInput
            control={control}
            inputName={inputName}
            inputClassName={clsx('!w-18 !justify-center text-medium-20', isDisabled && 'cursor-not-allowed')}
            min={minValue}
            max={100}
            className={clsx('!mb-0', isDisabled && 'pointer-events-none')}
            shortInput
            onBlur={handleBlur}
            coverValue={probability || 0}
            errorMsgClassName="hidden"
            step={1}
            disabled={isDisabled}
          />
        ) : (
          <div className="flex h-8 w-18 items-center justify-center rounded-lg bg-gray-200 text-center text-medium-13">
            -
          </div>
        )}
        <span className="text-medium-13">%</span>
      </div>
    </div>
  );
};

export default ProbabilityInputItem;
