'use client';

import CreateBenefitSection from '@/components/containers/CreateBenefitSection';
import ItemMainInfo from '@/components/containers/ItemMainInfo';
import ItemOptionsSection from '@/components/containers/ItemOptionsSection';
import SampleSection from '@/components/containers/SampleSection';
import ThumbnailSection from '@/components/containers/ThumbnailSection';
import UploadItems from '@/components/containers/UploadItems';
import PublicSetSection from '@/components/views/PublicSetSection';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';

const RealPhotoItemForm = ({ shopLimitation, isEdit }: { shopLimitation: ShopLimitation; isEdit?: boolean }) => {
  return (
    <>
      <UploadItems numbering="01" shopLimitation={shopLimitation} />
      <ItemMainInfo numbering="02" />
      <ThumbnailSection numbering="03" />
      <SampleSection numbering="04" shopLimitation={shopLimitation} />
      <CreateBenefitSection numbering="05" shopLimitation={shopLimitation} />
      <ItemOptionsSection numbering="06" isEdit={isEdit} />
      <PublicSetSection numbering="07" />
    </>
  );
};

export default RealPhotoItemForm;
