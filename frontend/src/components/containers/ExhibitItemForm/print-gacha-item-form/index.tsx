'use client';

import GachaBenefit from '@/components/containers/GachaBenefit';
import GachaThumbnailSection from '@/components/containers/GachaThumbnailSection';
import GachaUploadSection from '@/components/containers/GachaUploadSection';
import ItemMainInfo from '@/components/containers/ItemMainInfo';
import ItemOptionsSection from '@/components/containers/ItemOptionsSection';
import SampleSection from '@/components/containers/SampleSection';
import PublicSetSection from '@/components/views/PublicSetSection';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { ITEM_TYPE } from '@/types/item';

const PrintGachaItemForm = ({ shopLimitation, isEdit }: { shopLimitation: ShopLimitation; isEdit?: boolean }) => {
  return (
    <>
      <GachaUploadSection
        numbering="01"
        shopLimitation={shopLimitation}
        isEdit={isEdit}
        itemType={ITEM_TYPE.PRINT_GACHA.str}
      />
      <ItemMainInfo numbering="02" />
      <GachaThumbnailSection numbering="03" />
      <GachaBenefit
        numbering="04"
        shopLimitation={shopLimitation}
        isEdit={isEdit}
        itemType={ITEM_TYPE.PRINT_GACHA.str}
      />
      <SampleSection numbering="05" shopLimitation={shopLimitation} />
      <ItemOptionsSection numbering="06" isEdit={isEdit} />
      <PublicSetSection numbering="07" />
    </>
  );
};

export default PrintGachaItemForm;
