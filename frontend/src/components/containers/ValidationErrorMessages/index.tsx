import { useMemo } from 'react';
import { useParams } from 'next/navigation';
import { useExhibitsStore } from '@/store/useExhibit';
import { isDigitalBundle, isDigitalGacha, isExhibitCheki } from '@/utils/itemTypes';

const ValidationErrorMessages = () => {
  const {
    exhibits,
    validateItemFiles,
    validateTitle,
    validatePriceSet,
    validateThumbnail,
    validatePeriodStart,
    validatePeriodEnd,
    validateLimitedPerUser,
    validateBenefits,
  } = useExhibitsStore();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const isEditableItemType = isDigitalBundle(exhibitItem) || isDigitalGacha(exhibitItem);
  const warningMsgList: string[] = useMemo(() => {
    const newMessages: string[] = [];
    if (exhibitItem) {
      const sectionNo = 1;
      if (isEditableItemType && !validateItemFiles(itemId)) {
        newMessages.push(`※ 0${sectionNo} 商品データを1点以上登録してください。`);
      }
      if (!validateTitle(itemId)) {
        newMessages.push(`※ 0${isEditableItemType ? sectionNo + 1 : sectionNo} 商品タイトルを入力してください。`);
      }
      if (!validatePriceSet(itemId)) {
        newMessages.push(`※ 0${isEditableItemType ? sectionNo + 1 : sectionNo} 商品価格を入力してください。`);
      }
      if (!validateThumbnail(itemId)) {
        newMessages.push(`※ 0${isEditableItemType ? sectionNo + 2 : sectionNo + 1} 商品の表紙を設定してください。`);
      }
      if (!validateBenefits(itemId)) {
        newMessages.push(`※ 0${isEditableItemType ? sectionNo + 3 : sectionNo + 2} 特典画像を登録してください。`);
      }
      if (isExhibitCheki(exhibitItem) && (!validatePeriodStart(itemId) || !validatePeriodEnd(itemId))) {
        newMessages.push(`※ 0${sectionNo + 4} 販売期間を設定してください。`);
      }
      if (isExhibitCheki(exhibitItem) && !validateLimitedPerUser(itemId)) {
        newMessages.push(`※ 0${sectionNo + 4} 最大購入数を設定してください。`);
      }
    }
    return newMessages;
  }, [
    exhibitItem,
    isEditableItemType,
    itemId,
    validateItemFiles,
    validateLimitedPerUser,
    validatePeriodEnd,
    validatePeriodStart,
    validatePriceSet,
    validateThumbnail,
    validateTitle,
    validateBenefits,
  ]);

  return (
    <div className="mb-20 px-6">
      {warningMsgList.map((msg, index) => {
        return (
          <p className="text-regular-14 text-red-500" key={index}>
            {msg}
          </p>
        );
      })}
    </div>
  );
};

export default ValidationErrorMessages;
