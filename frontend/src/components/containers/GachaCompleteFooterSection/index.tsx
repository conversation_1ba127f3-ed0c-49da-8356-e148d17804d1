'use client';
import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';

const GachaCompleteFooterSection = ({ itemId, identityId }: { itemId: number; identityId: string }) => {
  const router = useRouter();
  return (
    <div className="flex flex-col items-center justify-center gap-4 rounded-b-2xl bg-secondary p-4">
      <Button onClick={() => router.push(`/@${identityId}/item/${itemId}`)} buttonType="light" buttonSize="lg">
        ガチャを回しに行く
      </Button>
      <div className="flex items-center justify-center">
        <ShopPublicImage src="/images/icons/zukan.svg" alt="zukan" width={181} height={24} />
      </div>
    </div>
  );
};

export default GachaCompleteFooterSection;
