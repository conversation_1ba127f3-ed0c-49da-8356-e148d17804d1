import { ReactNode } from 'react';
import SquareButton from '@/components/atoms/button/square-button';
type PullButton = {
  topText: ReactNode;
  bottomText: ReactNode;
  times: number;
};
type GachaPullButtonGroupProps = {
  buttons: PullButton[];
  onClick: (count: number) => void;
  disabled: boolean;
  status: 'green' | 'black';
};
const GachaPullButtonGroup = ({ buttons, onClick, disabled, status }: GachaPullButtonGroupProps) => {
  const handlePull = (count: number) => {
    onClick(count);
  };
  return (
    <div className="flex justify-center gap-4 px-4">
      {buttons.map((button, index) => (
        <SquareButton
          topText={button.topText}
          bottomText={button.bottomText}
          onClick={() => handlePull(button.times)}
          disabled={disabled}
          status={status}
          key={index}
        />
      ))}
    </div>
  );
};

export default GachaPullButtonGroup;
