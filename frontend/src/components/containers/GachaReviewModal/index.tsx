'use client';

import { useRef, useEffect, useState, useMemo } from 'react';
import { toast } from 'react-hot-toast';
import clsx from 'clsx';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Swiper as SwiperType } from 'swiper/types';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import CustomToast from '@/components/atoms/toast/custom-toast';
import ShopPublicImage from '@/components/ShopImage';
import { PlayerType, usePlayerStore } from '@/store/usePlayer';
import { useIsLandscape } from '@/hooks/useIsLandscape';
import { fileService } from '@/services/file';
import { GachaItemFile } from '@/types/gacha';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';

type GachaReviewModalProps = {
  isOpen: boolean;
  onClose: () => void;
  initialIndex: number;
  itemFiles: GachaItemFile[];
  itemId: number;
};

const GachaReviewModal = ({ isOpen, onClose, initialIndex, itemFiles, itemId }: GachaReviewModalProps) => {
  const swiperRef = useRef<SwiperType>();
  const { setPlayerProps, onPlayerOpen, isPlayerOpen } = usePlayerStore();
  const isLandscape = useIsLandscape();
  const [showControls, setShowControls] = useState(true);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Filter items that have receivedFileCount > 0
  const filteredItemFiles = itemFiles.filter((item) => item.receivedFileCount);

  // 2枚表示用のスライド配列を作成（横向き用）
  const [evenSlides, oddSlides] = useMemo(() => {
    const even: GachaItemFile[] = [];
    const odd: GachaItemFile[] = [];

    filteredItemFiles.forEach((item, index) => {
      if (index % 2 === 0) {
        even.push(item);
      } else {
        odd.push(item);
      }
    });

    return [even, odd];
  }, [filteredItemFiles]);

  // Create mapping from filtered index to original index
  const filteredToOriginalIndexMap = filteredItemFiles.map((filteredItem) =>
    itemFiles.findIndex((item) => item.id === filteredItem.id),
  );

  // Find the filtered index that corresponds to the original initialIndex
  const filteredInitialIndex = filteredToOriginalIndexMap.findIndex((originalIndex) => originalIndex === initialIndex);
  const safeFilteredInitialIndex = filteredInitialIndex >= 0 ? filteredInitialIndex : 0;

  // Set initial slide when modal opens
  useEffect(() => {
    if (isOpen && swiperRef.current && filteredItemFiles.length > 0) {
      const slideIndex = isLandscape ? Math.floor(safeFilteredInitialIndex / 2) : safeFilteredInitialIndex;
      swiperRef.current.slideTo(slideIndex, 0);
      setCurrentSlideIndex(safeFilteredInitialIndex);
    }
  }, [isOpen, safeFilteredInitialIndex, filteredItemFiles.length, initialIndex, isLandscape]);

  // Resume swiper autoplay when player is closed
  useEffect(() => {
    if (!isPlayerOpen && swiperRef.current) {
      swiperRef.current.autoplay?.start();
    }
  }, [isPlayerOpen]);

  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      window.addEventListener('keydown', handleEscKey);
    }

    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // If no items have receivedFileCount > 0, don't render the modal
  if (filteredItemFiles.length === 0) return null;

  const handleClick = (item: GachaItemFile) => {
    if (!item) return;
    if (item.type === 'image') return;

    onPlayerOpen();
    setPlayerProps({
      src: item.src!,
      thumbnail: item.thumbnail!,
      type: item.type as PlayerType,
    });

    if (swiperRef.current) {
      swiperRef.current.autoplay?.stop();
    }
  };

  const handleDownloadFile = async () => {
    const currentFilteredIndex = swiperRef.current?.activeIndex || 0;
    const file = filteredItemFiles[currentFilteredIndex];
    if (!file || !file.receivedFileCount) return;
    try {
      const downloadUrls = await fileService.getDownloadUrl({
        itemId,
        fileData: [
          {
            item: file,
            name: fileService.getValidFileNameForDownload(file.title, file.type!),
          },
        ],
      });

      downloadUrls.forEach((downloadItem) => {
        fileService.downloadByLink(downloadItem.url);
      });
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.custom((t) => CustomToast(t, 'error', 'ダウンロードに失敗しました'), {
        id: 'download-failed',
      });
    }
  };

  const toggleControls = () => {
    setShowControls((prev) => !prev);
  };

  // 枚数表示のロジック
  const getDisplayIndex = () => {
    return isLandscape
      ? filteredItemFiles.length > 1 &&
        filteredItemFiles.length % 2 === 1 &&
        currentSlideIndex < filteredItemFiles.length - 1
        ? `${currentSlideIndex + 1}-${currentSlideIndex + 2}`
        : `${currentSlideIndex + 1}`
      : currentSlideIndex + 1;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center" role="dialog" tabIndex={-1}>
      {/* 背景画像 */}
      <div className="fixed inset-0 z-20 m-auto max-w-120 bg-[url('/shop/images/gacha/zukanbg.webp')] bg-center bg-repeat"></div>

      {/* コンテンツ部分 */}
      <div className="relative z-40 flex size-full max-w-120 flex-col">
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full" onClick={(e) => e.stopPropagation()}>
            <Swiper
              modules={[Navigation]}
              navigation={{
                enabled: true,
              }}
              className="gacha-review-swiper"
              initialSlide={safeFilteredInitialIndex}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
              }}
              onSlideChange={(swiper) => {
                // 横向きの場合は2枚表示なので、インデックスを2倍にする
                setCurrentSlideIndex(isLandscape ? swiper.activeIndex * 2 : swiper.activeIndex);
              }}
            >
              {isLandscape
                ? // 横向き：2枚表示
                  evenSlides.map((_, index) => {
                    const leftItem = evenSlides[index];
                    const rightItem = oddSlides[index];
                    return (
                      <SwiperSlide key={`pair-${index}`}>
                        <div
                          className="relative flex h-screen flex-col items-center justify-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleControls();
                          }}
                        >
                          <div className="absolute inset-0 z-30" onClick={onClose}></div>

                          <div className="relative z-40 flex size-full">
                            {/* 左側の画像 */}
                            <div className="absolute left-0 top-0 h-full w-[calc(50%)]">
                              <img
                                src={leftItem.thumbnail!}
                                alt={leftItem.title || 'Left item'}
                                className="size-full object-contain object-right"
                              />
                            </div>

                            {/* 右側の画像 */}
                            <div className="absolute right-0 top-0 h-full w-[calc(50%)]">
                              {rightItem && (
                                <img
                                  src={rightItem.thumbnail!}
                                  alt={rightItem.title || 'Right item'}
                                  className="size-full object-contain object-left"
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </SwiperSlide>
                    );
                  })
                : // 縦向き：1枚表示
                  filteredItemFiles.map((item, index) => (
                    <SwiperSlide key={item.id || index}>
                      <div
                        className="relative flex h-screen flex-col items-center justify-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClick(item);
                        }}
                      >
                        <div className="absolute inset-0 z-30" onClick={onClose}></div>

                        <div className="relative z-40 flex w-full max-w-86 flex-col justify-center">
                          <div className="relative flex max-h-[80vh] items-center justify-center">
                            <img
                              src={item.thumbnail!}
                              alt={item.title || `Item ${index + 1}`}
                              width={500}
                              height={500}
                              className="max-h-[80vh] w-auto object-contain"
                            />
                            {item.type && item.type !== 'image' && (
                              <div className="absolute inset-0 grid place-content-center">
                                <OutlinedButton
                                  buttonColor="gray"
                                  buttonShape="circle"
                                  buttonType="play"
                                  buttonSize="md"
                                  className="absolute inset-0 m-auto"
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
            </Swiper>

            {/* コントロール要素 */}
            <>
              {/* 枚数表示 */}
              <div
                className={clsx(
                  'absolute left-3 top-4 z-50',
                  'transition-opacity duration-300',
                  showControls ? 'opacity-100' : 'opacity-0',
                )}
              >
                <div className="text-white">
                  <span className="text-bold-17">{getDisplayIndex()}</span>
                  <span className="text-bold-15">{` / ${filteredItemFiles.length}`}</span>
                </div>
              </div>

              {/* 閉じるボタン */}
              <button
                onClick={onClose}
                className={clsx(
                  'absolute right-3 top-4 z-50 flex h-8 w-8 items-center justify-center rounded-full bg-black bg-opacity-50',
                  'transition-opacity duration-300',
                  showControls ? 'opacity-100' : 'opacity-0',
                )}
              >
                <ShopPublicImage
                  src="/images/icons/Close.svg"
                  alt="close"
                  width={16}
                  height={16}
                  className="brightness-0 invert"
                />
              </button>
            </>

            {!isLandscape && (
              <div className="absolute bottom-15 left-0 right-0 flex justify-center">
                <Button
                  buttonType="light"
                  buttonSize="mdPlus"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownloadFile();
                  }}
                  className="z-50"
                >
                  <ShopPublicImage
                    src={'/images/icons/Download_White.svg'}
                    className="brightness-0"
                    alt="download"
                    width={16}
                    height={16}
                  />
                  <span className="text-bold-12">ダウンロード</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GachaReviewModal;
