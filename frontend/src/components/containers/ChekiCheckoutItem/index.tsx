'use client';

import React from 'react';
import FlatItem from '@/components/containers/FlatItem';
import { MediaType } from '@/types/shopItem';

interface IChekiItemProps {
  id: number;
  fileType?: MediaType;
  imageUrl: string;
  title: string;
  available?: boolean;
  price: number;
  currentPrice?: number;
  discountRate?: number;
  thumbnailRatio: number;
  quantity: number;
  purchaserComment?: string;
  isNowOnSale?: boolean;
}

const ChekiCheckoutItem = ({
  id,
  imageUrl,
  title,
  available = true,
  price,
  currentPrice,
  discountRate,
  thumbnailRatio,
  quantity,
  purchaserComment,
  isNowOnSale,
}: IChekiItemProps) => {
  return (
    <div className="flex w-full flex-col">
      <FlatItem
        id={id}
        imageUrl={imageUrl}
        title={title}
        available={available}
        isSet={true}
        price={price}
        currentPrice={currentPrice}
        discountRate={discountRate}
        showRemoveButton={false}
        thumbnailRatio={thumbnailRatio}
        isNowOnSale={isNowOnSale}
        className="mb-3"
      />
      <div className="mb-2 flex w-full gap-3 px-2">
        <div className="flex h-6 w-14 items-center justify-center rounded-2xl bg-white text-medium-11">数量</div>
        <div className="text-medium-18">{quantity}点</div>
      </div>
      <div className="flex w-full gap-3 px-2">
        <div className="flex h-6 w-14 items-center justify-center rounded-2xl bg-white text-medium-11">備考</div>
        <div className="flex-1 whitespace-pre-line text-medium-13">{purchaserComment}</div>
      </div>
    </div>
  );
};

export default ChekiCheckoutItem;
