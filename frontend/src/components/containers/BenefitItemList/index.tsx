'use client';

import React from 'react';
import BenefitItem from '@/components/containers/BenefitItem';
import BenefitItemWithTitle from '@/components/containers/BenefitItemWithTitle';
import { usePlayerStore, PlayerType } from '@/store/usePlayer';
import { GACHA_BENEFIT_CONDITIONS } from '@/consts/gacha-data';
import type { GachaBenefit } from '@/types/gacha';
import type { SingleItem, Benefit } from '@/types/shopItem';

interface BenefitItemListProps<T> {
  items: T[];
  readOnly?: boolean;
  onDownload?: (file: T) => void;
  onFileDownload?: (file: any) => void; // For individual file downloads
  isPreview?: boolean;
}

const BenefitItemList = <T extends Benefit | GachaBenefit>({
  items,
  readOnly = true,
  onFileDownload,
  isPreview = false,
}: BenefitItemListProps<T>) => {
  const { setPlayerProps, onPlayerOpen } = usePlayerStore();
  const handleClick = (id?: string) => {
    if (readOnly) return;
    if (!id) return;

    const item = items.find((item: T) => 'id' in item && item.id?.toString() === id);

    if (item && 'src' in item && 'thumbnail' in item && 'type' in item) {
      onPlayerOpen();
      setPlayerProps({
        src: item.src as string,
        thumbnail: item.thumbnail as string,
        type: item.type as PlayerType,
      });
    }
  };
  const benefitTitle = (index: number, conditionTitle: string, isDownloadable: boolean) => {
    return (
      <div className="flex w-full items-center justify-between">
        <div className="flex items-center justify-center gap-2">
          <span className="flex h-5 w-12.5 items-center justify-center rounded-m bg-gray-800 text-medium-11 text-white">
            特典 {index + 1}
          </span>
          <span className="text-medium-12 text-secondary">{conditionTitle}</span>
        </div>
        {/* ダウンロードできなかったら、未達成というテキストを表示 */}
        {!isDownloadable && (
          <span className="w-10 rounded bg-gray-500 text-center text-medium-12 text-white">未達成</span>
        )}
      </div>
    );
  };
  return (
    <div className="flex flex-col items-center gap-0.5">
      {items.map((item: Benefit | GachaBenefit, index: number) => {
        if (item.conditionType > 1) {
          const conditionTitle = GACHA_BENEFIT_CONDITIONS.find(
            (condition) => condition.value === item.conditionType,
          )?.label;

          return (
            <BenefitItemWithTitle
              title={benefitTitle(
                index,
                conditionTitle!,
                item.benefitFiles.some((file) => file.src),
              )}
              onDownload={isPreview ? undefined : onFileDownload}
              handleClick={handleClick}
              index={index}
              key={index}
              item={item as unknown as GachaBenefit}
              length={items.length}
            />
          );
        } else {
          const filteredBenefitFiles = item.benefitFiles.filter(
            (benefitFile: SingleItem) => benefitFile.id !== 'empty',
          );
          return filteredBenefitFiles.map((benefitFile: SingleItem, index: number) => {
            return (
              <BenefitItem
                key={benefitFile?.id}
                item={benefitFile}
                readOnly={readOnly}
                onDownload={onFileDownload}
                handleClick={handleClick}
                index={index}
                length={filteredBenefitFiles.length}
              />
            );
          });
        }
      })}
    </div>
  );
};

export default BenefitItemList;
