'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import Thumbnail from '@/components/containers/Thumbnail';
import ShopPublicImage from '@/components/ShopImage';
import GachaCompleteBadge from '../GachaCompleteBadge';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { useHandleSetCustomThumbnail } from '@/hooks/useHandleSetCustomThumbnail';

type GachaThumbnailSectionProps = {
  numbering: string;
};

const GachaThumbnailSection = ({ numbering }: GachaThumbnailSectionProps) => {
  const { exhibits, setThumbnailCustomImage, setThumbnail } = useExhibitsStore();

  const { handleSetCustomThumbnail } = useHandleSetCustomThumbnail();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { title, thumbnailCustomImage = '' } = exhibitItem ?? {};

  const [previewImage, setPreviewImage] = useState<string>('');

  const popupHeader = '表紙とコンプバッジの画像設定';
  const popupContent = (
    <div className="flex items-center justify-center bg-navy-50 py-4">
      <ShopPublicImage src={'/images/gacha/gachaThumbnailCover.webp'} width={242} height={158} alt="modal" />
    </div>
  );
  const popupFooter = 'ここで登録した画像は、商品一覧に表示されるサムネイルと、コンプリートバッジに表示されます。';
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  useEffect(() => {
    if (thumbnailCustomImage) {
      setThumbnail(itemId, thumbnailCustomImage);
    }
  }, [thumbnailCustomImage, itemId, setThumbnail]);

  useEffect(() => {
    if (thumbnailCustomImage) {
      setPreviewImage(thumbnailCustomImage);
    }
  }, [thumbnailCustomImage]);

  const handleCroppedImage = useCallback(
    async (croppedImageUrl: string) => {
      try {
        setPreviewImage(croppedImageUrl);

        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const file = new File([blob], 'thumbnail.png', { type: blob.type });

        await handleSetCustomThumbnail({
          itemId,
          title,
          setThumbnailCustomImage,
          file,
        });
      } catch (error) {
        console.error('Failed to upload cropped thumbnail:', error);
        setPreviewImage(thumbnailCustomImage);
      }
    },
    [handleSetCustomThumbnail, itemId, title, setThumbnailCustomImage, thumbnailCustomImage],
  );

  const displayImage = previewImage || thumbnailCustomImage;

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber
            title="表紙とコンプバッジの画像設定"
            numbering={numbering}
            required
            className="!mb-0"
          />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>

        <div>
          <div className="mb-4 text-medium-13">表紙専用の画像アップロード</div>
          <div className="mb-7 flex justify-center">
            <Thumbnail
              size="md"
              thumbImage={displayImage}
              setThumbImage={handleCroppedImage}
              originalImage={displayImage}
              isNeedEditIcon
              isNeedCrop={true}
              shape="circle"
            />
          </div>
          <div className="mb-4">
            <div className="text-medium-13">コンプリートバッジ画像の設定</div>
            <span className="text-regular-10 text-gray-500">*ガチャ図鑑コンプリートでユーザーが貰えるバッジです</span>
          </div>
          <GachaCompleteBadge
            creatorAvatar={displayImage}
            // 出品流れにコンプバッジのランクがいらない,0にする
            completeRank={0}
          />
        </div>
      </div>
    </section>
  );
};

export default GachaThumbnailSection;
