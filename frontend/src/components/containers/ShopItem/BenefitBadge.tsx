import React from 'react';
import StateBadge from '@/components/atoms/badges/state-badge';
import ShopPublicImage from '@/components/ShopImage';

const BenefitBadge = () => {
  return (
    <StateBadge type="square-radius-filled" color="pink" size="sm" className="flex items-end justify-around">
      <ShopPublicImage src={'/images/icons/Benefit.svg'} alt="benefit" width={15} height={15} />
      <span className="text-bold-13 !leading-none">特典付き</span>
    </StateBadge>
  );
};

export default BenefitBadge;
