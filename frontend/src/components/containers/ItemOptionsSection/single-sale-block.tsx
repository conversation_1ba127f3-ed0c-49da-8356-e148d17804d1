import Accordion from '@/components/atoms/accordion';
import Button from '@/components/atoms/button';
import CheckBoxGroup from '@/components/atoms/checkboxGroup/checkbox-group';
import CheckBoxGroupItem from '@/components/atoms/checkboxGroup/checkbox-group-item';
import Instruction from '@/components/atoms/typography/instruction';
import ShopPublicImage from '@/components/ShopImage';
import SingleSaleSection from '../SingleSaleSection';
import AccordionContent from './accordion-content';
import { DigitalBundle } from '@/types/exhibitItem';
import { OptionType, Period } from '@/types/shopItem';

type SingleSaleBlockProps = {
  itemId: string;
  singleSale: boolean;
  limited: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleOpenIntroductionModal: (type: OptionType) => void;
  isNew: (itemId: string) => boolean;
  exhibitItem: DigitalBundle;
  period?: Period;
  defaultPrice: number;
};

const SingleSaleBlock = ({
  itemId,
  singleSale,
  limited,
  handleChange,
  handleOpenIntroductionModal,
  isNew,
  exhibitItem,
  period,
  defaultPrice,
}: SingleSaleBlockProps) => {
  return (
    <div className="mb-4">
      <Accordion title="単品販売の許可" type="white" defaultOpen={singleSale}>
        <AccordionContent>
          {!limited ? (
            <div className="mb-4 flex items-center justify-between">
              <CheckBoxGroup>
                <CheckBoxGroupItem
                  id="singleSaleEnable"
                  label="単品販売を有効にする"
                  onCheckbox={handleChange}
                  checked={singleSale}
                  disabled={
                    (!isNew(itemId) && exhibitItem?.singleSaleDefault) ||
                    (exhibitItem?.itemFiles?.length && exhibitItem?.itemFiles?.length <= 1) ||
                    !exhibitItem?.itemFiles?.length
                  }
                />
              </CheckBoxGroup>
              <Button
                buttonType="light-small"
                buttonShape="circle"
                buttonSize="xxs"
                onClick={() => handleOpenIntroductionModal(OptionType.SINGLE_SALE)}
              >
                <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
              </Button>
            </div>
          ) : (
            <div className="relative mb-4 flex w-full items-center justify-center rounded-lg pt-1">
              <p className="text-center text-regular-13 text-error">
                販売個数を指定すると
                <br />
                単体販売を許可できません
              </p>
              <Button
                buttonType="light-small"
                buttonShape="circle"
                buttonSize="xxs"
                onClick={() => handleOpenIntroductionModal(OptionType.SINGLE_SALE)}
                className="absolute bottom-0 right-0 top-2.5"
              >
                <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
              </Button>
            </div>
          )}
          <p className="mb-2 text-regular-13">
            「単品販売」は、複数の商品データをアップロードしたときに設定できる機能です。
          </p>
          <Instruction>*単品販売には購入特典は付けられません。</Instruction>
          <Instruction>*単品商品は後で編集することが出来ません。</Instruction>
          {!limited && (!period?.start || !period?.end) && singleSale && (
            <SingleSaleSection defaultChecked={singleSale} defaultPrice={defaultPrice} />
          )}
        </AccordionContent>
      </Accordion>
    </div>
  );
};

export default SingleSaleBlock;
