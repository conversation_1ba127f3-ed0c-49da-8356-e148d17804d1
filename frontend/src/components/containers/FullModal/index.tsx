import React from 'react';
import clsx from 'clsx';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import ShopPublicImage from '@/components/ShopImage';

type FullModalProps = {
  isOpen: boolean;
  isHeaderWhite?: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
};

const FullModal: React.FC<FullModalProps> = ({ isOpen, isHeaderWhite = false, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 mx-auto h-screen max-w-120">
      <div className="h-screen bg-gray-100">
        <div
          className={clsx('flex h-14 items-center justify-between px-4', isHeaderWhite ? 'bg-white' : 'bg-gray-800')}
        >
          <div className="w-8"></div>
          <div
            className={clsx({
              ['text-bold-16 text-black']: isHeaderWhite,
              ['text-medium-14 text-white']: !isHeaderWhite,
            })}
          >
            {title}
          </div>
          {isHeaderWhite ? (
            <OutlinedButton
              buttonColor={'black'}
              buttonShape={'oval'}
              buttonSize={'md'}
              buttonType={'close'}
              onClick={onClose}
            />
          ) : (
            <Button buttonSize="sm" buttonType="light-shadow" buttonShape="circle" onClick={onClose}>
              <ShopPublicImage src="/images/icons/CloseBtn.svg" width={12.83} height={12.83} alt="close" />
            </Button>
          )}
        </div>
        {children}
      </div>
    </div>
  );
};

export default FullModal;
