import PreviewSquareItem from '@/components/views/PreviewSquareItem';
import { Benefit } from '@/types/shopItem';
type CommonConfirmBenefitSectionProps = {
  firstBenefit: Benefit;
};
const CommonConfirmBenefitSection = ({ firstBenefit }: CommonConfirmBenefitSectionProps) => {
  return (
    <>
      {firstBenefit?.description && (
        <div className="mt-4 whitespace-pre-wrap text-regular-13">{firstBenefit.description}</div>
      )}
      <div className="mt-4 grid grid-cols-3 items-start gap-4 PC:grid-cols-4 PC:gap-2">
        {firstBenefit?.benefitFiles.map(
          (benefit) => benefit.title && <PreviewSquareItem key={benefit.title} item={benefit} />,
        )}
      </div>
    </>
  );
};

export default CommonConfirmBenefitSection;
