'use client';

import React, { useState } from 'react';
import clsx from 'clsx';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import ShopPublicImage from '@/components/ShopImage';
import 'swiper/css';
import 'swiper/css/pagination';

type SlideItem = {
  imageSrc: string;
  width: number;
  height: number;
  alt: string;
};

type InstructionSwiperProps = {
  title?: React.ReactNode[];
  slides: SlideItem[];
  footerTexts?: React.ReactNode[];
  className?: string;
  bgColor?: string;
  containerClassName?: string;
  slideContainerHeight?: string; // Tailwindの高さクラス名を渡す
  swiperClassName?: string; // SwiperコンポーネントのカスタムclassName
  slidePadding?: string; // slideコンテナのpadding、追加の高さを設定するため
};

const InstructionSwiper = ({
  title,
  slides,
  footerTexts,
  className = '',
  bgColor,
  containerClassName = '',
  slideContainerHeight = 'h-auto',
  swiperClassName,
  slidePadding = 'pb-6',
}: InstructionSwiperProps) => {
  const [slideIndex, setSlideIndex] = useState(0);

  const renderTitle = () => {
    if (!title || title.length === 0) return null;
    return (
      <div className="flex h-18 items-center justify-center text-center text-bold-15">
        {title[title.length === 1 ? 0 : slideIndex] || null}
      </div>
    );
  };

  const renderFooter = () => {
    if (!footerTexts || footerTexts.length === 0) return null;
    return (
      <div className="h-18 px-4 pt-3 text-regular-12">
        {footerTexts[footerTexts.length === 1 ? 0 : slideIndex] || null}
      </div>
    );
  };

  return (
    <div className={clsx(className, containerClassName)}>
      {renderTitle()}
      <Swiper
        pagination={{
          clickable: true,
        }}
        modules={[Pagination]}
        className={clsx(swiperClassName, 'instructions-swiper')}
        onSlideChange={(swiper) => setSlideIndex(swiper.activeIndex)}
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <div
              className={clsx(
                '!flex items-center justify-center',
                bgColor ? bgColor : 'bg-navy-50',
                slideContainerHeight,
                slidePadding,
              )}
            >
              <ShopPublicImage src={slide.imageSrc} width={slide.width} height={slide.height} alt={slide.alt} />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      {renderFooter()}
    </div>
  );
};

export default InstructionSwiper;
