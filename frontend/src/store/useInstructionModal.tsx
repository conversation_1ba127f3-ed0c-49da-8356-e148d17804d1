/**
 * Store for managing instruction modals across the application
 */
import { ReactNode } from 'react';
import { create } from 'zustand';
import InstructionPopUpContent from '@/components/views/InstructionPopUpContent';
import { useModalsStore } from '@/store/useModals';

interface IInstructionModalStore {
  /**
   * Opens an instruction modal with the specified content
   * @param popupHeader The header content of the modal
   * @param popupContent The body content of the modal
   * @param popupFooter The footer content of the modal
   */
  openInstructionModal: (
    popupHeader: ReactNode,
    popupContent: ReactNode,
    popupFooter: ReactNode,
    cancelText?: string,
  ) => void;
}

export const useInstructionModalStore = create<IInstructionModalStore>()(() => ({
  openInstructionModal: (popupHeader, popupContent, popupFooter, cancelText) => {
    const { onModalOpen, onModalClose, setModalProps } = useModalsStore.getState();

    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      onConfirm: onModalClose,
      cancelText,
      children: (
        <InstructionPopUpContent headContent={popupHeader} bodyContent={popupContent} footerContent={popupFooter} />
      ),
    });
  },
}));
