import { create } from 'zustand';

type FullModalProps = {
  onClose: () => void;
  title?: string;
  isHeaderWhite?: boolean;
  children: React.ReactNode;
};

interface IFullModalStore {
  isFullModalOpen: boolean;
  onFullModalOpen: () => void;
  onFullModalClose: () => void;
  fullModalProps: FullModalProps;
  setFullModalProps: (props: FullModalProps) => void;
}

export const useFullModalStore = create<IFullModalStore>()((set) => ({
  isFullModalOpen: false,
  onFullModalOpen() {
    set({ isFullModalOpen: true });
    document.documentElement.style.overflow = 'hidden';
  },
  onFullModalClose() {
    set({ isFullModalOpen: false });
    document.documentElement.style.overflow = '';
  },
  fullModalProps: {
    onClose: () => {},
    title: '',
    children: <></>,
  },
  setFullModalProps(props: FullModalProps) {
    set({ fullModalProps: props });
  },
}));
