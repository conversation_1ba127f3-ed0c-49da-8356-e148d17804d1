// MSW ログツール関数
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 開発環境でのみログを出力する
 */
export const log = (message: string) => {
  if (isDevelopment) {
    console.log(message);
  }
};

/**
 * 開発環境でのみエラーログを出力する
 */
export const logError = (message: string, error?: any) => {
  if (isDevelopment) {
    console.error(message, error);
  }
};

/**
 * 現在の環境が開発環境かどうかを判定する
 */
export const isDevEnvironment = () => isDevelopment;
