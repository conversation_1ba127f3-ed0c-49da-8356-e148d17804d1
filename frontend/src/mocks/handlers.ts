import { faker } from '@faker-js/faker';
import { http, HttpResponse } from 'msw';
import { MediaType } from '@/types/shopItem';

// 例
export interface User {
  id: string;
  name: string;
  email: string;
}

// Mock data for gacha item
const createMockGachaItem = (itemId: string) => {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  // Create mock files for gacha items
  let completedCount = 0;

  const createMockFiles = (count: number) => {
    completedCount = count;
    return Array.from({ length: count }, (_, index) => ({
      id: faker.string.uuid(),
      title: `ガチャアイテム ${index + 1}`,
      type: faker.helpers.arrayElement(['image', 'video', 'audio']) as MediaType,
      src: faker.image.personPortrait(),
      thumbnail: faker.image.personPortrait(),
      size: faker.number.int({ min: 1000, max: 10000000 }),
      duration: index % 2 === 0 ? 0 : faker.number.int({ min: 10, max: 300 }),
      sortOrder: index,
      awardType: faker.number.int({ min: 1, max: 4 }),
      isSecret: faker.datatype.boolean(),
      count: faker.number.int({ min: 0, max: 99 }),
    }));
  };

  // Create mock benefit files
  const createMockBenefitFiles = (count: number) => {
    return Array.from({ length: count }, (_, index) => ({
      conditionType: faker.number.int({ min: 1, max: 3 }),
      benefitFile: {
        id: faker.string.uuid(),
        title: `特典アイテム ${index + 1}`,
        type: faker.helpers.arrayElement(['image', 'video', 'audio']) as MediaType,
        src: faker.image.personPortrait(),
        thumbnail: faker.image.personPortrait(),
        size: faker.number.int({ min: 1000, max: 5000000 }),
        duration: index % 2 === 0 ? 0 : faker.number.int({ min: 10, max: 180 }),
        sortOrder: null,
        awardType: null,
        isSecret: null,
      },
    }));
  };

  // Create mock award probabilities
  const createMockAwardProbabilities = (count: number) => {
    const probabilities = Array.from({ length: count }, (_, index) => ({
      awardType: index + 1,
      probability: faker.number.float({ min: 0.01, max: 0.5, fractionDigits: 2 }),
    }));

    // Ensure the last one makes the total 1.0
    const totalExisting = probabilities.reduce((sum, item) => sum + item.probability, 0);
    if (totalExisting < 1.0) {
      probabilities.push({
        awardType: count + 1,
        probability: parseFloat((1.0 - totalExisting).toFixed(2)),
      });
    }

    return probabilities;
  };

  return {
    data: {
      item: {
        id: itemId,
        title: `デジタルガチャ ${faker.commerce.productName()}`,
        description: faker.commerce.productDescription(),
        thumbnail: faker.image.personPortrait(),
        thumbnailType: 'custom' as const,
        thumbnailBlurLevel: faker.number.int({ min: 0, max: 2 }),
        thumbnailWatermarkLevel: faker.number.int({ min: 0, max: 2 }),
        thumbnailRatio: faker.number.float({ min: 0.5, max: 2.0, fractionDigits: 2 }),
        price: faker.number.int({ min: 500, max: 5000 }),
        currentPrice: faker.number.int({ min: 300, max: 4500 }),
        available: true,
        tags: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => faker.word.sample()),
        hasPassword: faker.datatype.boolean(),
        completedCount: faker.number.int({ min: 0, max: completedCount }),
        itemType: 1, // For gacha items
        isCheckout: faker.datatype.boolean(),
        isPurchased: faker.datatype.boolean(),
        limited: faker.number.int({ min: 10, max: 100 }),
        remainingAmount: faker.number.int({ min: 1, max: 50 }),
        totalCapacity: faker.number.int({ min: 1000, max: 10000000 }),
        duplicatedCount: faker.number.int({ min: 0, max: 10 }),
        // Period information
        period: {
          start: now,
          end: tomorrow,
        },

        // Discount information
        discount: {
          percentage: faker.number.int({ min: 5, max: 50 }),
          start: now,
          end: tomorrow,
        },

        // Item files
        files: createMockFiles(faker.number.int({ min: 3, max: 30 })),

        // Sample files
        samples: createMockFiles(faker.number.int({ min: 2, max: 5 })),

        // Benefits
        benefits: {
          description: faker.commerce.productDescription(),
          benefit: createMockBenefitFiles(faker.number.int({ min: 1, max: 3 })),
        },

        // Gacha-specific fields
        isDuplicated: faker.datatype.boolean(),
        awardProbabilities: createMockAwardProbabilities(faker.number.int({ min: 3, max: 5 })),
      },
    },
    statusText: 'success',
  };
};

const generateMockDrawnGacha = () => {
  return {
    totalQuantity: Math.round(Math.random() * 100),
  };
};

// gacha 引く結果
const generateMockGachaItems = (count: number) => {
  const items = [];

  for (let i = 0; i < count; i++) {
    const randomIndex = Math.random();
    let awardType;

    if (randomIndex < 0.05) {
      awardType = 4; // S 级 (5%)
    } else if (randomIndex < 0.15) {
      awardType = 3; // A 级 (10%)
    } else if (randomIndex < 0.4) {
      awardType = 2; // B 级 (25%)
    } else {
      awardType = 1; // C 级 (60%)
    }

    items.push({
      id: `item_${i}_${Date.now()}`,
      title: `ガチャアイテム ${i + 1} (${['S', 'A', 'B', 'C'][awardType - 1]}賞)`,
      objectUri: faker.image.personPortrait(),
      thumbnail: faker.image.personPortrait(),
      type: 'image',
      size: Math.floor(Math.random() * 1000000) + 10000,
      duration: 0,
      sortOrder: i,
      awardType,
      isSecret: Math.random() < 0.1, // 10% secret
      count: Math.random() < 0.3 ? 2 : 1, // 30% purchased
    });
  }

  return items;
};

const handlers = [
  // Mock handler for getGachaItem - updated to match exact API URL pattern
  http.get('*/shops/:identityId/items/:itemId/digital-gacha', ({ params }) => {
    const { identityId, itemId } = params;
    console.log('MSW intercepted gacha item request:', { identityId, itemId });
    const mockData = createMockGachaItem(itemId as string);

    console.log('MSW returning mock data structure:', {
      data: mockData.data,
      statusText: mockData.statusText,
    });

    return HttpResponse.json(
      {
        data: mockData.data,
        statusText: mockData.statusText,
      },
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
  }),

  // Mock handler for getDrawnGacha - using a more flexible pattern to catch all variations
  http.get('*/api/shop/digital-gacha/:id/draw', ({ params }) => {
    // Extract the itemId from the URL params
    const { id } = params;
    console.log('MSW intercepted digital-gacha draw request:', { id });

    const mockData = generateMockDrawnGacha();
    console.log('Returning mock data:', mockData);

    // Return the data in the format expected by the component
    return HttpResponse.json(
      {
        data: mockData,
      },
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
  }),
];

export { createMockGachaItem, generateMockGachaItems, handlers };
