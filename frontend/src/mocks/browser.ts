// Import only the handlers for now
import { handlers } from './handlers';
import { log, logError } from './logger';

// Create a placeholder worker that will be initialized later
let worker: any = null;

// Dynamically import MSW to avoid Next.js module resolution issues
async function initMsw() {
  if (typeof window === 'undefined') return null;

  try {
    // Dynamic import of MSW browser module
    const { setupWorker } = await import('msw/browser');
    return setupWorker(...handlers);
  } catch (error) {
    logError('Failed to initialize MSW:', error);
    return null;
  }
}

// Initialize the worker
initMsw().then((w) => {
  if (w) {
    worker = w;
    log('✅ MSW worker initialized successfully in browser');
  } else {
    logError('❌ MSW worker initialization failed in browser');
  }
});

export { worker };
