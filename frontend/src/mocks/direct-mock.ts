// This file provides a direct way to enable MSW mocking
// Import handlers directly
import { handlers } from './handlers';
import { log, logError, isDevEnvironment } from './logger';

// Create and start the worker immediately
if (typeof window !== 'undefined') {
  (async () => {
    try {
      log('🔄 Attempting to initialize MSW directly...');

      // Dynamic import of MSW browser module
      const { setupWorker } = await import('msw/browser');

      // Log all handlers for debugging
      log(`📋 MSW Handlers: ${handlers.length} handlers registered`);

      const worker = setupWorker(...handlers);

      worker
        .start({
          onUnhandledRequest: (request) => {
            // Log unhandled requests but don't show warnings
            log(`⚠️ MSW Unhandled Request: ${request.method} ${request.url}`);
            return 'bypass';
          },
        })
        .then(() => {
          log('✅ MSW initialized successfully through direct-mock.ts');
        })
        .catch((error) => {
          logError('❌ MSW initialization failed:', error);
        });

      // Make worker available globally for debugging
      if (isDevEnvironment()) {
        (window as any).__mswWorker = worker;
      }

      // Add a listener to log all intercepted requests
      if (isDevEnvironment()) {
        worker.events.on('request:start', ({ request }) => {
          log(`🔍 MSW Intercepted Request: ${request.method} ${request.url}`);
        });

        worker.events.on('response:mocked', ({ request, response }) => {
          log(`✅ MSW Mocked Response: ${request.method} ${request.url} ${response.status}`);
        });
      }

      log('MSW worker created and started');
    } catch (error) {
      logError('Failed to initialize MSW directly:', error);
    }
  })();
}
