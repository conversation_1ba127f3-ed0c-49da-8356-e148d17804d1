import { log, logError } from './logger';

export async function enableMocking() {
  log('🔄 enableMocking function called');

  // Only run in browser environment
  if (typeof window !== 'undefined') {
    // Always enable mocking for development
    log('🔄 Browser environment detected, attempting to enable MSW mocking...');
    try {
      // Dynamically import the browser module
      const { worker } = await import('./browser');

      // Check if worker is initialized
      if (worker) {
        await worker.start();
        log('✅ MSW Mocking enabled');
      } else {
        // Wait a bit and try again if worker is not yet initialized
        setTimeout(async () => {
          const { worker } = await import('./browser');
          if (worker) {
            await worker.start();
            log('✅ MSW Mocking enabled (delayed)');
          } else {
            logError('❌ MSW worker not initialized');
          }
        }, 1000);
      }
    } catch (error) {
      logError('❌ Error initializing MSW:', error);
    }
  } else {
    log('🚀 Not in browser environment, MSW Mocking disabled');
  }

  // We'll skip the force import since it's causing module resolution issues
  // The worker should be initialized through the normal flow in layout.tsx
  if (typeof window !== 'undefined') {
    log('MSW initialization should happen through the normal flow');
    log("If you're not seeing MSW logs, check your browser's network tab for any 404 errors related to MSW");
  }
}
