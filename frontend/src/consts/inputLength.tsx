// ショップ作り画面
const SHOP_NAME_MAX_LENGTH = 50;
const SHOP_DESCRIPTION_MAX_LENGTH = 500;
const SHOP_MESSAGE_MAX_LENGTH = 100;

// セット、サンプルアイテムの最大アップロード数
const MAX_UPLOAD_SET_ITEMS = 5;
// ガチャ特典の最大アップロード数
const MAX_UPLOAD_GACHA_BENEFITS = 5;

// 商品作り画面
const ITEM_TITLE_MAX_LENGTH = 100;
const ITEM_DESCRIPTION_MAX_LENGTH = 800;
const ITEM_TAG_MAX_LENGTH = 30;

const ITEM_FILENAME_MAX_LENGTH = 30;
const ITEM_PASSWORD_MIN_LENGTH = 4;
const ITEM_PASSWORD_MAX_LENGTH = 20;

const BENEFITS_MESSAGE_MAX_LENGTH = 100;

// prices
const ITEM_MAX_PRICE = 1000000;
const ITEM_MIN_PRICE = 100;

// FIXME: チェキと同じ画面でアクキー等の出品をするため一時的に最低価格を500円に設定
const ITEM_MIN_PRICE_FOR_CHEKI = 500;
const ITEM_MAX_SALES_COUNT = 100000;
const ITEM_MAX_QUANTITY_PER_USER = 50;

// cart
const PURCHASER_COMMENT_MAX_LENGTH = 50;
const CART_ITEM_MAX_QUANTITY = 99;

export {
  SHOP_NAME_MAX_LENGTH,
  SHOP_DESCRIPTION_MAX_LENGTH,
  SHOP_MESSAGE_MAX_LENGTH,
  ITEM_TITLE_MAX_LENGTH,
  ITEM_DESCRIPTION_MAX_LENGTH,
  ITEM_TAG_MAX_LENGTH,
  ITEM_FILENAME_MAX_LENGTH,
  ITEM_MAX_PRICE,
  ITEM_MIN_PRICE,
  ITEM_MIN_PRICE_FOR_CHEKI,
  BENEFITS_MESSAGE_MAX_LENGTH,
  ITEM_MAX_SALES_COUNT,
  ITEM_PASSWORD_MIN_LENGTH,
  ITEM_PASSWORD_MAX_LENGTH,
  MAX_UPLOAD_SET_ITEMS,
  PURCHASER_COMMENT_MAX_LENGTH,
  CART_ITEM_MAX_QUANTITY,
  ITEM_MAX_QUANTITY_PER_USER,
  MAX_UPLOAD_GACHA_BENEFITS,
};
