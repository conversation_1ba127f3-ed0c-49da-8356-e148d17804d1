import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { MediaType, SingleItem } from './shopItem';

export type Shop = {
  name: string;
  tenant: string;
  creatorUid: string;
  creatorName: string;
  creatorAccountIdentity: string;
  description?: string;
  headerImageUri?: string;
  message: string;
  marginRate: Record<number, number>;
  isOpen: boolean;
  creatorIconUri: string;
  identityId: string;
  limitation: ShopLimitation;
};

export type createShopInfo = {
  shopName: string;
  description: string;
  coverImage: string;
};

export type CardItemType = SingleItem & { type: MediaType };
