import type { CreateSingleOrderRequest } from '@/lib/client-api/client-shop-api.schemas';
import type { PaymentInfo, AmountInfo } from './order';
import type { PAYMENT_METHODS } from '@/consts/order';

// TODO: 将来的にすべての支払い関連の型はorvalで生成された型に統一する
// 現在は一時的な対応として、CreateSingleOrderRequestから必要な支払い関連のフィールドのみを抽出して使用
export type BasePaymentParams = Pick<
  CreateSingleOrderRequest,
  'paymentMethod' | 'tip' | 'cardParam' | 'convenienceParam' | 'googlePayParam' | 'applePayParam'
>;

// Google Pay SDKとの通信に使用する型定義
// この型はAPIリクエストの型とは別で、Google Pay固有の実装に必要
export type GooglePaymentRequest = {
  apiVersion: number;
  apiVersionMinor: number;
  allowedPaymentMethods?: any[];
  transactionInfo?: {
    totalPriceStatus: string;
    totalPrice: string;
    currencyCode: string;
    countryCode: string;
  };
  merchantInfo?: {
    merchantName: string;
    merchantId: string;
  };
};

// 支払い処理のコールバック関数の共通型
export type BasePaymentCallbacks = {
  clearOtherPaymentParams: (paymentMethod: PAYMENT_METHODS) => void;
  setConvenienceResult: (result: any) => void;
};

// 支払い処理のPropsの共通型
export type BasePaymentProcessProps = {
  payment: PaymentInfo;
  amount: AmountInfo;
  identityId: string;
  trigger: () => Promise<boolean>;
};
