import { GACHA_BENEFIT_CONDITIONS } from '@/consts/gacha-data';
import { CONDITION_TYPE, CONDITION_TYPE_TIMES, type ConditionType, type GachaBenefit } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType } from '@/types/shopItem';

export interface BenefitItem extends GachaBenefit {
  uploadProgress: number;
}

export type FormValues = {
  benefits: {
    [key: string]: {
      conditionType: ConditionType;
      description: string;
    };
  };
};

/**
 * 指定した特典で利用可能な条件オプションを取得
 */
export const getAvailableConditions = (
  currentBenefitId: number,
  benefitItems: BenefitItem[],
  isCompBenefitEnabled: boolean,
  isDuplicated?: boolean,
  itemFilesCount?: number,
  itemType?: ExhibitType,
) => {
  const currentBenefit = benefitItems.find((item) => item.id === currentBenefitId);
  const isPrintGacha = itemType === ITEM_TYPE.PRINT_GACHA.str;

  let minAllowedValue = 0;
  for (let i = 0; i < currentBenefitId; i++) {
    const previousBenefit = benefitItems.find((item) => item.id === i);
    if (previousBenefit?.conditionType && previousBenefit.conditionType !== CONDITION_TYPE.COMPLETED) {
      minAllowedValue = Math.max(minAllowedValue, previousBenefit.conditionType);
    }
  }

  const isCompletedUsedByOthers = benefitItems.some(
    (benefit) => benefit.id !== currentBenefitId && benefit.conditionType === CONDITION_TYPE.COMPLETED,
  );

  const filteredOptions = GACHA_BENEFIT_CONDITIONS.filter((option) => {
    if (option.value === CONDITION_TYPE.COMPLETED) {
      const isCurrentlySelected = currentBenefit?.conditionType === CONDITION_TYPE.COMPLETED;
      if (isPrintGacha || isDuplicated === true) {
        return false;
      }
      // 重複なし設定時は、全ての特典でコンプリート特典を選択可能
      if (isDuplicated === false) {
        return isCompBenefitEnabled;
      }
      // itemFiles の数がコンプ特典の場合は常に表示（itemFilesCount チェックなし）
      return isCompBenefitEnabled && (isCurrentlySelected || !isCompletedUsedByOthers);
    }

    const currentValue = option.value;
    if (currentValue) {
      const allowed = currentValue >= minAllowedValue;

      // itemFiles の数をチェック（TIMES_XX 特典の場合のみ）
      if (!isDuplicated && itemFilesCount !== undefined && currentValue !== (CONDITION_TYPE.COMPLETED as number)) {
        let requiredItemFiles = 0;

        // 各条件に必要なitemFiles数を設定
        switch (currentValue) {
          case CONDITION_TYPE.TIMES_10:
            requiredItemFiles = CONDITION_TYPE_TIMES.TIMES_10;
            break;
          case CONDITION_TYPE.TIMES_20:
            requiredItemFiles = CONDITION_TYPE_TIMES.TIMES_20;
            break;
          case CONDITION_TYPE.TIMES_30:
            requiredItemFiles = CONDITION_TYPE_TIMES.TIMES_30;
            break;
        }

        // itemFiles が必要な数より少ない場合は表示しない
        if (requiredItemFiles > 0 && itemFilesCount < requiredItemFiles) {
          return false;
        }
      }

      return allowed;
    }

    return true;
  });

  return filteredOptions;
};
