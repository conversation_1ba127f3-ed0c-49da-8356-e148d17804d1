import { GachaBenefit, GachaBenefitFile } from '@/types/gacha';
import { Benefit, SingleItem } from '@/types/shopItem';

/**
 * Extract all benefit files from benefits structure.
 * @param benefits - Benefit[] or GachaBenefit[] or undefined
 * @returns Array of benefit files (SingleItem or GachaBenefitFile)
 */
export const extractBenefitFiles = (
  benefits: Benefit[] | GachaBenefit[] | undefined,
): (SingleItem | GachaBenefitFile)[] => {
  if (!benefits || !Array.isArray(benefits)) return [];
  return benefits.flatMap((benefit) => (benefit.benefitFiles as (SingleItem | GachaBenefitFile)[]) ?? []);
};
