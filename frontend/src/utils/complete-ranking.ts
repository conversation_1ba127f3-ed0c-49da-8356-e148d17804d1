import type { CompleteTopRank } from '@/types/common';

export const RANK_STYLES = {
  decorationColor: {
    1: 'var(--ranking-first-decoration)',
    2: 'var(--ranking-second-decoration)',
    3: 'var(--ranking-third-decoration)',
  },
  avatarBorder: {
    1: 'var(--ranking-first-avatar-border)',
    2: 'var(--ranking-second-avatar-border)',
    3: 'var(--ranking-third-avatar-border)',
    default: 'white',
  },
  opacity: {
    normal: 0.4,
    highlighted: {
      1: 0.1,
      2: 0.1,
      3: 0.2,
    },
  },
} as const;

export const getRankStyle = {
  decorationColor: (rank: number): string => RANK_STYLES.decorationColor[rank as CompleteTopRank] ?? '',
  avatarBorder: (rank: number): string =>
    RANK_STYLES.avatarBorder[rank as CompleteTopRank] ?? RANK_STYLES.avatarBorder.default,
  opacity: (rank: number, isHighlighted: boolean): number => {
    if (!isHighlighted) return RANK_STYLES.opacity.normal;
    return RANK_STYLES.opacity.highlighted[rank as CompleteTopRank] ?? RANK_STYLES.opacity.normal;
  },
};
