import type { DigitalBundle, ExhibitChekiItem, ExhibitGachaItem, ExhibitPrintGachaItem } from '@/types/exhibitItem';
import { ITEM_TYPE } from '@/types/item';

export const isDigitalBundle = (item: any): item is DigitalBundle =>
  item && item.itemType === ITEM_TYPE.DIGITAL_BUNDLE.value;
export const isDigitalGacha = (item: any): item is ExhibitGachaItem =>
  item && item.itemType === ITEM_TYPE.DIGITAL_GACHA.value;
export const isPrintGacha = (item: any): item is ExhibitPrintGachaItem =>
  item && item.itemType === ITEM_TYPE.PRINT_GACHA.value;
export const isExhibitCheki = (item: any): item is ExhibitChekiItem => item && item.itemType === ITEM_TYPE.CHEKI.value;
export const isDigitalItem = (item: any): item is DigitalBundle | ExhibitGachaItem =>
  isDigitalBundle(item) || isDigitalGacha(item);
export const isPhysicalItem = (item: any): item is ExhibitChekiItem | ExhibitPrintGachaItem => !isDigitalItem(item);
export const isGacha = (item: any): item is ExhibitGachaItem | ExhibitPrintGachaItem =>
  isDigitalGacha(item) || isPrintGacha(item);
