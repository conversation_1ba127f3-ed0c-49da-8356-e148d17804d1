import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';

// Award type labels and icons
export const awardLabels = {
  [AWARD_TYPE.S]: { label: 'S賞', icon: '/images/gacha/icons/RarityS.svg' },
  [AWARD_TYPE.A]: { label: 'A賞', icon: '/images/gacha/icons/RarityA.svg' },
  [AWARD_TYPE.B]: { label: 'B賞', icon: '/images/gacha/icons/RarityB.svg' },
  [AWARD_TYPE.C]: { label: 'C賞', icon: '/images/gacha/icons/RarityC.svg' },
};

// ヘルパー関数：降格移動時の元賞のアイテム数チェック
export const checkDemotionIntegrity = (
  items: GachaItemFile[],
  sourceAward: Award,
  targetAward: Award,
): { valid: boolean; message: string } => {
  // 上位賞 -> 下位賞への移動の場合のみチェック
  if (sourceAward > targetAward) {
    const sourceCount = items.filter((item) => item.awardType === sourceAward).length;
    if (sourceCount <= 1) {
      return {
        valid: false,
        message: `${awardLabels[sourceAward].label}のアイテムが1つしかないため、${awardLabels[targetAward].label}には移動できません。`,
      };
    }
  }
  return { valid: true, message: '' };
};

// 中間賞の存在チェック
export const checkIntermediateAwards = (
  items: GachaItemFile[],
  sourceAward: Award,
  targetAward: Award,
): { valid: boolean; message: string } => {
  // 低級賞 -> 高級賞の移動のみチェック（S→A→B→Cの順序）
  if (sourceAward >= targetAward) {
    return { valid: true, message: '' };
  }

  // 中間の賞をチェック
  for (let tier = sourceAward + 1; tier < targetAward; tier++) {
    const tierCount = items.filter((item) => item.awardType === tier).length;
    if (tierCount === 0) {
      return {
        valid: false,
        message: `${awardLabels[tier as Award].label}が空のため、${awardLabels[sourceAward].label}から${awardLabels[targetAward].label}には直接移動できません。`,
      };
    }
  }

  return { valid: true, message: '' };
};

// グループ化されたアイテムを使用した削除検証関数
export const validateDeleteWithGroupedItems = (
  groupedItems: GachaItemFile[][],
  fileIdToDelete: string,
): { valid: boolean; message: string } => {
  // 削除対象のアイテムとその賞を特定
  let fileAwardIndex = -1;

  // groupedItems[0] = S賞、groupedItems[1] = A賞、groupedItems[2] = B賞、groupedItems[3] = C賞
  const awardMap = [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C];

  // 空でないグループと対応するインデックスを取得
  const nonEmptyGroups: GachaItemFile[][] = [];

  groupedItems.forEach((items) => {
    if (items.length > 0) {
      nonEmptyGroups.push(items);
    }
  });

  // 削除対象ファイルの賞インデックスを取得
  for (let i = 0; i < groupedItems.length; i++) {
    const foundItem = groupedItems[i].find((item) => item.id === fileIdToDelete);
    if (foundItem) {
      fileAwardIndex = i;
      break;
    }
  }

  if (fileAwardIndex === -1) {
    return { valid: true, message: '' }; // アイテムが見つからない場合は削除可能
  }

  // アクティブな賞が1つしかない場合は常に削除可能
  if (nonEmptyGroups.length <= 1) {
    return { valid: true, message: '' };
  }

  // 各賞のアイテム数を取得
  const counts = groupedItems.map((items) => items.length);

  // 削除後のカウントをシミュレート
  const afterCounts = [...counts];
  afterCounts[fileAwardIndex]--;

  // 削除後の空でないグループのインデックスを再計算
  const activeIndicesAfterDelete = [];
  for (let i = 0; i < afterCounts.length; i++) {
    if (afterCounts[i] > 0) {
      activeIndicesAfterDelete.push(i);
    }
  }

  // 削除前の最低位賞のインデックス（元の配列内の位置）
  const lowestActiveIndex = nonEmptyGroups.length - 1;

  // 削除対象が最低位でなく、削除後に空になる場合は削除不可
  if (fileAwardIndex !== lowestActiveIndex && afterCounts[fileAwardIndex] === 0) {
    // 削除できない賞タイプに応じたエラーメッセージ
    const awardType = awardMap[fileAwardIndex];
    let errorMessage = '';

    switch (awardType) {
      case AWARD_TYPE.S:
        errorMessage =
          'S賞の景品を削除することはできません。賞の数を調整したい場合は、A賞以下の景品を移動・調整してください。';
        break;
      case AWARD_TYPE.A:
        errorMessage =
          'B賞以下が登録されていますので、A賞の景品を削除することはできません。賞の数を調整したい場合は、B賞以下の景品をA賞に移動・調整してください。';
        break;
      case AWARD_TYPE.B:
        errorMessage =
          'C賞が登録されていますので、B賞の景品を削除することはできません。賞の数を調整したい場合は、C賞の景品をB賞に移動・調整してください。';
        break;
      default:
        errorMessage = `削除すると${awardLabels[awardType].label}が空になり、賞が飛んでしまうため削除できません。`;
    }

    return {
      valid: false,
      message: errorMessage,
    };
  }

  return { valid: true, message: '' };
};

// 移動ルールの定義マトリックス
export const createMoveValidityMatrix = () => ({
  // S賞からの移動（4）
  [AWARD_TYPE.S]: {
    [AWARD_TYPE.S]: () => ({ valid: true, message: '' }), // S→S: 常に可
    [AWARD_TYPE.A]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.S, AWARD_TYPE.A), // S→A
    [AWARD_TYPE.B]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.S, AWARD_TYPE.B), // S→B
    [AWARD_TYPE.C]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.S, AWARD_TYPE.C), // S→C
  },
  // A賞からの移動（3）
  [AWARD_TYPE.A]: {
    [AWARD_TYPE.S]: () => ({ valid: true, message: '' }), // A→S (昇格移動はここでは制限なし)
    [AWARD_TYPE.A]: () => ({ valid: true, message: '' }), // A→A: 常に可
    [AWARD_TYPE.B]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.A, AWARD_TYPE.B), // A→B
    [AWARD_TYPE.C]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.A, AWARD_TYPE.C), // A→C
  },
  // B賞からの移動（2）
  [AWARD_TYPE.B]: {
    [AWARD_TYPE.S]: () => ({ valid: true, message: '' }), // B→S (昇格移動はここでは制限なし)
    [AWARD_TYPE.A]: () => ({ valid: true, message: '' }), // B→A (昇格移動はここでは制限なし)
    [AWARD_TYPE.B]: () => ({ valid: true, message: '' }), // B→B: 常に可
    [AWARD_TYPE.C]: (items: GachaItemFile[], _sourceId: string) =>
      checkDemotionIntegrity(items, AWARD_TYPE.B, AWARD_TYPE.C), // B→C
  },
  // C賞からの移動（1）
  [AWARD_TYPE.C]: {
    [AWARD_TYPE.S]: () => ({ valid: true, message: '' }), // C→S (昇格移動はここでは制限なし)
    [AWARD_TYPE.A]: () => ({ valid: true, message: '' }), // C→A (昇格移動はここでは制限なし)
    [AWARD_TYPE.B]: () => ({ valid: true, message: '' }), // C→B (昇格移動はここでは制限なし)
    [AWARD_TYPE.C]: () => ({ valid: true, message: '' }), // C→C: 常に可
  },
});
