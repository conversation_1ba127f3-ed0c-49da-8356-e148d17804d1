'use client';

import { toast } from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { FeatureFlags } from '@/lib/feature';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { MAX_GACHA_ITEM_COUNT } from '@/consts/sizes';
import { validateUploadedFiles } from '@/utils/item';
import { generatePreSignedThumbnails, generateProcessedThumbnail, uploadThumbnail } from '@/utils/thumbnail';
import { handleMediaUpload } from '@/utils/upload';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';
import { ItemFiles, SingleItem } from '@/types/shopItem';

export interface GachaUploadOptions {
  currentFiles: FileList | null;
  existingFiles: GachaItemFile[];
  shopLimitation: ShopLimitation;
  identityId: string;
  setFiles: (files: GachaItemFile[]) => void;
  onProgress?: (id: string, progress: number) => void;
  resetProgress?: () => void;
  awardType?: Award;
  itemId?: string;
  isPrintGacha?: boolean;
}

/**
 * Custom handleFileUpload function for gacha uploads that preserves loading state
 */
export const handleGachaFileUpload = async (options: GachaUploadOptions): Promise<boolean> => {
  const {
    currentFiles,
    existingFiles,
    shopLimitation,
    identityId,
    setFiles,
    onProgress,
    resetProgress,
    awardType = AWARD_TYPE.S, // Default to S award
    isPrintGacha = false,
  } = options;

  // Validate input
  if (!currentFiles || currentFiles.length === 0) return false;

  // Validate files
  const validationResult = validateUploadedFiles(currentFiles, existingFiles, shopLimitation);
  if (validationResult) {
    toast.custom((t) => CustomToast(t, 'error', validationResult));
    return false;
  }

  // Check if adding these files would exceed the maximum
  if (existingFiles.length + currentFiles.length > MAX_GACHA_ITEM_COUNT) {
    toast.custom((t) => CustomToast(t, 'error', `アップロードできるファイルは最大${MAX_GACHA_ITEM_COUNT}個までです`));
    return false;
  }

  try {
    // Reset progress if provided
    if (resetProgress) {
      resetProgress();
    }

    // Handle the upload process - pass existingFiles to enable progress
    let resultFiles = await handleMediaUpload(
      currentFiles,
      existingFiles as ItemFiles,
      (files) => {
        // This callback is called during upload to show progress
        // Only add new files with gacha properties, keep existing files unchanged
        const newFiles = files.filter((f) => !existingFiles.some((ef) => ef.id === f.id));
        const newFilesWithGachaProps = newFiles.map(
          (file) =>
            ({
              ...file,
              awardType,
              isSecret: false,
              sortOrder: 0,
            }) as GachaItemFile,
        );

        setFiles([...existingFiles, ...newFilesWithGachaProps]);
      },
      onProgress,
    );

    // Generate thumbnails
    resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);

    // Process thumbnails
    resultFiles = await Promise.all(
      resultFiles.map(async (file) => {
        const processed = await generateProcessedThumbnail(
          file as SingleItem,
          THUMBNAIL_BLUR_LEVEL.HIGH,
          THUMBNAIL_WATERMARK_LEVEL.WHITE,
        );

        if (file.type !== 'audio' && processed.processedThumbnail) {
          try {
            const processedThumbnailUrl = await uploadThumbnail({
              img: processed.processedThumbnail,
              fileId: file.id,
              isPublic: true,
            });

            let watermarkImageUrl = { url: '' };

            if (FeatureFlags.printGacha() && isPrintGacha) {
              const processedWaterMark = await generateProcessedThumbnail(
                file as SingleItem,
                THUMBNAIL_BLUR_LEVEL.NONE,
                THUMBNAIL_WATERMARK_LEVEL.WHITE,
                true,
              );
              if (processedWaterMark.processedThumbnail) {
                watermarkImageUrl = await uploadThumbnail({
                  img: processedWaterMark.processedThumbnail,
                  fileId: file.id,
                  isPublic: true,
                });
              }
            }

            return {
              ...processed,
              maskedThumbnailUri: processedThumbnailUrl.url,
              watermarkThumbnailUri: watermarkImageUrl.url || null,
              isLoading: false,
            } as unknown as GachaItemFile;
          } catch (error) {
            console.error('Failed to upload processed thumbnail:', error);
          }
        }

        return {
          ...processed,
          isLoading: false,
        } as unknown as GachaItemFile;
      }),
    );

    // Final update: combine existing files with processed new files
    const newFiles = resultFiles.filter((f) => !existingFiles.some((ef) => ef.id === f.id));
    const finalNewFiles = newFiles.map(
      (file) =>
        ({
          ...file,
          awardType,
          isSecret: false,
          sortOrder: 0,
        }) as GachaItemFile,
    );

    setFiles([...existingFiles, ...finalNewFiles]);

    return true;
  } catch (error) {
    console.error('Error processing media files:', error);
    toast.custom((t) => CustomToast(t, 'error', 'ファイルのアップロードに失敗しました'));
    return false;
  }
};
