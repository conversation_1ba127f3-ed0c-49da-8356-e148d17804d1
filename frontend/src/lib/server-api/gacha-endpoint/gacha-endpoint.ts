/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  BadgeRankingResponseBody,
  CompleteBadgeResponseBody,
  GachaPullResponseBody,
  GachaPullableCountResponseBody,
  PullGachaRequest,
  PurchasedItemReceivedFilesResponseBody,
} from '../shop-api.schemas';

/**
 * @summary Pull
 */
export const pull = (pullGachaRequest: PullGachaRequest) => {
  return customServerInstance<GachaPullResponseBody>({
    url: `/shops/gacha/pull`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: pullGachaRequest,
  });
};
/**
 * @summary Get Purchased Item Received Files
 */
export const getPurchasedItemReceivedFiles = (purchasedItemId: number) => {
  return customServerInstance<PurchasedItemReceivedFilesResponseBody>({
    url: `/shops/gacha/purchased-items/${purchasedItemId}/received-files`,
    method: 'GET',
  });
};
/**
 * @summary Get Complete Badge
 */
export const getCompleteBadge = (itemId: number) => {
  return customServerInstance<CompleteBadgeResponseBody>({
    url: `/shops/gacha/${itemId}/complete-badge`,
    method: 'GET',
  });
};
/**
 * @summary Get Complete Badge Ranking
 */
export const getCompleteBadgeRanking = (itemId: number) => {
  return customServerInstance<BadgeRankingResponseBody>({
    url: `/shops/gacha/${itemId}/complete-badge-ranking`,
    method: 'GET',
  });
};
/**
 * @summary Get Pullable Gacha Count
 */
export const getPullableGachaCount = (itemId: number) => {
  return customServerInstance<GachaPullableCountResponseBody>({
    url: `/shops/gacha/${itemId}/pullable-count`,
    method: 'GET',
  });
};
export type PullResult = NonNullable<Awaited<ReturnType<typeof pull>>>;
export type GetPurchasedItemReceivedFilesResult = NonNullable<
  Awaited<ReturnType<typeof getPurchasedItemReceivedFiles>>
>;
export type GetCompleteBadgeResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadge>>>;
export type GetCompleteBadgeRankingResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadgeRanking>>>;
export type GetPullableGachaCountResult = NonNullable<Awaited<ReturnType<typeof getPullableGachaCount>>>;
