/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  AgenciesResponseBody,
  AgencySalesResponseBody,
  AuditGroupsResponseBody,
  AuditStatusResponseBody,
  BaseResponseBodyContentBlockDetailResponse,
  BaseResponseBodyContentBlockResponse,
  BaseResponseBodyContentBlocks,
  BaseResponseBodyListItemMarginRateResult,
  BaseResponseBodyListShopItemTypeMarginRateResult,
  BaseResponseBodyUserProfileResponse,
  BaseResponseBodyUserResponse,
  BaseResponseBodyUsersByPartialAccountIdentityResponse,
  ConsoleUsersResponseBody,
  CreateContentWithDetailRequest,
  GetAgencyMonthlySalesParams,
  GetAuditGroupsParams,
  GetUserMonthlySalesParams,
  GetUsersByPartialAccountIdentityParams,
  ItemMarginRateItem,
  ProxyAccessTokenResponse,
  UpdateContentBlockDetailRequest,
  UpdateShopItemTypeMarginRatesRequest,
  UpdateStatusRequest,
  UsersResponseBody,
} from '../shop-api.schemas';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customServerInstance<AgenciesResponseBody>({ url: `/console/agencies`, method: 'GET' });
};
/**
 * @summary Get Agency Sales
 */
export const getAgencySales = (agencyId: number) => {
  return customServerInstance<AgencySalesResponseBody>({ url: `/console/agencies/${agencyId}/sales`, method: 'GET' });
};
/**
 * @summary Get Agency Monthly Sales
 */
export const getAgencyMonthlySales = (agencyId: number, params?: GetAgencyMonthlySalesParams) => {
  return customServerInstance<void>({ url: `/console/agencies/${agencyId}/sales/monthly`, method: 'GET', params });
};
/**
 * @summary Get Agency Users
 */
export const getAgencyUsers = (agencyId: number) => {
  return customServerInstance<UsersResponseBody>({ url: `/console/agencies/${agencyId}/users`, method: 'GET' });
};
/**
 * @summary Get Audit Groups
 */
export const getAuditGroups = (params?: GetAuditGroupsParams) => {
  return customServerInstance<AuditGroupsResponseBody>({ url: `/console/audit-groups`, method: 'GET', params });
};
/**
 * @summary Update Audit Status
 */
export const updateAuditStatus = (auditGroupId: number, updateStatusRequest: UpdateStatusRequest) => {
  return customServerInstance<AuditStatusResponseBody>({
    url: `/console/audit-groups/${auditGroupId}/status`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateStatusRequest,
  });
};
/**
 * @summary Update Item Margin Rates
 */
export const updateItemMarginRates = (accountIdentity: string, itemMarginRateItem: ItemMarginRateItem[]) => {
  return customServerInstance<BaseResponseBodyListItemMarginRateResult>({
    url: `/console/commission-control/creator/${accountIdentity}/items`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: itemMarginRateItem,
  });
};
/**
 * @summary Update Shop Item Type Margin Rates
 */
export const updateShopItemTypeMarginRates = (
  accountIdentity: string,
  shopId: number,
  updateShopItemTypeMarginRatesRequest: UpdateShopItemTypeMarginRatesRequest,
) => {
  return customServerInstance<BaseResponseBodyListShopItemTypeMarginRateResult>({
    url: `/console/commission-control/creator/${accountIdentity}/shop/${shopId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateShopItemTypeMarginRatesRequest,
  });
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customServerInstance<ConsoleUsersResponseBody>({ url: `/console/console-users`, method: 'GET' });
};
/**
 * @summary Get Users By Partial Account Identity
 */
export const getUsersByPartialAccountIdentity = (params?: GetUsersByPartialAccountIdentityParams) => {
  return customServerInstance<BaseResponseBodyUsersByPartialAccountIdentityResponse>({
    url: `/console/users`,
    method: 'GET',
    params,
  });
};
/**
 * @summary Update Content Block Detail
 */
export const updateContentBlockDetail = (
  accountIdentity: string,
  updateContentBlockDetailRequest: UpdateContentBlockDetailRequest,
) => {
  return customServerInstance<BaseResponseBodyContentBlockDetailResponse>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateContentBlockDetailRequest,
  });
};
/**
 * @summary Get User Content Blocks
 */
export const getUserContentBlocks = (accountIdentity: string) => {
  return customServerInstance<BaseResponseBodyContentBlocks>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'GET',
  });
};
/**
 * @summary Create Content Block
 */
export const createContentBlock = (
  accountIdentity: string,
  createContentWithDetailRequest: CreateContentWithDetailRequest,
) => {
  return customServerInstance<BaseResponseBodyContentBlockResponse>({
    url: `/console/users/${accountIdentity}/content-blocks`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createContentWithDetailRequest,
  });
};
/**
 * @summary Get Profile
 */
export const getProfile = (accountIdentity: string) => {
  return customServerInstance<BaseResponseBodyUserProfileResponse>({
    url: `/console/users/${accountIdentity}/profile`,
    method: 'GET',
  });
};
/**
 * @summary Create Proxy Access Token
 */
export const createProxyAccessToken = (accountIdentity: string) => {
  return customServerInstance<ProxyAccessTokenResponse>({
    url: `/console/users/${accountIdentity}/proxy-access-token`,
    method: 'POST',
  });
};
/**
 * @summary Get User
 */
export const getUser = (id: number) => {
  return customServerInstance<BaseResponseBodyUserResponse>({ url: `/console/users/${id}`, method: 'GET' });
};
/**
 * @summary Get User Monthly Sales
 */
export const getUserMonthlySales = (id: number, params?: GetUserMonthlySalesParams) => {
  return customServerInstance<void>({ url: `/console/users/${id}/sales/monthly`, method: 'GET', params });
};
/**
 * @summary Get User Transaction
 */
export const getUserTransaction = (id: number) => {
  return customServerInstance<void>({ url: `/console/users/${id}/sales/transactions`, method: 'GET' });
};
export type GetAgenciesResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetAgencySalesResult = NonNullable<Awaited<ReturnType<typeof getAgencySales>>>;
export type GetAgencyMonthlySalesResult = NonNullable<Awaited<ReturnType<typeof getAgencyMonthlySales>>>;
export type GetAgencyUsersResult = NonNullable<Awaited<ReturnType<typeof getAgencyUsers>>>;
export type GetAuditGroupsResult = NonNullable<Awaited<ReturnType<typeof getAuditGroups>>>;
export type UpdateAuditStatusResult = NonNullable<Awaited<ReturnType<typeof updateAuditStatus>>>;
export type UpdateItemMarginRatesResult = NonNullable<Awaited<ReturnType<typeof updateItemMarginRates>>>;
export type UpdateShopItemTypeMarginRatesResult = NonNullable<
  Awaited<ReturnType<typeof updateShopItemTypeMarginRates>>
>;
export type GetConsoleUsersResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
export type GetUsersByPartialAccountIdentityResult = NonNullable<
  Awaited<ReturnType<typeof getUsersByPartialAccountIdentity>>
>;
export type UpdateContentBlockDetailResult = NonNullable<Awaited<ReturnType<typeof updateContentBlockDetail>>>;
export type GetUserContentBlocksResult = NonNullable<Awaited<ReturnType<typeof getUserContentBlocks>>>;
export type CreateContentBlockResult = NonNullable<Awaited<ReturnType<typeof createContentBlock>>>;
export type GetProfileResult = NonNullable<Awaited<ReturnType<typeof getProfile>>>;
export type CreateProxyAccessTokenResult = NonNullable<Awaited<ReturnType<typeof createProxyAccessToken>>>;
export type GetUserResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserMonthlySalesResult = NonNullable<Awaited<ReturnType<typeof getUserMonthlySales>>>;
export type GetUserTransactionResult = NonNullable<Awaited<ReturnType<typeof getUserTransaction>>>;
