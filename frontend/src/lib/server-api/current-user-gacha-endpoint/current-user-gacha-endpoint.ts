/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { CreateGachaItemRequest, ItemResponseBody, UpdateGachaItemRequest } from '../shop-api.schemas';

/**
 * @summary Create Gacha Item
 */
export const createGachaItem = (createGachaItemRequest: CreateGachaItemRequest) => {
  return customServerInstance<ItemResponseBody>({
    url: `/shops/current/gacha`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createGachaItemRequest,
  });
};
/**
 * @summary Update Gacha Item
 */
export const updateGachaItem = (itemId: number, updateGachaItemRequest: UpdateGachaItemRequest) => {
  return customServerInstance<ItemResponseBody>({
    url: `/shops/current/gacha/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateGachaItemRequest,
  });
};
export type CreateGachaItemResult = NonNullable<Awaited<ReturnType<typeof createGachaItem>>>;
export type UpdateGachaItemResult = NonNullable<Awaited<ReturnType<typeof updateGachaItem>>>;
