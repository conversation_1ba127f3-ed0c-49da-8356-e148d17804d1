/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  CreateSingleOrderRequest,
  FinalizeCreditCard3DSecureRequest,
  SingleOrderResponseBody,
} from '../shop-api.schemas';

/**
 * @summary Create Order
 */
export const createOrder = (createSingleOrderRequest: CreateSingleOrderRequest) => {
  return customServerInstance<SingleOrderResponseBody>({
    url: `/single-order`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createSingleOrderRequest,
  });
};
/**
 * @summary Finalize Credit Card 3 D Secure Single Order
 */
export const finalizeCreditCard3DSecureSingleOrder = (
  finalizeCreditCard3DSecureRequest: FinalizeCreditCard3DSecureRequest,
) => {
  return customServerInstance<void>({
    url: `/single-order/finalize-credit-card-3d-secure`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: finalizeCreditCard3DSecureRequest,
  });
};
export type CreateOrderResult = NonNullable<Awaited<ReturnType<typeof createOrder>>>;
export type FinalizeCreditCard3DSecureSingleOrderResult = NonNullable<
  Awaited<ReturnType<typeof finalizeCreditCard3DSecureSingleOrder>>
>;
