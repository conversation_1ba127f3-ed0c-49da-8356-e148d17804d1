/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { CreateGachaItemRequest, ItemResponseBody, UpdateGachaItemRequest } from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Create Gacha Item
 */
export const createGachaItem = (createGachaItemRequest: CreateGachaItemRequest) => {
  return customClientInstance<ItemResponseBody>({
    url: `/shops/current/gacha`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createGachaItemRequest,
  });
};

export const getCreateGachaItemMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateGachaItemRequest }): Promise<ItemResponseBody> => {
    return createGachaItem(arg);
  };
};
export const getCreateGachaItemMutationKey = () => [`/shops/current/gacha`] as const;

export type CreateGachaItemMutationResult = NonNullable<Awaited<ReturnType<typeof createGachaItem>>>;
export type CreateGachaItemMutationError = void;

/**
 * @summary Create Gacha Item
 */
export const useCreateGachaItem = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createGachaItem>>,
    TError,
    Key,
    CreateGachaItemRequest,
    Awaited<ReturnType<typeof createGachaItem>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateGachaItemMutationKey();
  const swrFn = getCreateGachaItemMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Gacha Item
 */
export const updateGachaItem = (itemId: number, updateGachaItemRequest: UpdateGachaItemRequest) => {
  return customClientInstance<ItemResponseBody>({
    url: `/shops/current/gacha/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateGachaItemRequest,
  });
};

export const getUpdateGachaItemMutationFetcher = (itemId: number) => {
  return (_: Key, { arg }: { arg: UpdateGachaItemRequest }): Promise<ItemResponseBody> => {
    return updateGachaItem(itemId, arg);
  };
};
export const getUpdateGachaItemMutationKey = (itemId: number) => [`/shops/current/gacha/${itemId}`] as const;

export type UpdateGachaItemMutationResult = NonNullable<Awaited<ReturnType<typeof updateGachaItem>>>;
export type UpdateGachaItemMutationError = void;

/**
 * @summary Update Gacha Item
 */
export const useUpdateGachaItem = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateGachaItem>>,
      TError,
      Key,
      UpdateGachaItemRequest,
      Awaited<ReturnType<typeof updateGachaItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateGachaItemMutationKey(itemId);
  const swrFn = getUpdateGachaItemMutationFetcher(itemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
