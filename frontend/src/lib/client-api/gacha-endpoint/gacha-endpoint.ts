/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  BadgeRankingResponseBody,
  CompleteBadgeResponseBody,
  GachaPullResponseBody,
  GachaPullableCountResponseBody,
  PullGachaRequest,
  PurchasedItemReceivedFilesResponseBody,
} from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Pull
 */
export const pull = (pullGachaRequest: PullGachaRequest) => {
  return customClientInstance<GachaPullResponseBody>({
    url: `/shops/gacha/pull`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: pullGachaRequest,
  });
};

export const getPullMutationFetcher = () => {
  return (_: Key, { arg }: { arg: PullGachaRequest }): Promise<GachaPullResponseBody> => {
    return pull(arg);
  };
};
export const getPullMutationKey = () => [`/shops/gacha/pull`] as const;

export type PullMutationResult = NonNullable<Awaited<ReturnType<typeof pull>>>;
export type PullMutationError = void;

/**
 * @summary Pull
 */
export const usePull = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof pull>>,
    TError,
    Key,
    PullGachaRequest,
    Awaited<ReturnType<typeof pull>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getPullMutationKey();
  const swrFn = getPullMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Purchased Item Received Files
 */
export const getPurchasedItemReceivedFiles = (purchasedItemId: number) => {
  return customClientInstance<PurchasedItemReceivedFilesResponseBody>({
    url: `/shops/gacha/purchased-items/${purchasedItemId}/received-files`,
    method: 'GET',
  });
};

export const getGetPurchasedItemReceivedFilesKey = (purchasedItemId: number) =>
  [`/shops/gacha/purchased-items/${purchasedItemId}/received-files`] as const;

export type GetPurchasedItemReceivedFilesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getPurchasedItemReceivedFiles>>
>;
export type GetPurchasedItemReceivedFilesQueryError = void;

/**
 * @summary Get Purchased Item Received Files
 */
export const useGetPurchasedItemReceivedFiles = <TError = void>(
  purchasedItemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getPurchasedItemReceivedFiles>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!purchasedItemId;
  const swrKey =
    swrOptions?.swrKey ?? (() => (isEnabled ? getGetPurchasedItemReceivedFilesKey(purchasedItemId) : null));
  const swrFn = () => getPurchasedItemReceivedFiles(purchasedItemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Complete Badge
 */
export const getCompleteBadge = (itemId: number) => {
  return customClientInstance<CompleteBadgeResponseBody>({
    url: `/shops/gacha/${itemId}/complete-badge`,
    method: 'GET',
  });
};

export const getGetCompleteBadgeKey = (itemId: number) => [`/shops/gacha/${itemId}/complete-badge`] as const;

export type GetCompleteBadgeQueryResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadge>>>;
export type GetCompleteBadgeQueryError = void;

/**
 * @summary Get Complete Badge
 */
export const useGetCompleteBadge = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCompleteBadge>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCompleteBadgeKey(itemId) : null));
  const swrFn = () => getCompleteBadge(itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Complete Badge Ranking
 */
export const getCompleteBadgeRanking = (itemId: number) => {
  return customClientInstance<BadgeRankingResponseBody>({
    url: `/shops/gacha/${itemId}/complete-badge-ranking`,
    method: 'GET',
  });
};

export const getGetCompleteBadgeRankingKey = (itemId: number) =>
  [`/shops/gacha/${itemId}/complete-badge-ranking`] as const;

export type GetCompleteBadgeRankingQueryResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadgeRanking>>>;
export type GetCompleteBadgeRankingQueryError = void;

/**
 * @summary Get Complete Badge Ranking
 */
export const useGetCompleteBadgeRanking = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCompleteBadgeRanking>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCompleteBadgeRankingKey(itemId) : null));
  const swrFn = () => getCompleteBadgeRanking(itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Pullable Gacha Count
 */
export const getPullableGachaCount = (itemId: number) => {
  return customClientInstance<GachaPullableCountResponseBody>({
    url: `/shops/gacha/${itemId}/pullable-count`,
    method: 'GET',
  });
};

export const getGetPullableGachaCountKey = (itemId: number) => [`/shops/gacha/${itemId}/pullable-count`] as const;

export type GetPullableGachaCountQueryResult = NonNullable<Awaited<ReturnType<typeof getPullableGachaCount>>>;
export type GetPullableGachaCountQueryError = void;

/**
 * @summary Get Pullable Gacha Count
 */
export const useGetPullableGachaCount = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getPullableGachaCount>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetPullableGachaCountKey(itemId) : null));
  const swrFn = () => getPullableGachaCount(itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
