/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customFanmeInstance } from '../../../../config/custom-fanme-instance';
import type {
  CallbackParams,
  CreateContentBlockRequest,
  CreateContentBlockResponse,
  CreateContentWithDetailRequest1,
  DeleteContentBlockResponse,
  FanmeCustomerResponseBody,
  FanmeParams,
  GetContentBlockResponse,
  GetContentBlocksParams,
  GetProfileResponse,
  MoveContentBlockDetailRequest,
  MoveContentBlockDetailResponse,
  MoveContentBlockRequest,
  MoveContentBlockResponse,
  SaveFanmeCustomerRequest,
  SuggestAddressResponseBody,
  ToggleContentBlockDisplayableRequest,
  ToggleContentBlockDisplayableResponse,
  UpdateContentBlockDetailRequest1,
  UpdateContentBlockDetailResponse,
  UpdateProfileCoverImageRequest,
  UpdateProfileCoverImageResponse,
  UpdateProfileCoverRequest,
  UpdateProfileCoverResponse,
  UpdateProfileRequest,
  UpdateProfileResponse,
  UpsertUserTutorialRequest,
  UserCurrentEntryCampaignsResponseBody,
  UserTutorialResponseBody,
} from '../fanme-api.schemas';
import type { Arguments, Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Suggest Address
 */
export const suggestAddress = (postalCode: string) => {
  return customFanmeInstance<SuggestAddressResponseBody>({
    url: `/address/suggestion/postal-code/${postalCode}`,
    method: 'GET',
  });
};

export const getSuggestAddressKey = (postalCode: string) => [`/address/suggestion/postal-code/${postalCode}`] as const;

export type SuggestAddressQueryResult = NonNullable<Awaited<ReturnType<typeof suggestAddress>>>;
export type SuggestAddressQueryError = void;

/**
 * @summary Suggest Address
 */
export const useSuggestAddress = <TError = void>(
  postalCode: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof suggestAddress>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!postalCode;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getSuggestAddressKey(postalCode) : null));
  const swrFn = () => suggestAddress(postalCode);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * OAuth認証を開始し、認証プロバイダへのリダイレクト先URLを取得するエンドポイント
 * @summary Fanme OAuth認証開始
 */
export const fanme = (params?: FanmeParams) => {
  return customFanmeInstance<void>({ url: `/auth/fanme`, method: 'GET', params });
};

export const getFanmeKey = (params?: FanmeParams) => [`/auth/fanme`, ...(params ? [params] : [])] as const;

export type FanmeQueryResult = NonNullable<Awaited<ReturnType<typeof fanme>>>;
export type FanmeQueryError = unknown;

/**
 * @summary Fanme OAuth認証開始
 */
export const useFanme = <TError = unknown>(
  params?: FanmeParams,
  options?: { swr?: SWRConfiguration<Awaited<ReturnType<typeof fanme>>, TError> & { swrKey?: Key; enabled?: boolean } },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getFanmeKey(params) : null));
  const swrFn = () => fanme(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * OAuthプロバイダからのコールバックを受け取り、認証処理を実行する
 * @summary OAuthコールバック処理
 */
export const callback = (params: CallbackParams) => {
  return customFanmeInstance<void>({ url: `/auth/fanme/callback`, method: 'GET', params });
};

export const getCallbackKey = (params: CallbackParams) =>
  [`/auth/fanme/callback`, ...(params ? [params] : [])] as const;

export type CallbackQueryResult = NonNullable<Awaited<ReturnType<typeof callback>>>;
export type CallbackQueryError = void;

/**
 * @summary OAuthコールバック処理
 */
export const useCallback = <TError = void>(
  params: CallbackParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof callback>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getCallbackKey(params) : null));
  const swrFn = () => callback(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Content Blocks
 */
export const getContentBlocks = (params: GetContentBlocksParams) => {
  return customFanmeInstance<GetContentBlockResponse>({ url: `/fanme/content_blocks`, method: 'GET', params });
};

export const getGetContentBlocksKey = (params: GetContentBlocksParams) =>
  [`/fanme/content_blocks`, ...(params ? [params] : [])] as const;

export type GetContentBlocksQueryResult = NonNullable<Awaited<ReturnType<typeof getContentBlocks>>>;
export type GetContentBlocksQueryError = unknown;

/**
 * @summary Get Content Blocks
 */
export const useGetContentBlocks = <TError = unknown>(
  params: GetContentBlocksParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getContentBlocks>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetContentBlocksKey(params) : null));
  const swrFn = () => getContentBlocks(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Content Block
 */
export const createContentBlock = (createContentBlockRequest: CreateContentBlockRequest) => {
  return customFanmeInstance<CreateContentBlockResponse>({
    url: `/fanme/content_blocks/create`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createContentBlockRequest,
  });
};

export const getCreateContentBlockMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateContentBlockRequest }): Promise<CreateContentBlockResponse> => {
    return createContentBlock(arg);
  };
};
export const getCreateContentBlockMutationKey = () => [`/fanme/content_blocks/create`] as const;

export type CreateContentBlockMutationResult = NonNullable<Awaited<ReturnType<typeof createContentBlock>>>;
export type CreateContentBlockMutationError = void;

/**
 * @summary Create Content Block
 */
export const useCreateContentBlock = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createContentBlock>>,
    TError,
    Key,
    CreateContentBlockRequest,
    Awaited<ReturnType<typeof createContentBlock>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateContentBlockMutationKey();
  const swrFn = getCreateContentBlockMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create With Detail
 */
export const createWithDetail = (createContentWithDetailRequest1: CreateContentWithDetailRequest1) => {
  return customFanmeInstance<void>({
    url: `/fanme/content_blocks/create_with_detail`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createContentWithDetailRequest1,
  });
};

export const getCreateWithDetailMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateContentWithDetailRequest1 }): Promise<void> => {
    return createWithDetail(arg);
  };
};
export const getCreateWithDetailMutationKey = () => [`/fanme/content_blocks/create_with_detail`] as const;

export type CreateWithDetailMutationResult = NonNullable<Awaited<ReturnType<typeof createWithDetail>>>;
export type CreateWithDetailMutationError = void;

/**
 * @summary Create With Detail
 */
export const useCreateWithDetail = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createWithDetail>>,
    TError,
    Key,
    CreateContentWithDetailRequest1,
    Awaited<ReturnType<typeof createWithDetail>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateWithDetailMutationKey();
  const swrFn = getCreateWithDetailMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Current User Content Blocks
 */
export const getCurrentUserContentBlocks = () => {
  return customFanmeInstance<GetContentBlockResponse>({ url: `/fanme/content_blocks/current`, method: 'GET' });
};

export const getGetCurrentUserContentBlocksKey = () => [`/fanme/content_blocks/current`] as const;

export type GetCurrentUserContentBlocksQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCurrentUserContentBlocks>>
>;
export type GetCurrentUserContentBlocksQueryError = void;

/**
 * @summary Get Current User Content Blocks
 */
export const useGetCurrentUserContentBlocks = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUserContentBlocks>>, TError> & {
    swrKey?: Key;
    enabled?: boolean;
  };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserContentBlocksKey() : null));
  const swrFn = () => getCurrentUserContentBlocks();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Content Block Detail
 */
export const updateContentBlockDetail = (updateContentBlockDetailRequest1: UpdateContentBlockDetailRequest1) => {
  return customFanmeInstance<UpdateContentBlockDetailResponse>({
    url: `/fanme/content_blocks/detail`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateContentBlockDetailRequest1,
  });
};

export const getUpdateContentBlockDetailMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateContentBlockDetailRequest1 }): Promise<UpdateContentBlockDetailResponse> => {
    return updateContentBlockDetail(arg);
  };
};
export const getUpdateContentBlockDetailMutationKey = () => [`/fanme/content_blocks/detail`] as const;

export type UpdateContentBlockDetailMutationResult = NonNullable<Awaited<ReturnType<typeof updateContentBlockDetail>>>;
export type UpdateContentBlockDetailMutationError = void;

/**
 * @summary Update Content Block Detail
 */
export const useUpdateContentBlockDetail = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateContentBlockDetail>>,
    TError,
    Key,
    UpdateContentBlockDetailRequest1,
    Awaited<ReturnType<typeof updateContentBlockDetail>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateContentBlockDetailMutationKey();
  const swrFn = getUpdateContentBlockDetailMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Move Content Block Detail
 */
export const moveContentBlockDetail = (moveContentBlockDetailRequest: MoveContentBlockDetailRequest) => {
  return customFanmeInstance<MoveContentBlockDetailResponse>({
    url: `/fanme/content_blocks/detail/move`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: moveContentBlockDetailRequest,
  });
};

export const getMoveContentBlockDetailMutationFetcher = () => {
  return (_: Key, { arg }: { arg: MoveContentBlockDetailRequest }): Promise<MoveContentBlockDetailResponse> => {
    return moveContentBlockDetail(arg);
  };
};
export const getMoveContentBlockDetailMutationKey = () => [`/fanme/content_blocks/detail/move`] as const;

export type MoveContentBlockDetailMutationResult = NonNullable<Awaited<ReturnType<typeof moveContentBlockDetail>>>;
export type MoveContentBlockDetailMutationError = void;

/**
 * @summary Move Content Block Detail
 */
export const useMoveContentBlockDetail = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof moveContentBlockDetail>>,
    TError,
    Key,
    MoveContentBlockDetailRequest,
    Awaited<ReturnType<typeof moveContentBlockDetail>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getMoveContentBlockDetailMutationKey();
  const swrFn = getMoveContentBlockDetailMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Move Content Block
 */
export const moveContentBlock = (moveContentBlockRequest: MoveContentBlockRequest) => {
  return customFanmeInstance<MoveContentBlockResponse>({
    url: `/fanme/content_blocks/move`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: moveContentBlockRequest,
  });
};

export const getMoveContentBlockMutationFetcher = () => {
  return (_: Key, { arg }: { arg: MoveContentBlockRequest }): Promise<MoveContentBlockResponse> => {
    return moveContentBlock(arg);
  };
};
export const getMoveContentBlockMutationKey = () => [`/fanme/content_blocks/move`] as const;

export type MoveContentBlockMutationResult = NonNullable<Awaited<ReturnType<typeof moveContentBlock>>>;
export type MoveContentBlockMutationError = void;

/**
 * @summary Move Content Block
 */
export const useMoveContentBlock = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof moveContentBlock>>,
    TError,
    Key,
    MoveContentBlockRequest,
    Awaited<ReturnType<typeof moveContentBlock>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getMoveContentBlockMutationKey();
  const swrFn = getMoveContentBlockMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Toggle Content Block Displayable
 */
export const toggleContentBlockDisplayable = (
  toggleContentBlockDisplayableRequest: ToggleContentBlockDisplayableRequest,
) => {
  return customFanmeInstance<ToggleContentBlockDisplayableResponse>({
    url: `/fanme/content_blocks/toggle_displayable`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: toggleContentBlockDisplayableRequest,
  });
};

export const getToggleContentBlockDisplayableMutationFetcher = () => {
  return (
    _: Key,
    { arg }: { arg: ToggleContentBlockDisplayableRequest },
  ): Promise<ToggleContentBlockDisplayableResponse> => {
    return toggleContentBlockDisplayable(arg);
  };
};
export const getToggleContentBlockDisplayableMutationKey = () => [`/fanme/content_blocks/toggle_displayable`] as const;

export type ToggleContentBlockDisplayableMutationResult = NonNullable<
  Awaited<ReturnType<typeof toggleContentBlockDisplayable>>
>;
export type ToggleContentBlockDisplayableMutationError = void;

/**
 * @summary Toggle Content Block Displayable
 */
export const useToggleContentBlockDisplayable = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof toggleContentBlockDisplayable>>,
    TError,
    Key,
    ToggleContentBlockDisplayableRequest,
    Awaited<ReturnType<typeof toggleContentBlockDisplayable>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getToggleContentBlockDisplayableMutationKey();
  const swrFn = getToggleContentBlockDisplayableMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Delete Content Block
 */
export const deleteContentBlock = (contentBlockId: number) => {
  return customFanmeInstance<DeleteContentBlockResponse>({
    url: `/fanme/content_blocks/${contentBlockId}`,
    method: 'DELETE',
  });
};

export const getDeleteContentBlockMutationFetcher = (contentBlockId: number) => {
  return (_: Key, __: { arg: Arguments }): Promise<DeleteContentBlockResponse> => {
    return deleteContentBlock(contentBlockId);
  };
};
export const getDeleteContentBlockMutationKey = (contentBlockId: number) =>
  [`/fanme/content_blocks/${contentBlockId}`] as const;

export type DeleteContentBlockMutationResult = NonNullable<Awaited<ReturnType<typeof deleteContentBlock>>>;
export type DeleteContentBlockMutationError = void;

/**
 * @summary Delete Content Block
 */
export const useDeleteContentBlock = <TError = void>(
  contentBlockId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof deleteContentBlock>>,
      TError,
      Key,
      Arguments,
      Awaited<ReturnType<typeof deleteContentBlock>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getDeleteContentBlockMutationKey(contentBlockId);
  const swrFn = getDeleteContentBlockMutationFetcher(contentBlockId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Save Fanme Customer
 */
export const saveFanmeCustomer = (saveFanmeCustomerRequest: SaveFanmeCustomerRequest) => {
  return customFanmeInstance<void>({
    url: `/fanme/fanme-customers`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: saveFanmeCustomerRequest,
  });
};

export const getSaveFanmeCustomerMutationFetcher = () => {
  return (_: Key, { arg }: { arg: SaveFanmeCustomerRequest }): Promise<void> => {
    return saveFanmeCustomer(arg);
  };
};
export const getSaveFanmeCustomerMutationKey = () => [`/fanme/fanme-customers`] as const;

export type SaveFanmeCustomerMutationResult = NonNullable<Awaited<ReturnType<typeof saveFanmeCustomer>>>;
export type SaveFanmeCustomerMutationError = void;

/**
 * @summary Save Fanme Customer
 */
export const useSaveFanmeCustomer = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof saveFanmeCustomer>>,
    TError,
    Key,
    SaveFanmeCustomerRequest,
    Awaited<ReturnType<typeof saveFanmeCustomer>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getSaveFanmeCustomerMutationKey();
  const swrFn = getSaveFanmeCustomerMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Fanme Customer
 */
export const getFanmeCustomer = () => {
  return customFanmeInstance<FanmeCustomerResponseBody>({ url: `/fanme/fanme-customers`, method: 'GET' });
};

export const getGetFanmeCustomerKey = () => [`/fanme/fanme-customers`] as const;

export type GetFanmeCustomerQueryResult = NonNullable<Awaited<ReturnType<typeof getFanmeCustomer>>>;
export type GetFanmeCustomerQueryError = void;

/**
 * @summary Get Fanme Customer
 */
export const useGetFanmeCustomer = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getFanmeCustomer>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetFanmeCustomerKey() : null));
  const swrFn = () => getFanmeCustomer();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Profile
 */
export const updateProfile = (updateProfileRequest: UpdateProfileRequest) => {
  return customFanmeInstance<UpdateProfileResponse>({
    url: `/fanme/profiles/current`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateProfileRequest,
  });
};

export const getUpdateProfileMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateProfileRequest }): Promise<UpdateProfileResponse> => {
    return updateProfile(arg);
  };
};
export const getUpdateProfileMutationKey = () => [`/fanme/profiles/current`] as const;

export type UpdateProfileMutationResult = NonNullable<Awaited<ReturnType<typeof updateProfile>>>;
export type UpdateProfileMutationError = void;

/**
 * @summary Update Profile
 */
export const useUpdateProfile = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateProfile>>,
    TError,
    Key,
    UpdateProfileRequest,
    Awaited<ReturnType<typeof updateProfile>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateProfileMutationKey();
  const swrFn = getUpdateProfileMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Current Profile
 */
export const getCurrentProfile = () => {
  return customFanmeInstance<GetProfileResponse>({ url: `/fanme/profiles/current`, method: 'GET' });
};

export const getGetCurrentProfileKey = () => [`/fanme/profiles/current`] as const;

export type GetCurrentProfileQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentProfile>>>;
export type GetCurrentProfileQueryError = void;

/**
 * @summary Get Current Profile
 */
export const useGetCurrentProfile = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentProfile>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentProfileKey() : null));
  const swrFn = () => getCurrentProfile();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Profile Cover
 */
export const updateProfileCover = (updateProfileCoverRequest: UpdateProfileCoverRequest) => {
  return customFanmeInstance<UpdateProfileCoverResponse>({
    url: `/fanme/profiles/current/cover`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateProfileCoverRequest,
  });
};

export const getUpdateProfileCoverMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateProfileCoverRequest }): Promise<UpdateProfileCoverResponse> => {
    return updateProfileCover(arg);
  };
};
export const getUpdateProfileCoverMutationKey = () => [`/fanme/profiles/current/cover`] as const;

export type UpdateProfileCoverMutationResult = NonNullable<Awaited<ReturnType<typeof updateProfileCover>>>;
export type UpdateProfileCoverMutationError = void;

/**
 * @summary Update Profile Cover
 */
export const useUpdateProfileCover = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateProfileCover>>,
    TError,
    Key,
    UpdateProfileCoverRequest,
    Awaited<ReturnType<typeof updateProfileCover>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateProfileCoverMutationKey();
  const swrFn = getUpdateProfileCoverMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Profile Cover Image
 */
export const updateProfileCoverImage = (updateProfileCoverImageRequest: UpdateProfileCoverImageRequest) => {
  return customFanmeInstance<UpdateProfileCoverImageResponse>({
    url: `/fanme/profiles/current/cover/image`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateProfileCoverImageRequest,
  });
};

export const getUpdateProfileCoverImageMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateProfileCoverImageRequest }): Promise<UpdateProfileCoverImageResponse> => {
    return updateProfileCoverImage(arg);
  };
};
export const getUpdateProfileCoverImageMutationKey = () => [`/fanme/profiles/current/cover/image`] as const;

export type UpdateProfileCoverImageMutationResult = NonNullable<Awaited<ReturnType<typeof updateProfileCoverImage>>>;
export type UpdateProfileCoverImageMutationError = void;

/**
 * @summary Update Profile Cover Image
 */
export const useUpdateProfileCoverImage = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateProfileCoverImage>>,
    TError,
    Key,
    UpdateProfileCoverImageRequest,
    Awaited<ReturnType<typeof updateProfileCoverImage>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateProfileCoverImageMutationKey();
  const swrFn = getUpdateProfileCoverImageMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Profile
 */
export const getProfile = (creatorAccountIdentity: string) => {
  return customFanmeInstance<GetProfileResponse>({ url: `/fanme/profiles/${creatorAccountIdentity}`, method: 'GET' });
};

export const getGetProfileKey = (creatorAccountIdentity: string) =>
  [`/fanme/profiles/${creatorAccountIdentity}`] as const;

export type GetProfileQueryResult = NonNullable<Awaited<ReturnType<typeof getProfile>>>;
export type GetProfileQueryError = unknown;

/**
 * @summary Get Profile
 */
export const useGetProfile = <TError = unknown>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getProfile>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetProfileKey(creatorAccountIdentity) : null));
  const swrFn = () => getProfile(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Current User
 */
export const getCurrentUser = () => {
  return customFanmeInstance<unknown>({ url: `/fanme/users/current`, method: 'GET' });
};

export const getGetCurrentUserKey = () => [`/fanme/users/current`] as const;

export type GetCurrentUserQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentUser>>>;
export type GetCurrentUserQueryError = void;

/**
 * @summary Get Current User
 */
export const useGetCurrentUser = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUser>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserKey() : null));
  const swrFn = () => getCurrentUser();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Current User Entry Campaigns
 */
export const getCurrentUserEntryCampaigns = () => {
  return customFanmeInstance<UserCurrentEntryCampaignsResponseBody>({
    url: `/fanme/users/current/entry_campaigns`,
    method: 'GET',
  });
};

export const getGetCurrentUserEntryCampaignsKey = () => [`/fanme/users/current/entry_campaigns`] as const;

export type GetCurrentUserEntryCampaignsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCurrentUserEntryCampaigns>>
>;
export type GetCurrentUserEntryCampaignsQueryError = void;

/**
 * @summary Get Current User Entry Campaigns
 */
export const useGetCurrentUserEntryCampaigns = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUserEntryCampaigns>>, TError> & {
    swrKey?: Key;
    enabled?: boolean;
  };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserEntryCampaignsKey() : null));
  const swrFn = () => getCurrentUserEntryCampaigns();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Upsert User Tutorial
 */
export const upsertUserTutorial = (flagKey: string, upsertUserTutorialRequest: UpsertUserTutorialRequest) => {
  return customFanmeInstance<UserTutorialResponseBody>({
    url: `/fanme/users/current/user_tutorials/${flagKey}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: upsertUserTutorialRequest,
  });
};

export const getUpsertUserTutorialMutationFetcher = (flagKey: string) => {
  return (_: Key, { arg }: { arg: UpsertUserTutorialRequest }): Promise<UserTutorialResponseBody> => {
    return upsertUserTutorial(flagKey, arg);
  };
};
export const getUpsertUserTutorialMutationKey = (flagKey: string) =>
  [`/fanme/users/current/user_tutorials/${flagKey}`] as const;

export type UpsertUserTutorialMutationResult = NonNullable<Awaited<ReturnType<typeof upsertUserTutorial>>>;
export type UpsertUserTutorialMutationError = void;

/**
 * @summary Upsert User Tutorial
 */
export const useUpsertUserTutorial = <TError = void>(
  flagKey: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof upsertUserTutorial>>,
      TError,
      Key,
      UpsertUserTutorialRequest,
      Awaited<ReturnType<typeof upsertUserTutorial>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpsertUserTutorialMutationKey(flagKey);
  const swrFn = getUpsertUserTutorialMutationFetcher(flagKey);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User Tutorial
 */
export const getUserTutorial = (flagKey: string) => {
  return customFanmeInstance<UserTutorialResponseBody>({
    url: `/fanme/users/current/user_tutorials/${flagKey}`,
    method: 'GET',
  });
};

export const getGetUserTutorialKey = (flagKey: string) => [`/fanme/users/current/user_tutorials/${flagKey}`] as const;

export type GetUserTutorialQueryResult = NonNullable<Awaited<ReturnType<typeof getUserTutorial>>>;
export type GetUserTutorialQueryError = void;

/**
 * @summary Get User Tutorial
 */
export const useGetUserTutorial = <TError = void>(
  flagKey: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUserTutorial>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!flagKey;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserTutorialKey(flagKey) : null));
  const swrFn = () => getUserTutorial(flagKey);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User
 */
export const getUser = (userUuid: string) => {
  return customFanmeInstance<unknown>({ url: `/fanme/users/${userUuid}`, method: 'GET' });
};

export const getGetUserKey = (userUuid: string) => [`/fanme/users/${userUuid}`] as const;

export type GetUserQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserQueryError = unknown;

/**
 * @summary Get User
 */
export const useGetUser = <TError = unknown>(
  userUuid: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUser>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!userUuid;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserKey(userUuid) : null));
  const swrFn = () => getUser(userUuid);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
