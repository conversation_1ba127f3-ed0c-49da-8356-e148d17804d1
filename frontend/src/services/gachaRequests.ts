import HttpClient from '@/utils/request';
import { ApiResponse } from '@/types/api';
import { PullGachaResponse } from '@/types/gacha';

export const gachaRequests = {
  pullGacha: async (data: { itemId: number; identityId: string }): Promise<ApiResponse<PullGachaResponse>> => {
    const response = await HttpClient.post<PullGachaResponse>(`/api/shop/gacha/${data.itemId}/pull`, data);
    return response;
  },
};
