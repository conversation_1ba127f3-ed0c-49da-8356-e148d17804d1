'use client';

import { useEffect, useRef, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useShallow } from 'zustand/react/shallow';
import DetailItemsSection from '@/components/containers/DetailItemsSection';
import DetailMainInfo from '@/components/containers/DetailMainInfo';
import ThumbnailSwiper from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import { useExhibitsStore } from '@/store/useExhibit';
import { isDigitalBundle } from '@/utils/itemTypes';
import { ITEM_TYPE } from '@/types/shopItem';

const DigitalBundlePreview = () => {
  const router = useRouter();
  const exhibits = useExhibitsStore(useShallow((state) => state.exhibits));
  const midDiv = useRef<HTMLDivElement>(null);
  const params = useParams();
  const identityId = params.identityId as string;
  const itemId = params.id as string;
  const itemType = ITEM_TYPE.digitalBundle;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);

  useEffect(() => {
    const isPreviewDataMissing = !exhibitItem || !exhibitItem?.title;
    if (isPreviewDataMissing) {
      router.back();
    }
  }, [exhibitItem, router]);
  const { title, description, limited, period, thumbnail, thumbnailRatio, priceSet, remainingAmount, discount } =
    exhibitItem ?? {};
  const singleSale = isDigitalBundle(exhibitItem) ? (exhibitItem.singleSale ?? false) : false;
  const itemFiles = isDigitalBundle(exhibitItem) ? (exhibitItem.itemFiles ?? []) : [];
  const benefits = isDigitalBundle(exhibitItem) ? (exhibitItem.benefits ?? []) : [];

  const discountRate = discount?.percentage || 0;
  const currentPrice = Math.floor((priceSet ?? 0) - (priceSet ?? 0) * (discountRate / 100));
  const thumbnailProps = {
    shopName: '',
    creatorName: '',
    itemId,
    isSingleSale: singleSale,
    isPreview: true,
    itemType,
    currentPrice,
  };
  const detailMainInfoProps = {
    description,
    limited,
    period,
    itemId,
    isPreview: true,
  };
  const detailItemsSectionProps = {
    itemFiles,
    thumbnail: thumbnail!,
    thumbnailRatio: thumbnailRatio!,
    title: title!,
    price: priceSet!,
    discountRate,
    currentPrice,
    discount,
    benefits: benefits,
    isSoldOut: remainingAmount === 0,
    singleSale: singleSale!,
    period,
    isPreview: true,
    identityId,
    forceShowItemOption: true,
    itemType,
  };
  return (
    <div ref={midDiv}>
      <ThumbnailSwiper props={thumbnailProps} />
      <DetailMainInfo props={detailMainInfoProps} />
      <DetailItemsSection props={detailItemsSectionProps} />
      <DetailPageInstruction />
    </div>
  );
};

export default DigitalBundlePreview;
