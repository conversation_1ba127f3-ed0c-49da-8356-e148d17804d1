'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import GachaCollectionSection from '@/components/containers/GachaCollectionSection';
import GachaCompleteProgressBar from '@/components/containers/GachaCompleteProgressBar';
import GachaCompleteSection from '@/components/containers/GachaCompleteSection';
import GachaPullSection from '@/components/containers/GachaPullSection';
import GachaResultPreview from '@/components/containers/GachaResultPreview';
import { getPullableGachaCount } from '@/lib/client-api/gacha-endpoint/gacha-endpoint';
import { GachaResultAnimation } from './gacha-result-animation';
import { usePullGacha } from '@/hooks/use-gacha-result';
import { hasSAwardType, getResultAnimationSrc } from '@/utils/gacha-helpers';
import { isPrintGacha } from '@/utils/itemTypes';

type ClientGachaResultProps = {
  itemId: string;
  identityId: string;
};

const ClientGachaResult = ({ itemId, identityId }: ClientGachaResultProps) => {
  const isInitialMount = useRef(true);
  const router = useRouter();

  const [showAnimation, setShowAnimation] = useState(true);
  const { isLoading, error, currentPulledFiles, selectedAwardFileId, gachaCollection, pullGacha } = usePullGacha(
    itemId,
    identityId,
  );

  const hasS = hasSAwardType(currentPulledFiles);
  const pulledCount = currentPulledFiles.reduce((sum, file) => sum + (file.receivedFileCount ?? 0), 0);
  const resultAnimationSrc = getResultAnimationSrc(hasS, pulledCount);

  useEffect(() => {
    if (!itemId || !identityId) return;
    if (!isInitialMount.current) return;
    isInitialMount.current = false;

    (async () => {
      try {
        const response = await getPullableGachaCount(Number(itemId));
        const remainingPullCount = response.data?.item?.remainingPullCount ?? 0;
        if (remainingPullCount > 0) {
          pullGacha();
        } else {
          router.replace(`/${identityId}/item/${itemId}`);
        }
      } catch (e) {
        console.log(e);
        router.replace(`/${identityId}/item/${itemId}`);
      }
    })();
  }, [itemId, identityId, pullGacha, router]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 size-12 animate-spin rounded-full border-4 border-gray-300 border-t-secondary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    throw new Error(error);
  }

  if (showAnimation && currentPulledFiles.length > 0) {
    return (
      <GachaResultAnimation
        src={resultAnimationSrc}
        onAnimationEnd={() => {
          setShowAnimation(false);
        }}
      />
    );
  }

  if (!gachaCollection) return null;

  const {
    price,
    currentPrice = 0,
    discount,
    period,
    available,
    itemFiles,
    duplicatedCount,
    collectedUniqueItemsCount,
    isCompleted,
    isDuplicated,
    remainingUniquePullCount,
  } = gachaCollection;

  return (
    <div className="min-h-screen max-w-120 bg-gray-100 bg-[url('/shop/images/gacha/zukanbg.webp')] bg-center bg-repeat">
      <GachaResultPreview
        props={{ pulledFiles: currentPulledFiles, selectedAwardFileId, isPrintGacha: isPrintGacha(gachaCollection) }}
      />
      <div className="mb-10">
        <GachaPullSection
          props={{
            price,
            currentPrice,
            discount,
            period,
            available: !!available,
            itemId,
            identityId,
            isCompleted,
            isDuplicated,
            remainingUniquePullCount,
            itemType: gachaCollection.itemType,
          }}
        />
      </div>
      <GachaCompleteSection
        props={{
          isPreview: false,
          totalGachaCount: itemFiles.length,
          collectedUniqueItemsCount,
          isCompleted,
          thumbnail: gachaCollection.thumbnail,
          itemId: Number(itemId),
        }}
      />
      <GachaCollectionSection isPreview={false} itemFiles={itemFiles} isPrintGacha={isPrintGacha(gachaCollection)} />
      <GachaCompleteProgressBar props={{ itemFiles, duplicatedCount, isPreview: false }} />
    </div>
  );
};

export default ClientGachaResult;
