import ClientGachaResult from './_components/client-gacha-result';
import { getUserIdentityId } from '@/utils/base';

type Props = {
  params: {
    id: string;
    identityId: string;
  };
};

const ResultPage = ({ params }: Props) => {
  const itemId = params.id;
  const identityId = getUserIdentityId(params.identityId);

  return <ClientGachaResult itemId={itemId} identityId={identityId} />;
};

export default ResultPage;
