import { useEffect, useRef } from 'react';
import type { SyntheticEvent } from 'react';
import ShopPublicVideo from '@/components/shop-public-video';

type GachaResultAnimationProps = {
  onAnimationEnd: () => void;
  src: string;
};

export const GachaResultAnimation = ({ onAnimationEnd, src }: GachaResultAnimationProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleEnded = () => {
      onAnimationEnd();
    };

    const handleError = () => {
      console.error('動画の読み込みに失敗しました');
      onAnimationEnd();
    };

    video.addEventListener('ended', handleEnded);
    video.addEventListener('error', handleError);

    // 明示的に再生を試みる
    const playVideo = async () => {
      try {
        await video.play();
        setTimeout(() => {
          video.pause();
          onAnimationEnd();
        }, 4000);
      } catch (error) {
        console.error('動画の再生に失敗しました:', error);
        onAnimationEnd();
      }
    };

    playVideo();

    return () => {
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('error', handleError);
    };
  }, [onAnimationEnd]);

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black">
      <ShopPublicVideo
        ref={videoRef}
        className="h-full w-full object-contain"
        src={src}
        playsInline
        autoPlay
        controls={false}
        muted={true}
        loop={false}
        preload="auto"
        onError={(e: SyntheticEvent) => console.error('動画要素のエラー:', e)}
      />
    </div>
  );
};
