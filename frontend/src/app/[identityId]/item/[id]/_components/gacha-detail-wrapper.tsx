'use client';
import { useEffect, useRef } from 'react';
import GachaCollectionSection from '@/components/containers/GachaCollectionSection';
import GachaCompleteProgressBar, {
  type GachaCompleteProgressBarProps,
} from '@/components/containers/GachaCompleteProgressBar';
import GachaCompleteSection, { type GachaCompleteSectionProps } from '@/components/containers/GachaCompleteSection';
import GachaDetailItemsSection, {
  type GachaDetailItemSectionProps,
} from '@/components/containers/GachaDetailItemSection';
import GachaDetailMainInfo, { type GachaDetailMainInfoProps } from '@/components/containers/GachaDetailMainInfo';
import GachaPullSection, { type GachaPullSectionProps } from '@/components/containers/GachaPullSection';
import ThumbnailSwiper, { type ThumbnailSwiperProps } from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import { useCheckPullableGachaStatus } from '@/hooks/use-check-pullable-gacha-status';
import { isDigitalGacha, isPrintGacha } from '@/utils/itemTypes';
import type { GachaItemFile } from '@/types/gacha';

type DigitalGachaDetailWrapperProps = {
  thumbnailProps: ThumbnailSwiperProps['props'];
  pullGachaProps: GachaPullSectionProps;
  gachaMainInfoProps: GachaDetailMainInfoProps;
  gachaItemsSectionProps: GachaDetailItemSectionProps;
  gachaCompleteSectionProps: GachaCompleteSectionProps;
  progressBarProps: GachaCompleteProgressBarProps;
};

const GachaDetailWrapper = ({
  thumbnailProps,
  pullGachaProps,
  gachaMainInfoProps,
  gachaItemsSectionProps,
  gachaCompleteSectionProps,
  progressBarProps,
}: DigitalGachaDetailWrapperProps) => {
  const { itemId, identityId } = pullGachaProps;
  const { checkPullableGachaStatus } = useCheckPullableGachaStatus(Number(itemId), identityId);
  const hasCheckedPullableGachaStatus = useRef(false);

  useEffect(() => {
    if (itemId && identityId && !hasCheckedPullableGachaStatus.current) {
      checkPullableGachaStatus();
      hasCheckedPullableGachaStatus.current = true;
    }
  }, [itemId, identityId, checkPullableGachaStatus]);

  return (
    <>
      <ThumbnailSwiper props={thumbnailProps} />
      <GachaPullSection props={pullGachaProps} />
      <GachaDetailMainInfo props={gachaMainInfoProps} />
      <GachaDetailItemsSection props={gachaItemsSectionProps} />
      <GachaCompleteSection props={gachaCompleteSectionProps} />
      <GachaCollectionSection
        isPreview={false}
        itemFiles={thumbnailProps.itemFiles as GachaItemFile[]}
        isPrintGacha={isPrintGacha(thumbnailProps)}
      />
      <GachaCompleteProgressBar props={progressBarProps} />
      <div className="mt-4">
        <DetailPageInstruction isDigital={isDigitalGacha(thumbnailProps)} />
      </div>
    </>
  );
};

export default GachaDetailWrapper;
