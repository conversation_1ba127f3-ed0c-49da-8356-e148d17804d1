import DetailItemsSection from '@/components/containers/DetailItemsSection';
import DetailMainInfo from '@/components/containers/DetailMainInfo';
import ThumbnailSwiper from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import type { ShopItemDetail } from '@/types/shopItem';

// Server Componentとしてデータフェッチを実装
type DigitalBundleDetailProps = {
  data: ShopItemDetail;
  itemId: string;
  identityId: string;
  shopName: string;
  creatorName: string;
};
const ChekiDetail = ({ data, itemId, identityId, shopName, creatorName }: DigitalBundleDetailProps) => {
  const {
    description,
    limited,
    limitedPerUser,
    period,
    itemFiles,
    thumbnail,
    thumbnailRatio,
    remainingAmount,
    title,
    price,
    currentPrice,
    discountRate,
    benefits,
    singleSale,
    samples,
    discount,
    isPurchased,
    isCheckout,
    purchasedCount,
    hasPassword,
    itemType,
  } = data.item;

  const isSoldOut = remainingAmount === 0;

  const thumbnailProps = {
    itemId,
    itemFiles,
    samples,
    title,
    priceSet: price,
    currentPrice,
    thumbnailRatio,
    thumbnail,
    discount,
    isPreview: false,
    isSingleSale: singleSale,
    shopName,
    creatorName,
    identityId,
    itemType,
  };

  const detailMainInfoProps = {
    description,
    limited,
    limitedPerUser,
    purchasedCount,
    leftStock: remainingAmount,
    period,
  };

  const detailItemsSectionProps = {
    itemFiles,
    itemType,
    thumbnail,
    thumbnailRatio,
    title,
    price,
    currentPrice,
    discountRate,
    discount,
    benefits,
    singleSale,
    identityId,
    itemId: Number(itemId),
    isPurchased,
    isCheckout,
    period,
    isSoldOut,
    hasPassword,
    forceShowItemOption: false,
  };
  return (
    <>
      <ThumbnailSwiper props={thumbnailProps} />
      <DetailMainInfo props={detailMainInfoProps} />
      <DetailItemsSection props={detailItemsSectionProps} />
      <DetailPageInstruction isDigital={false} />
    </>
  );
};

export default ChekiDetail;
