import { getCompleteBadgeRanking } from '@/lib/server-api/gacha-endpoint/gacha-endpoint';
import type { GetGachaCompleteBadgeRankingResponse } from '@/lib/server-api/shop-api.schemas';
import { EmptyRanking } from './empty-ranking';
import { StandardRankingListItem } from './standard-ranking-list-item';
import { TopRankingListItem } from './top-ranking-list-item';
import { getCurrentUser } from '@/app/actions/user';
import { getCookie } from '@/utils/cookie';
import type { CompleteTopRank } from '@/types/common';

const TOP_RANKING_THRESHOLD = 3;
const isTopRank = (rank: number): rank is CompleteTopRank => rank >= 1 && rank <= TOP_RANKING_THRESHOLD;

type RankingListProps = {
  itemId: string;
  identityId: string;
};

const RankingList = async ({ itemId, identityId }: RankingListProps) => {
  const token = await getCookie('fanme_token-http-only');

  const getCurrentUserUid = async (): Promise<string | null> => {
    if (!token) return null;

    try {
      const currentUserResponse = await getCurrentUser(token);
      if (!currentUserResponse.ok) {
        console.warn('Current user request failed:', currentUserResponse.status);
        return null;
      }

      const currentUserData = await currentUserResponse.json();
      return currentUserData?.data?.uid || null;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  };

  const [rankingResult, userUid] = await Promise.allSettled([
    getCompleteBadgeRanking(Number(itemId)),
    getCurrentUserUid(),
  ]);

  const ranking = rankingResult.status === 'fulfilled' ? rankingResult.value.data?.ranking || [] : [];
  const currentUserUid = userUid.status === 'fulfilled' ? userUid.value : null;

  if (!ranking || ranking.length === 0) {
    return <EmptyRanking />;
  }

  return (
    <div className="h-full overflow-y-auto px-2">
      <div className="space-y-3 pb-4">
        {ranking.map((entry: GetGachaCompleteBadgeRankingResponse, index: number) => {
          const rank = index + 1;
          const isCurrentUser = entry.userUid === currentUserUid;

          return isTopRank(rank) ? (
            <TopRankingListItem
              key={rank}
              entry={entry}
              rank={rank}
              itemId={itemId}
              identityId={identityId}
              isHighlighted={isCurrentUser}
            />
          ) : (
            <StandardRankingListItem
              key={rank}
              entry={entry}
              rank={rank}
              itemId={itemId}
              identityId={identityId}
              isHighlighted={isCurrentUser}
            />
          );
        })}
      </div>
    </div>
  );
};

export default RankingList;
