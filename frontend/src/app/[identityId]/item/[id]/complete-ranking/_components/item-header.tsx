import { getItem } from '@/lib/server-api/item-endpoint/item-endpoint';

interface ItemHeaderProps {
  itemId: string;
  identityId: string;
}

const ItemHeader = async ({ itemId, identityId }: ItemHeaderProps) => {
  const itemData = await getItem(identityId, Number(itemId));

  return (
    <div className="mt-4 p-2">
      <div className="border border-white p-2">
        <h1 className="text-center text-medium-18 text-white">{itemData.data?.item.name || 'アイテム名'}</h1>
      </div>
    </div>
  );
};

export default ItemHeader;
