import clsx from 'clsx';
import Link from 'next/link';
import Avatar from '@/components/atoms/avatar';
import { RightArrowIcon } from '@/components/atoms/right-arrow-icon';
import ShopPublicImage from '@/components/ShopImage';
import type { GetGachaCompleteBadgeRankingResponse } from '@/lib/server-api/shop-api.schemas';
import { formatRankingDate } from '@/utils/time';

type StandardRankingListItemProps = {
  entry: GetGachaCompleteBadgeRankingResponse;
  rank: number;
  itemId: string;
  identityId: string;
  isHighlighted?: boolean;
};

export const StandardRankingListItem = ({ entry, rank, isHighlighted = false }: StandardRankingListItemProps) => {
  const { userAccountIdentity, userName, userIcon, getBadgeAt } = entry;

  return (
    <div
      className={clsx('mb-3 h-18 rounded-xl', {
        'bg-ranking-highlight-bg': isHighlighted,
      })}
    >
      <div className="flex h-full items-center gap-2 p-3">
        <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
          <span
            className={clsx('text-medium-16', {
              'text-black': isHighlighted,
              'text-white': !isHighlighted,
            })}
          >
            {rank}
          </span>
        </div>

        <div className="flex-shrink-0">
          <Avatar src={userIcon} alt={userName} size="medium" borderColor="white" />
        </div>

        <div className="min-w-0 flex-1">
          <div
            className={clsx('mb-1 truncate text-medium-14', {
              'text-black': isHighlighted,
              'text-white': !isHighlighted,
            })}
          >
            {userName}
          </div>
          <div
            className={clsx('text-regular-12 opacity-80', {
              'text-black': isHighlighted,
              'text-white': !isHighlighted,
            })}
          >
            {formatRankingDate(getBadgeAt)}
          </div>
        </div>

        <div className="relative flex-shrink-0">
          <Link
            href={userAccountIdentity ? `${process.env.NEXT_PUBLIC_FANME_LINK_URL}/@${userAccountIdentity}` : '#'}
            className={clsx('flex h-7 w-12 items-center justify-center gap-1 rounded-2xl border', {
              'border-black': isHighlighted,
              'border-white': !isHighlighted,
              'pointer-events-none cursor-not-allowed opacity-50': !userAccountIdentity,
            })}
          >
            <ShopPublicImage
              src="/images/gacha/icons/user.svg"
              alt="User"
              width={14}
              height={14}
              className={clsx({
                'invert filter': isHighlighted,
              })}
            />
            <RightArrowIcon color={isHighlighted ? 'black' : 'white'} width={14} height={14} />
          </Link>
          {isHighlighted && (
            <span className="absolute -bottom-5 left-1/2 -translate-x-1/2 whitespace-nowrap text-regular-9 text-black">
              あなたの順位
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
