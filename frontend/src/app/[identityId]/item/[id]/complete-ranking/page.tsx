import ShopPublicImage from '@/components/ShopImage';
import ItemHeader from './_components/item-header';
import RankingList from './_components/ranking-list';
import { getUserIdentityId } from '@/utils/base';

type Props = {
  params: {
    id: string;
    identityId: string;
  };
};

const CompleteRankingPage = ({ params }: Props) => {
  const itemId = params.id;
  const identityId = getUserIdentityId(params.identityId);

  return (
    <div className="flex min-h-screen flex-col bg-ranking-pattern bg-repeat">
      <div className="relative mt-4 flex h-18 items-center justify-center">
        <ShopPublicImage
          src="/images/gacha/Complete_Ranking_Header.svg"
          alt="Complete Ranking"
          width={270}
          height={72}
        />
      </div>

      <ItemHeader itemId={itemId} identityId={identityId} />

      <div className="flex-1 overflow-hidden">
        <RankingList itemId={itemId} identityId={identityId} />
      </div>
    </div>
  );
};

export default CompleteRankingPage;
