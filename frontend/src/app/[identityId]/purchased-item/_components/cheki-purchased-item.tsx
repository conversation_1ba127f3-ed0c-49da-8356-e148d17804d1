import React from 'react';
import Accordion from '@/components/atoms/accordion';
import CreatorInfo from '@/components/containers/creator-info';
import { PurchasedItemDetail, ShopForGetShop } from '@/lib/fanme-api/fanme-api.schemas';
import BenefitsSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/benefit-section';
import ItemDetailSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/item-detail-section';
import MainItemSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-section';
import NotesSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/notes-section';
import PaymentReceipt from '@/app/[identityId]/purchased-item/[purchasedItemId]/payment-receipt';
import PurchaseOptionSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/purchase-option-section';
import PurchaseStatusSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/purchase-status-section';
import { ChekiItemDetail } from '@/types/shopItem';

type DigitalBundlePurchasedItemProps = {
  shop: ShopForGetShop;
  item: ChekiItemDetail['item'];
  purchasedItemDetail: PurchasedItemDetail;
};
const DigitalBundlePurchasedItem = ({ shop, item, purchasedItemDetail }: DigitalBundlePurchasedItemProps) => {
  if (!purchasedItemDetail || !purchasedItemDetail.itemId) throw new Error('purchasedItem not found');
  if (!item || !item.id) throw new Error('item not found');
  if (!shop) throw new Error('shop not found');

  return (
    <div className="flex flex-col items-center justify-center gap-5 bg-gray-100 pb-10">
      <div className="flex w-full flex-col gap-3 bg-white px-4 py-3">
        <CreatorInfo
          creatorName={shop.creatorName}
          identityId={shop.creatorAccountIdentity}
          creatorIcon={shop.creatorIconUri}
        />
        {!item.isPurchased && <PurchaseStatusSection purchasedItemDetail={purchasedItemDetail} />}
        <MainItemSection thumbnail={item.thumbnail} title={item.title} />
      </div>
      <div className="flex w-full flex-col gap-3">
        <ItemDetailSection name={item.title} description={item.description} />
        {item.isPurchased && item.benefits && item.benefits.length > 0 && (
          <BenefitsSection itemId={item.id?.toString()} benefits={item.benefits} />
        )}
      </div>
      <div className="flex w-full flex-col gap-3">
        {purchasedItemDetail.purchaserComment && <PurchaseOptionSection purchasedItemDetail={purchasedItemDetail} />}
        <div className="w-full px-4">
          <Accordion icon={'/images/icons/CreditScore.svg'} title={'決済情報'} type="white" showDivider={false}>
            <PaymentReceipt purchasedItemDetail={purchasedItemDetail} />
          </Accordion>
        </div>
        <div className="w-full px-4">
          <Accordion icon={'/images/icons/ErrorOutline.svg'} title={'商品について'} type="white" showDivider={false}>
            <NotesSection isDigital={false} />
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default DigitalBundlePurchasedItem;
