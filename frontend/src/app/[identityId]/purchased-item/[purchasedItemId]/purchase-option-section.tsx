import React from 'react';
import { PurchasedItemDetail } from '@/lib/server-api/shop-api.schemas';

interface PurchaseOptionSectionProps {
  purchasedItemDetail: PurchasedItemDetail;
}

const PurchaseOptionSection = ({ purchasedItemDetail }: PurchaseOptionSectionProps) => {
  return (
    <section className="flex w-full flex-col bg-gray-100 px-4">
      <div className="flex w-full flex-col gap-2 rounded-lg bg-white p-4">
        <div className="whitespace-pre-wrap break-words text-bold-14">備考</div>
        <div className="whitespace-pre-wrap break-words text-regular-14">{purchasedItemDetail.purchaserComment}</div>
      </div>
    </section>
  );
};

export default PurchaseOptionSection;
