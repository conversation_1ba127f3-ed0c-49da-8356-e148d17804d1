'use client';

import React from 'react';
import clsx from 'clsx';
import CvsPaymentUrl from '@/components/containers/CvsPaymentUrl';
import ShopPublicImage from '@/components/ShopImage';
import { PurchasedItemDetail } from '@/lib/fanme-api/fanme-api.schemas';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { formatDate } from '@/utils/time';

interface PurchaseStatusSectionProps {
  purchasedItemDetail: PurchasedItemDetail;
}

const PurchaseRow = ({
  title,
  value = '',
  isLink = false,
}: {
  title: string;
  value?: string | null | undefined;
  isLink?: boolean;
}) => {
  return (
    <div className="flex min-h-8 w-full border-b border-b-white">
      <div className="flex w-20 items-center justify-center bg-gray-700 text-regular-10 text-white">{title}</div>
      <div
        className={clsx(
          isLink && 'text-orange-300',
          'flex flex-1 items-center justify-start bg-gray-100 pl-2 pr-3 text-regular-12',
        )}
      >
        {isLink && value ? (
          <a href={value} target="_blank" rel="noopener noreferrer" className="text-orange-300">
            {value}
          </a>
        ) : (
          <>{value}</>
        )}
      </div>
    </div>
  );
};

const PurchaseStatusSection = ({ purchasedItemDetail }: PurchaseStatusSectionProps) => {
  const popupHeader = (
    <div className="flex flex-col items-center gap-2">
      <ShopPublicImage src={'/images/icons/HelpOutline.svg'} width={26} height={26} alt="help" />
      <p className="text-bold-16">決済待ちとは</p>
    </div>
  );
  const popupContent = (
    <div className="px-6 text-center text-regular-14">
      コンビニ支払いまたはPayPayをご選択の場合、決済期限までにお支払いが確認できないと、自動的にご注文はキャンセルとなります。お支払い方法をご確認のうえ、期日までにお手続きをお済ませください。
    </div>
  );
  const popupFooter = <></>;
  const { openInstructionModal } = useInstructionModalStore();

  const openHelpModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  if (purchasedItemDetail?.checkout?.status === 'PAYSUCCESS') return <></>;

  return (
    <div className="mb-10 flex w-full flex-col">
      <div className="relative flex w-full flex-col items-center justify-center rounded-lg border border-gray-200 bg-white px-4 pb-5 pt-3.5">
        <div className="mb-1.5 text-regular-10">購入ステータス</div>
        <div className="mb-3 text-bold-16">決済待ち</div>
        <div className="absolute right-1.5 top-1.5" onClick={openHelpModal}>
          <ShopPublicImage src={'/images/icons/HelpOutline.svg'} alt={''} width={24} height={24} />
        </div>
        <div className="w-full overflow-hidden rounded-md">
          <PurchaseRow title={'コンビニ'} value={purchasedItemDetail?.checkout?.convenience} />
          <PurchaseRow title={'支払い期限'} value={formatDate(purchasedItemDetail?.checkout?.paymentTerm)} />
          <PurchaseRow title={'受付番号'} value={purchasedItemDetail?.checkout?.receiptNo} />
          <PurchaseRow title={'確認番号'} value={purchasedItemDetail?.checkout?.confNo} />
          <PurchaseRow title={'振込表URL'} value={purchasedItemDetail?.checkout?.receiptUrl} isLink={true} />
        </div>
        <div className="mt-5 flex flex-col gap-3">
          <CvsPaymentUrl convenience={purchasedItemDetail.checkout?.convenience} />
          <div className="text-regular-12">注文番号 {purchasedItemDetail?.order.orderNumber}</div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseStatusSection;
