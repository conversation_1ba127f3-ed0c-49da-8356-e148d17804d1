'use client';

import React from 'react';
import ReadMoreButton from '@/components/containers/ReadMoreButton';

interface ItemDetailSectionProps {
  name: string;
  description?: string;
}

const ItemDetailSection = ({ name, description }: ItemDetailSectionProps) => {
  return (
    <section className="flex w-full flex-col bg-gray-100 px-4">
      <div className="flex w-full flex-col gap-2 rounded-lg bg-white p-4">
        <div className="whitespace-pre-wrap break-words text-bold-16">{name}</div>
        {!!description && <ReadMoreButton description={description?.trim()} textSize={14} maxLine={3} />}
      </div>
    </section>
  );
};

export default ItemDetailSection;
