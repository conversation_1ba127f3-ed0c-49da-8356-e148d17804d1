import React from 'react';
import { notFound } from 'next/navigation';
import { getPurchasedItem } from '@/lib/server-api/purchased-item-endpoint/purchased-item-endpoint';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import ChekiPurchasedItem from '@/app/[identityId]/purchased-item/_components/cheki-purchased-item';
import DigitalBundlePurchasedItem from '@/app/[identityId]/purchased-item/_components/digital-bundle-purchased-item';
import GachaPurchasedItem from '@/app/[identityId]/purchased-item/_components/gacha-purchased-item';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { GachaItem } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';
import { ChekiItemDetail, ShopItemDetail } from '@/types/shopItem';

type ItemViewerProps = {
  identityId: string;
  purchasedItemId: string;
};

const ItemPurchasedDetail = async ({ params }: { params: ItemViewerProps }) => {
  const purchasedItemId = params.purchasedItemId;
  const identityId = getUserIdentityId(params.identityId);

  if (isNaN(Number(purchasedItemId))) {
    return notFound();
  }

  try {
    const purchasedItemResponse = await getPurchasedItem(parseInt(params.purchasedItemId));
    const purchasedItemDetail = purchasedItemResponse.data?.purchasedItem;
    if (!purchasedItemDetail || !purchasedItemDetail.itemId) throw new Error('purchasedItem not found');

    const itemResponse = await getItem(purchasedItemDetail.itemId.toString(), identityId, true);
    const itemData = await itemResponse.json();
    const item: ShopItemDetail['item'] | GachaItem['item'] | ChekiItemDetail['item'] = itemData.data?.item;
    if (!item || !item.id) throw new Error('item not found');

    const shopResponse = await getShop(identityId);
    const shop = shopResponse.data?.shop;
    if (!shop) throw new Error('shop not found');

    return (
      <>
        {item.itemType === ITEM_TYPE.DIGITAL_BUNDLE.value && (
          <DigitalBundlePurchasedItem
            shop={shop}
            item={item as ShopItemDetail['item']}
            purchasedItemDetail={purchasedItemDetail}
          />
        )}
        {item.itemType === ITEM_TYPE.DIGITAL_GACHA.value && (
          <GachaPurchasedItem shop={shop} item={item as GachaItem['item']} purchasedItemDetail={purchasedItemDetail} />
        )}
        {item.itemType === ITEM_TYPE.CHEKI.value && (
          <ChekiPurchasedItem
            shop={shop}
            item={item as ChekiItemDetail['item']}
            purchasedItemDetail={purchasedItemDetail}
          />
        )}
      </>
    );
  } catch (e) {
    console.error(JSON.stringify({ e }));
    return notFound();
  }
};

export default ItemPurchasedDetail;
