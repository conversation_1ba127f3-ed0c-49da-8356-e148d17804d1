'use client';

import { useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-hot-toast';
import clsx from 'clsx';
import Lightbox, { ImageSource, ZoomRef } from 'yet-another-react-lightbox';
import Zoom from 'yet-another-react-lightbox/plugins/zoom';
import Button from '@/components/atoms/button';
import CustomToast from '@/components/atoms/toast/custom-toast';
import ShopPublicImage from '@/components/ShopImage';
import { useViewerStore } from '@/store/useViewerStore';
import 'yet-another-react-lightbox/styles.css';
import LightboxControlIcon from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/full-screen-swiper-modal/lightbox-control-icon';
import LightboxFractionControls from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/full-screen-swiper-modal/lightbox-fraction-controls';
import { useIsLandscape } from '@/hooks/useIsLandscape';
import { useIsPCBrowser } from '@/hooks/useIsPCBrowser';
import { fileService } from '@/services/file';
import { SingleItem } from '@/types/shopItem';

type FullScreenSwiperModalProps = {
  index: number;
  purchasedFiles: SingleItem[];
  setZoomIndex: React.Dispatch<React.SetStateAction<number>>;
  itemId: number;
};

const FullScreenSwiperModal = ({ index, setZoomIndex, purchasedFiles, itemId }: FullScreenSwiperModalProps) => {
  const isLandscape = useIsLandscape();
  const isPCBrowser = useIsPCBrowser();
  const { bookViewer, setBookViewer } = useViewerStore();
  const isBookView = bookViewer || isLandscape;
  const zoomRef = useRef<ZoomRef>(null);
  const [currentIndex, setCurrentIndex] = useState(index >= 0 ? index : 0);
  const prevIndexRef = useRef(index);
  const prevCurrentIndexRef = useRef(currentIndex);
  const isInitializedRef = useRef(false);

  useEffect(() => {
    // indexプロパティが変更されたら常にcurrentIndexを更新
    if (index >= 0) {
      setCurrentIndex(index);
      prevIndexRef.current = index;
    }
  }, [index]);

  useEffect(() => {
    prevCurrentIndexRef.current = currentIndex;
  }, [currentIndex]);

  useEffect(() => {
    // 初期化完了をマークする（Zoomプラグインの初期化を待つ）
    const timer = setTimeout(() => {
      isInitializedRef.current = true;
    }, 200);
    return () => clearTimeout(timer);
  }, []);

  const [showControls, setShowControls] = useState(true);
  const [showIcon, setShowIcon] = useState(true);
  const [width, setWidth] = useState<number>(window.innerWidth);
  const [height, setHeight] = useState<number>(window.innerHeight);

  const handleClose = () => {
    setZoomIndex(-1);
    setBookViewer(false);
  };

  const toggleControls = () => {
    setShowControls((prev) => !prev);
  };

  const imageOnlyFiles = useMemo(() => {
    return purchasedFiles.filter((item) => item.type === 'image');
  }, [purchasedFiles]);

  const [evenSlides, oddSlides, slides] = useMemo(() => {
    const even: { src: string; alt?: string }[] = [];
    const odd: { src: string; alt?: string }[] = [];
    const all: { src: string; alt?: string }[] = [];

    imageOnlyFiles.forEach((item, index) => {
      const slide = { src: item.src, alt: item.title };
      if (index % 2 === 0) {
        even.push(slide);
      } else {
        odd.push(slide);
      }
      all.push(slide);
    });

    return [even, odd, all];
  }, [imageOnlyFiles]);

  const totalSlides = slides.length;

  const onSingleDownloadFile = async () => {
    const file = imageOnlyFiles[currentIndex];
    if (!file) return;
    try {
      const downloadUrls = await fileService.getDownloadUrl({
        itemId: itemId,
        fileData: [file]
          .filter((file) => !!file.type && file.isPurchased)
          .map((item) => ({ item, name: fileService.getValidFileNameForDownload(item.title, item.type!) })),
      });
      downloadUrls.forEach((downloadItem, index) => {
        setTimeout(() => {
          fileService.downloadByLink(downloadItem.url);
        }, index * 200);
      });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      toast.custom((t) => CustomToast(t, 'error', 'ダウンロードに失敗しました'), {
        id: 'download-failed',
      });
    }
  };

  useEffect(() => {
    setWidth(window.innerWidth);
    setHeight(window.innerHeight);

    setShowIcon(true);
    const timer = setTimeout(() => setShowIcon(false), 1000);
    return () => clearTimeout(timer);
  }, [isLandscape]);

  return (
    <div className={`fixed top-0 z-50 h-screen bg-gray-600 ${isBookView ? 'max-w-full' : 'max-w-120'}`}>
      <div className="absolute inset-0 m-auto">
        <Lightbox
          open={index !== -1}
          close={handleClose}
          slides={
            isBookView
              ? evenSlides.map((slide, index) => ({
                  ...slide,
                  src: slide.src,
                  alt: 'landscape',
                  srcSet: [
                    {
                      src: slide.src,
                      width: width,
                      height: height,
                    },
                    oddSlides[index] && {
                      src: oddSlides[index].src,
                      width: width,
                      height: height,
                    },
                  ].filter(Boolean) as ImageSource[],
                }))
              : slides.map((slide) => ({
                  ...slide,
                  src: slide.src,
                  alt: 'portrait',
                  srcSet: [
                    {
                      src: slide.src,
                      width: width,
                      height: height,
                    },
                  ],
                }))
          }
          plugins={[Zoom]}
          zoom={{ ref: zoomRef, maxZoomPixelRatio: 5, zoomInMultiplier: 1.5 }}
          index={isBookView ? Math.floor(currentIndex / 2) : currentIndex}
          carousel={{ padding: 0, finite: false }}
          animation={{ swipe: 500 }}
          className={clsx(
            'viewer-images-modal',
            isBookView && 'landscape',
            showControls ? 'controls-visible' : 'controls-hidden',
          )}
          on={{
            view: ({ index: newIndex }) => {
              const calculatedIndex = isBookView ? 2 * newIndex : newIndex;

              // 初期化中のon.view呼び出しを全て無視
              if (!isInitializedRef.current) {
                return;
              }

              if (calculatedIndex !== prevCurrentIndexRef.current) {
                setCurrentIndex(calculatedIndex);
              }
            },
            click: toggleControls,
          }}
          render={{
            ...(isBookView
              ? {
                  slide: () => {
                    const left = evenSlides[Math.floor(currentIndex / 2)];
                    const right = oddSlides[Math.floor(currentIndex / 2)];
                    return (
                      <div className="relative size-full" onClick={toggleControls}>
                        <div className="absolute left-0 top-0 h-full w-[calc(50%)]">
                          <img src={left?.src} alt="left" className="size-full object-contain object-right" />
                        </div>

                        <div className="absolute right-0 top-0 h-full w-[calc(50%)]">
                          {right?.src && (
                            <img src={right?.src} alt="right" className="size-full object-contain object-left" />
                          )}
                        </div>
                        {showIcon && !isPCBrowser && (
                          <ShopPublicImage
                            src="/images/icons/DualSwiper.svg"
                            width={97}
                            height={96}
                            alt="dual swiper"
                            className={`absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transition-opacity duration-1000 ${
                              showIcon ? 'opacity-100' : 'opacity-0'
                            }`}
                          />
                        )}
                      </div>
                    );
                  },
                }
              : {}),
            controls: () => (
              <>
                <LightboxFractionControls
                  isPageSpread={bookViewer && isPCBrowser}
                  showControls={showControls}
                  currentIndex={currentIndex}
                  totalSlides={totalSlides}
                />
                {!isBookView && (
                  <Button
                    buttonSize="free"
                    buttonType="light"
                    className={clsx(
                      'fixed bottom-5 left-1/2 -translate-x-1/2',
                      'transition-opacity duration-300',
                      showControls ? 'opacity-100' : 'opacity-0',
                    )}
                    buttonClassNames="text-bold-12 w-30 h-8 gap-1"
                    onClick={onSingleDownloadFile}
                  >
                    <ShopPublicImage src="/images/icons/Download.svg" alt="download" width={16} height={16} />
                    ダウンロード
                  </Button>
                )}
              </>
            ),
            iconZoomIn: () => null,
            iconZoomOut: () => null,
            iconPrev: () => (
              <LightboxControlIcon
                type={isPCBrowser && isBookView ? 'prev_pc' : 'prev'}
                iconWidth={16}
                iconHeight={16}
              />
            ),
            iconNext: () => (
              <LightboxControlIcon
                type={isPCBrowser && isBookView ? 'next_pc' : 'next'}
                iconWidth={16}
                iconHeight={16}
              />
            ),
            iconClose: () => (
              <LightboxControlIcon
                type={isPCBrowser && isBookView ? 'close_pc' : 'close'}
                iconWidth={20}
                iconHeight={20}
              />
            ),
          }}
        />
      </div>
    </div>
  );
};

export default FullScreenSwiperModal;
