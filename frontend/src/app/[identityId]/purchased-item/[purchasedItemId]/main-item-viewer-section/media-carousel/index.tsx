import React, { useCallback, useMemo, useState } from 'react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/virtual';
import clsx from 'clsx';
import { Pagination, Virtual } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Swiper as SwiperType } from 'swiper/types';
import { PlayerType, usePlayerStore } from '@/store/usePlayer';
import MediaActions from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/media-carousel/media-actions';
import MediaPreview from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/media-carousel/media-preview';
import { SingleItem } from '@/types/shopItem';

type MediaCarouselProps = {
  files: SingleItem[];
  setZoomIndex: React.Dispatch<React.SetStateAction<number>>;
  itemId: number;
};

const INITIAL_SLIDES_COUNT = 5;
const ADDITIONAL_SLIDES_COUNT = 3;
const THRESHOLD_FROM_END = 2;

const MediaCarousel = ({ files, setZoomIndex, itemId }: MediaCarouselProps) => {
  const { setPlayerProps, onPlayerOpen } = usePlayerStore();

  // 仮想化のためのステート管理
  const [virtualSlides, setVirtualSlides] = useState<SingleItem[]>(files.slice(0, INITIAL_SLIDES_COUNT));

  const openItemViewer = useCallback(
    (file: SingleItem) => {
      if (file.type === 'image') {
        // virtualSlides配列内でのインデックスを取得
        const virtualIndex = virtualSlides.findIndex((slide) => slide.id === file.id);
        setZoomIndex(virtualIndex);
        document.body.style.overflow = 'hidden';
        return;
      }
      onPlayerOpen();
      setPlayerProps({
        src: file.src,
        thumbnail: file.thumbnail!,
        type: file.type as PlayerType,
        file: file,
        itemId: itemId,
      });
    },
    [virtualSlides, setZoomIndex, onPlayerOpen, setPlayerProps, itemId],
  );

  const onSlideChange = useCallback(
    (swiper: SwiperType) => {
      // 仮想スライドの動的ロード
      const currentIndex = swiper.activeIndex;
      const remainingItems = files.length - virtualSlides.length;

      if (remainingItems > 0 && currentIndex >= virtualSlides.length - THRESHOLD_FROM_END) {
        const nextBatch = files.slice(
          virtualSlides.length,
          Math.min(virtualSlides.length + ADDITIONAL_SLIDES_COUNT, files.length),
        );

        setVirtualSlides((prev) => [...prev, ...nextBatch]);
      }
    },
    [files, virtualSlides.length],
  );

  // メモ化されたスライドレンダリング
  const slideContent = useMemo(
    () =>
      virtualSlides.map((file) => (
        <SwiperSlide key={file.id} className={clsx(1 < virtualSlides.length && 'pb-2')}>
          <div className="relative" onClick={() => openItemViewer(file)}>
            <MediaPreview file={file} />
            <MediaActions file={file} />
          </div>
          <div className="p-2 text-center text-regular-14">{file.title}</div>
        </SwiperSlide>
      )),
    [virtualSlides, openItemViewer],
  );

  return (
    <div className="relative w-full [&_.swiper-pagination-bullet-active]:bg-gray-800 [&_.swiper-pagination-bullet]:bg-gray-400">
      <Swiper
        modules={[Pagination, Virtual]}
        pagination={{
          clickable: true,
          dynamicBullets: true,
          dynamicMainBullets: 3,
        }}
        virtual
        className="w-full"
        onSlideChange={onSlideChange}
      >
        {slideContent}
      </Swiper>
    </div>
  );
};

export default React.memo(MediaCarousel);
