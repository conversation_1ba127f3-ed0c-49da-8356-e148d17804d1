import React from 'react';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';
import { useViewerStore } from '@/store/useViewerStore';
import { useIsPCBrowser } from '@/hooks/useIsPCBrowser';
import { SingleItem } from '@/types/shopItem';

type MediaActionsProps = {
  file: SingleItem;
};

const MediaActions = ({ file }: MediaActionsProps) => {
  const isPCBrowser = useIsPCBrowser();
  const { setBookViewer } = useViewerStore();

  if (file.type !== 'image') {
    return (
      <div className="absolute inset-0 grid place-content-center">
        <Button buttonShape="circle" buttonSize="md" buttonType="light">
          <div className="triangle-right ml-1" />
        </Button>
      </div>
    );
  }
  return (
    <>
      {isPCBrowser && (
        <div
          className="absolute bottom-4 right-18 flex size-12 cursor-pointer items-center justify-center rounded-full bg-white"
          onClick={() => setBookViewer(true)}
        >
          <ShopPublicImage src={'/images/icons/Book.svg'} width={27} height={27} alt="book" />
        </div>
      )}
      <div
        className="absolute bottom-4 right-4 flex size-12 cursor-pointer items-center justify-center rounded-full bg-white"
        onClick={() => setBookViewer(false)}
      >
        <ShopPublicImage src={'/images/icons/ZoomIn.svg'} width={27} height={27} alt="zoom" />
      </div>
    </>
  );
};

export default MediaActions;
