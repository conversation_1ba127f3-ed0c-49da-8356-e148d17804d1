import clsx from 'clsx';

type LightboxFractionControlsProps = {
  showControls: boolean;
  isPageSpread: boolean;
  currentIndex: number;
  totalSlides: number;
};

const LightboxFractionControls = ({
  showControls,
  isPageSpread,
  currentIndex,
  totalSlides,
}: LightboxFractionControlsProps) => {
  const slidesIndex =
    isPageSpread && totalSlides > 1 && totalSlides % 2 === 1 && currentIndex < totalSlides - 1
      ? `${currentIndex + 1}-${currentIndex + 2}`
      : `${currentIndex + 1}`;
  return (
    <div
      className={clsx(
        'absolute left-3 top-4',
        'transition-opacity duration-300',
        showControls ? 'opacity-100' : 'opacity-0',
      )}
    >
      <div className="text-white">
        <span className="text-bold-17">{`${slidesIndex}`}</span>
        <span className="text-bold-15">{` / ${totalSlides}`}</span>
      </div>
    </div>
  );
};

export default LightboxFractionControls;
