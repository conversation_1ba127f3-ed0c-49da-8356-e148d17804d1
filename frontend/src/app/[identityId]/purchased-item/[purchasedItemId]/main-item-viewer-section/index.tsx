'use client';

import { useEffect, useMemo, useState } from 'react';
import clsx from 'clsx';
import Link from 'next/link';
import FullScreenModal from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/FullScreenSwiperModal';
import MediaCarousel from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-viewer-section/media-carousel';
import { SingleItem } from '@/types/shopItem';

type MainItemViewerSectionProps = {
  itemFiles: SingleItem[];
  itemId: number;
};

const MainItemViewerSection = ({ itemFiles, itemId }: MainItemViewerSectionProps) => {
  // Using useState and useEffect to handle hydration mismatch
  // Issue: isIOS() returns different values on server and client
  // - Server: Always returns false (no window object)
  // - Client: Returns true/false based on user agent
  // Solution: Initially hide buttons (false) and show them after client-side check

  const [zoomIndex, setZoomIndex] = useState<number>(-1);

  useEffect(() => {
    document.body.style.overflow = zoomIndex === -1 ? 'auto' : 'hidden';
  }, [zoomIndex]);

  const purchasedFiles = useMemo(() => itemFiles.filter((file) => file.isPurchased), [itemFiles]);

  return (
    <div
      className={clsx(
        'flex w-full flex-col items-center justify-center rounded-b-2xl',
        1 < purchasedFiles.length && 'gap-5',
      )}
    >
      <MediaCarousel files={purchasedFiles} setZoomIndex={setZoomIndex} itemId={itemId} />
      <Link
        href="https://media.fanme.link/1187/"
        target="_blank"
        className="mb-2 text-regular-12 text-blue-150 underline"
      >
        購入したコンテンツの楽しみ方
      </Link>
      {zoomIndex !== -1 && (
        <FullScreenModal
          purchasedFiles={purchasedFiles}
          index={zoomIndex}
          setZoomIndex={setZoomIndex}
          itemId={itemId}
        />
      )}
    </div>
  );
};

export default MainItemViewerSection;
