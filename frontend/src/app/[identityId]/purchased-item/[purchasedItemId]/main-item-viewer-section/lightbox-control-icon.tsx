import ShopPublicImage from '@/components/ShopImage';

const iconMap = {
  zoomIn: {
    src: '/images/icons/ScalingPlus.svg',
    alt: 'zoom-in',
    className: 'zoom-btn',
    width: '24',
    height: '24',
  },
  zoomOut: {
    src: '/images/icons/ScalingMinus.svg',
    alt: 'zoom-out',
    className: 'zoom-btn',
    width: '24',
    height: '24',
  },
  prev: {
    src: '/images/icons/Arrow_Back_White.svg',
    alt: 'prev',
    className: 'prev-btn',
    width: '24',
    height: '24',
  },
  next: {
    src: '/images/icons/Arrow_Back_White.svg',
    alt: 'next',
    className: 'next-btn rotate-180',
    width: '24',
    height: '24',
  },
  close: {
    src: '/images/icons/Close.svg',
    alt: 'close',
    className: 'close-btn',
    width: '24',
    height: '24',
  },
  prev_pc: {
    src: '/images/icons/Arrow_Back_White_PC.svg',
    alt: 'pre_vpc-btn',
    className: 'prev-btn pc',
    width: '25',
    height: '25',
  },
  next_pc: {
    src: '/images/icons/Arrow_Back_White_PC.svg',
    alt: 'next_pc-btn',
    className: 'next-btn pc rotate-180',
    width: '25',
    height: '25',
  },
  close_pc: {
    src: '/images/icons/Close.svg',
    alt: 'close_pc',
    className: 'close-btn',
    width: '30',
    height: '30',
  },
} as const;

type IconKey = keyof typeof iconMap;

type LightboxControlIconProps = {
  type: IconKey;
  iconWidth?: number;
  iconHeight?: number;
};

const LightboxControlIcon = ({ type, iconWidth, iconHeight }: LightboxControlIconProps) => {
  const { src, alt, className, width, height } = iconMap[type];
  return (
    <ShopPublicImage
      src={src}
      alt={alt}
      width={iconWidth ?? width}
      height={iconHeight ?? height}
      className={className}
    />
  );
};

export default LightboxControlIcon;
