'use client';

import React, { ReactNode } from 'react';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Button from '@/components/atoms/button';
import FlatItem from '@/components/containers/FlatItem';
import FixedBar from '@/components/layouts/FixedBar';
import ShopPublicImage from '@/components/ShopImage';
import { useShopItemsStore } from '@/store/useItems';
import { useSorting } from '@/store/useSorting';
import { shopItemsRequests } from '@/services/shopItemsRequests';
import { FileQuantity } from '@/types/cart';
import { ShopItemType } from '@/types/shopItem';

interface SortableItemProps {
  id: number;
  children: ReactNode;
}

const SortableItem = ({ id, children }: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </div>
  );
};

interface EditItemsSectionProps {
  identityId: string;
}

const EditItemsSection = ({ identityId }: EditItemsSectionProps) => {
  const { itemList, setItemList } = useShopItemsStore();
  const [activeItems, setActiveItems] = React.useState<ShopItemType[]>(itemList || []);

  const { sorting, setSorting } = useSorting();

  const now = new Date().getTime();

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      setActiveItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const handleSortStart = () => {
    setSorting(true);
  };
  const handleSortEnd = async () => {
    const tempActiveItems = [...activeItems];
    const newActiveItems = tempActiveItems.map((item, index) => ({ id: item.id, sortOrder: index }));
    await shopItemsRequests.sort({
      items: newActiveItems,
    });
    const newItems = await shopItemsRequests.getItems(identityId);
    setSorting(false);
    setItemList(newItems.data.items);
  };

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 200, // 0.2秒間触った後にドラッグを開始
      tolerance: 5, // 5px以内の移動は無視
    },
  });
  const sensors = useSensors(useSensor(MouseSensor), touchSensor);

  return (
    <>
      <div className="flex justify-end px-2 pb-1 pt-4">
        {!sorting && (
          <Button buttonType="light-shadow" buttonSize="md" onClick={handleSortStart}>
            <ShopPublicImage src="/images/icons/Sort.svg" alt="sort" width={12} height={12} className="mr-1" />
            並び替え
          </Button>
        )}
      </div>

      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext items={activeItems.map((item) => item.id)} disabled={!sorting}>
          <div className="my-2 flex flex-col items-center justify-center gap-8 px-3">
            <div className={'flex w-full flex-col gap-4'}>
              {activeItems.map((item) => {
                const isEnded =
                  (item.stockCount !== undefined && item.stockCount <= 0) ||
                  (item.forSale?.endAt ? new Date(item.forSale?.endAt).getTime() <= now : false);

                return (
                  <SortableItem key={item.id} id={item.id}>
                    <div className={`${sorting ? 'animate-shake_weekly' : ''} w-full bg-white p-2`}>
                      <FlatItem
                        identityId={identityId}
                        id={item.id}
                        itemType={item.itemType}
                        imageUrl={item.thumbnail}
                        title={item.title}
                        available={item.available}
                        isLabeled={true}
                        isStarted={item.forSale?.startAt ? now < new Date(item.forSale?.startAt).getTime() : false}
                        isEnded={isEnded}
                        isSet={true}
                        price={item.price}
                        currentPrice={item.currentPrice}
                        discountRate={item.onSale?.discountRate}
                        showRemoveButton={false}
                        thumbnailRatio={1}
                        isEdit={sorting ? !sorting : true}
                        fileQuantities={item.fileQuantities as unknown as FileQuantity[]}
                      />
                    </div>
                  </SortableItem>
                );
              })}
            </div>
          </div>
        </SortableContext>
      </DndContext>
      {sorting && (
        <FixedBar isFixed={true}>
          <Button buttonType="primary" onClick={handleSortEnd}>
            完了
          </Button>
        </FixedBar>
      )}
    </>
  );
};

export default EditItemsSection;
