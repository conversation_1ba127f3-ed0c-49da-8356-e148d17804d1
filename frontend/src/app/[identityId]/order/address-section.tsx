'use client';

import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';
import { useGetFanmeCustomer } from '@/lib/fanme-api/fanme/fanme';

const AddressSection = () => {
  const router = useRouter();
  const { data } = useGetFanmeCustomer();
  const fanmeCustomer = data?.data?.fanmeCustomer;

  return (
    <div className="flex w-full">
      {fanmeCustomer ? (
        <div className="flex w-full items-center justify-between text-medium-14 text-gray-700">
          <div className="flex flex-col">
            <div>
              {fanmeCustomer.postalCode} {fanmeCustomer.prefecture} {fanmeCustomer.city}
            </div>
            <div>
              {fanmeCustomer.street} {fanmeCustomer.building}
            </div>
          </div>
          <Button
            onClick={() => router.push(`/address/edit`)}
            buttonType="light-small"
            buttonShape="circle"
            buttonSize="xxs"
          >
            <ShopPublicImage
              src="/images/icons/Arrow_Back.svg"
              width={12}
              height={12}
              alt="help"
              className="-rotate-180"
            />
          </Button>
        </div>
      ) : (
        <div className="flex w-full items-center justify-center">
          <Button buttonType="dark" onClick={() => router.push(`/address/edit`)} className="!h-auto">
            配送先の登録
          </Button>
        </div>
      )}
    </div>
  );
};

export default AddressSection;
