import React from 'react';
import Link from 'next/link';

const NotesSection = () => {
  const fanmeDomain = process.env.NEXT_PUBLIC_FANME_LINK_URL || 'http://host.docker.internal:11000';
  return (
    <div className="flex w-full items-center justify-between">
      <div className="w-1/4 text-regular-11">注意事項</div>
      <div className="w-3/4 grow text-regular-11">
        ご購入をもって、
        <Link href={`${fanmeDomain}/terms`} className="text-blue-500 underline" target="_blank">
          利用規約
        </Link>
        に同意したものとさせていただきます。ご購入後のキャンセルはできません。画像・テキスト・音声・動画などのデータは著作権法により保護されています。データの複製や、転載、それに準ずる行為は禁止されており、法律により罰せられる場合がございます。
      </div>
    </div>
  );
};

export default NotesSection;
