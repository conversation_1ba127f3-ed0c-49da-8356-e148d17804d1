'use client';

import { useForm } from 'react-hook-form';
import NumberInput from '@/components/atoms/inputs/number-input';
import ShopPublicImage from '@/components/ShopImage';
import InputTipButton from '../input-tip-button';
import { useOrderFormStore } from '@/store/useOrderFormStore';
interface TipRecommendationSectionProps {
  tipLimitAmount: number;
}

const TipRecommendationSection = ({ tipLimitAmount = 0 }: TipRecommendationSectionProps) => {
  const tipAmounts = [500, 1000, 10000, 50000];
  const { tip, setTip } = useOrderFormStore();
  const {
    control,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      tip: tip || 0,
    },
  });

  const handleTipChange = (value: number) => {
    if (value === 0) {
      setTip(0);
      setValue('tip', 0);
    } else if (value > tipLimitAmount) {
      setTip(tipLimitAmount);
      setValue('tip', tipLimitAmount);
    } else if (value >= 100) {
      setTip(value);
      setValue('tip', value);
    } else {
      // Keep previous tip value if input is invalid
      setValue('tip', tip);
    }
  };

  return (
    <div className="flex w-full flex-col items-center gap-4 bg-green-10 p-3">
      <div className="flex w-full items-center justify-center gap-4">
        <div>
          <span className="text-bold-21 text-orange-200">チップ</span>
          <span className="text-bold-21 text-green-300">を送って応援しよう！</span>
        </div>
        <ShopPublicImage src="/images/Coin.webp" alt="Coin" width={60} height={60} />
      </div>
      <div className="text-medium-12">
        日々コンテンツを作り、皆様を楽しませてくれている
        <br />
        クリエイターに追加でチップを送って応援してみませんか？
      </div>
      <div className="flex gap-3">
        {tipAmounts.map((amount) => (
          <InputTipButton key={amount} amount={amount} onTipSelect={handleTipChange} />
        ))}
      </div>
      <div className="flex gap-4">
        <div className="text-regular-11 text-green-300">
          ※手動でも金額は入力できます
          <br />
          ※限度額は1日300,000円(税込)です
          <br />
          ※今日送れる金額は残り¥{tipLimitAmount.toLocaleString()}円(税込)です
          <br />
          ※100円以上で入力してください
        </div>
        <div className="w-36 shrink-0">
          <NumberInput
            inputName="tip"
            prefix="¥"
            control={control}
            min={0}
            max={tipLimitAmount}
            inputClassName="text-regular-20 text-right w-full"
            error={!!errors.tip}
            required={false}
            shortInput
            coverValue={tip || ''}
            onBlur={(value) => {
              handleTipChange(Number(value) || 0);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TipRecommendationSection;
