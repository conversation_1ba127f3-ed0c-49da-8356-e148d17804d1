import React, { useRef } from 'react';
import clsx from 'clsx';
import Button from '@/components/atoms/button';
import SectionTitle from '@/components/atoms/typography/section-title';
import FixedBar from '@/components/layouts/FixedBar';
import BilledAmountInformationSection from '../sections/billed-amount-information-section';
import NotesSection from '../sections/notes-section';
import PaymentSelectionSection from '../sections/payment-selection-section';
import TipRecommendationSection from '../sections/tip-recommendation-section';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';

export interface OrderPageTemplateProps {
  children: React.ReactNode;
  tipLimitAmount: number;
  cartItemsTotalPrice: number;
  fee: number;
  tip: number;
  deliveryFee?: number;
  yellCount: number;
  boostRatio: number;
  hasShopCreatorActiveEvent: boolean;
  identityId: string;
  isValid: boolean;
  onProcessPayment: () => Promise<void>;
}

const OrderPageTemplate: React.FC<OrderPageTemplateProps> = ({
  children,
  tipLimitAmount,
  cartItemsTotalPrice,
  fee,
  tip,
  deliveryFee,
  yellCount,
  boostRatio,
  hasShopCreatorActiveEvent,
  identityId,
  isValid,
  onProcessPayment,
}) => {
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv);
  return (
    <div className={clsx('relative flex flex-col items-center justify-center', { ['pb-30']: !isFixed })} ref={midDiv}>
      <TipRecommendationSection tipLimitAmount={tipLimitAmount} />
      <div className="flex flex-col items-center justify-center">
        <section className="section-double-border mb-4 w-full p-4">
          <BilledAmountInformationSection
            cartItemsTotalPrice={cartItemsTotalPrice}
            fee={fee}
            tip={tip}
            deliveryFee={deliveryFee}
            yellCount={yellCount}
            boostRatio={boostRatio}
            hasShopCreatorActiveEvent={hasShopCreatorActiveEvent}
          />
        </section>
        <section className="section-double-border mb-4 w-full px-4 pb-4">
          <SectionTitle title="お支払い方法" className="mb-4" />
          <PaymentSelectionSection identityId={identityId} />
        </section>
        {children}
        <section className="w-full p-4">
          <NotesSection />
        </section>
      </div>
      <FixedBar isFixed={isFixed} alignCenter={true}>
        <Button
          onClick={onProcessPayment}
          buttonType={isValid ? 'primary' : 'disabled'}
          buttonSize="lg"
          disabled={!isValid}
        >
          注文を確定する
        </Button>
      </FixedBar>
    </div>
  );
};

export default OrderPageTemplate;
