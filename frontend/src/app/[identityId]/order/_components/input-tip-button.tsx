'use client';

import React from 'react';
import Button from '@/components/atoms/button';

interface InputTipButtonProps {
  amount: number;
  onTipSelect: (value: number) => void;
}

const InputTipButton = ({ amount, onTipSelect }: InputTipButtonProps) => {
  const handleClick = () => {
    onTipSelect(amount);
  };

  return (
    <Button
      buttonType={'light-shadow'}
      buttonSize="free"
      buttonClassNames={'w-20 h-9 text-bold-16 text-green-300'}
      onClick={handleClick}
    >
      ¥{amount.toLocaleString()}
    </Button>
  );
};

export default InputTipButton;
