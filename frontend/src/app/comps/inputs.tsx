'use client';

import { useForm } from 'react-hook-form';
import DateInput from '@/components/atoms/inputs/date-input';
import NumberInput from '@/components/atoms/inputs/number-input';
import SelectInput from '@/components/atoms/inputs/select-input';
import TelInput from '@/components/atoms/inputs/tel-input';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import TextInput from '@/components/atoms/inputs/text-input';

const Inputs = () => {
  const { control, watch } = useForm({
    defaultValues: {
      text: 'test',
      date: '2025-01-01T00:00',
      select: 'test',
      number: 100,
      textArea: 'test',
      tel: '09012345678',
    },
  });
  return (
    <div className="m-auto flex w-1/2 flex-col items-center">
      <h2 className="mb-6 text-bold-18">Inputs</h2>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import TextInput from '@/components/atoms/inputs/text-input';`}</code>
        </pre>
      </div>
      <TextInput control={control} inputName="text" />
      <pre>
        <code>{`<TextInput control={control} inputName="text" />`}</code>
      </pre>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import DateInput from '@/components/atoms/inputs/date-input';`}</code>
        </pre>
      </div>
      <DateInput name="date" value={watch('date')} />
      <pre>
        <code>{`<DateInput name="date" value={watch('date')} />`}</code>
      </pre>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import SelectInput from '@/components/atoms/inputs/select-input';`}</code>
        </pre>
      </div>
      <SelectInput
        control={control}
        inputName="select"
        labelTitle="select"
        options={[{ label: 'test', value: 'test' }]}
      />
      <pre>
        <code>
          {`<SelectInput
        control={control}
        inputName="select"
        labelTitle="select"
        options={[{ label: 'test', value: 'test' }]}
      />`}
        </code>
      </pre>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import NumberInput from '@/components/atoms/inputs/number-input';`}</code>
        </pre>
      </div>
      <NumberInput control={control} inputName="number" defaultValue={100} coverValue={watch('number')} />
      <pre>
        <code>{`<NumberInput control={control} inputName="number" defaultValue={100} coverValue={watch('number')} />`}</code>
      </pre>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import TextAreaInput from '@/components/atoms/inputs/text-area-input';`}</code>
        </pre>
      </div>
      <TextAreaInput control={control} inputName="textArea" defaultValue="test" />
      <pre>
        <code>{`<TextAreaInput control={control} inputName="textArea" defaultValue="test" />`}</code>
      </pre>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import TelInput from '@/components/atoms/inputs/tel-input';`}</code>
        </pre>
      </div>
      <TelInput control={control} inputName="tel" defaultValue="09012345678" />
      <pre>
        <code>{`<TelInput control={control} inputName="tel" defaultValue="09012345678" />`}</code>
      </pre>
    </div>
  );
};

export default Inputs;
