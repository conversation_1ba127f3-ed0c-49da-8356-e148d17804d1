'use client';
import GoodsIcon from '@/components/atoms/badges/goods-icon';
import GoodsTypesBadge from '@/components/atoms/badges/goods-types-badge';
import NewIcon from '@/components/atoms/badges/new-icon';
import StateBadge from '@/components/atoms/badges/state-badge';
import Tag from '@/components/atoms/badges/tag';
import ShopPublicImage from '@/components/ShopImage';

const BadgesExample = () => {
  return (
    <div className="m-auto flex w-1/2 flex-col items-center">
      <h2 className="mb-6 text-bold-18">NEWS Badge</h2>
      <div className="mb-5">
        <NewIcon />
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`<NewIcon />`}</code>
        </pre>
      </div>
      <h2 className="mb-6 text-bold-18">Goods Icon Badge</h2>
      <div className="mb-5 flex w-full items-center justify-center gap-2 bg-slate-200 p-2">
        <GoodsIcon type="audio" />
        <GoodsIcon type="video" />
        <GoodsIcon type="image" />
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`<GoodsIcon type="audio" />
<GoodsIcon type="video" />
<GoodsIcon type="image" />`}</code>
        </pre>
      </div>
      <h2 className="mb-6 text-bold-18">State Badge</h2>
      <div className="mb-5 grid w-full grid-cols-2 items-center justify-center gap-2 bg-slate-200 p-2">
        <StateBadge type="round-filled" color="white" size="sm">
          <div className="flex gap-1 px-1">
            <ShopPublicImage src="/images/icons/Time.svg" alt="time" width={12} height={12} />
            期間限定
          </div>
        </StateBadge>
        <StateBadge type="round-filled" color="orange" size="lg">
          <div className="flex items-center gap-1 p-2 pl-1">
            <ShopPublicImage
              src="/images/icons/Time.svg"
              alt="time"
              width={12}
              height={12}
              className="brightness-1000"
            />
            〜2024/10/10 23:59
          </div>
        </StateBadge>
        <StateBadge type="round-filled" color="green" size="lg">
          <div className="flex items-center gap-1 p-2 pl-0.5">
            <ShopPublicImage
              src="/images/icons/Sale.svg"
              alt="time"
              width={16}
              height={16}
              className="brightness-1000"
            />
            〜2024/10/10 23:59
          </div>
        </StateBadge>
        <StateBadge type="round-filled" color="blue" size="lg">
          <div className="flex items-center gap-1 p-2 pl-1 pr-2">
            <ShopPublicImage
              src="/images/icons/Time.svg"
              alt="time"
              width={12}
              height={12}
              className="brightness-1000"
            />
            2024/10/10 12:00〜
          </div>
        </StateBadge>
        <StateBadge type="square-lined" color="lined-green" size="sm">
          購入済み
        </StateBadge>
        <StateBadge type="square-lined" color="lined-orange" size="sm">
          追加済み
        </StateBadge>
        <StateBadge type="half-round-filled" color="pink">
          <ShopPublicImage
            src="/images/icons/Stock.svg"
            alt="left"
            width={16}
            height={16}
            className="mr-1.5 brightness-1000"
          />
          残り点数
        </StateBadge>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`interface IStateBadgeProps {
  type: 'round-filled' | 'square-lined' | 'half-round-filled';
  size?: 'sm' | 'lg';
  color: 'white' | 'orange' | 'green' | 'blue' | 'pink' | 'lined-green' | 'lined-orange';
  children: React.ReactNode;
}`}</code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 期間限定 round-filled
<StateBadge type="round-filled" color="white" size="sm">
  <div className="p2-2 flex gap-1 pl-1 pr-1">
    <ShopPublicImage src="/images/icons/Time.svg" alt="time" width={12} height={12} />
    期間限定
  </div>
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 〜2024/10/10 23:59 round-filled
<StateBadge type="round-filled" color="orange" size="lg">
  <div className="p2-2 flex items-center gap-1 pl-1 pr-2">
    <ShopPublicImage src="/images/icons/Time.svg" alt="time" width={12} height={12} className="brightness-1000" />
    〜2024/10/10 23:59
  </div>
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 〜2024/10/10 23:59 round-filled
<StateBadge type="round-filled" color="green" size="lg">
  <div className="p2-2 flex items-center gap-1 pl-0.5 pr-2">
    <ShopPublicImage src="/images/icons/Sale.svg" alt="time" width={16} height={16} className="brightness-1000" />
    〜2024/10/10 23:59
  </div>
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 〜2024/10/10 23:59 round-filled
<StateBadge type="round-filled" color="blue" size="lg">
  <div className="p2-2 flex items-center gap-1 pl-1 pr-2">
    <ShopPublicImage src="/images/icons/Time.svg" alt="time" width={12} height={12} className="brightness-1000" />
    〜2024/10/10 23:59
  </div>
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 〜2024/10/10 23:59 round-filled
<StateBadge type="round-filled" color="blue" size="lg">
  <div className="p2-2 flex items-center gap-1 pl-1 pr-2">
    <ShopPublicImage src="/images/icons/Time.svg" alt="time" width={12} height={12} className="brightness-1000" />
    〜2024/10/10 23:59
  </div>
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 購入済み square-lined
<StateBadge type="square-lined" color="lined-green" size="sm">
  購入済み
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 追加済み square-lined
<StateBadge type="square-lined" color="lined-orange" size="sm">
  追加済み
</StateBadge>`}
          </code>
        </pre>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>
            {`// 残り数点 half-round-filled
<StateBadge type="half-round-filled" color="pink">
  <ShopPublicImage src="/images/icons/Stock.svg" alt="left" width={16} height={16} className="brightness-1000 mr-1.5" />
  残り数点
</StateBadge>`}
          </code>
        </pre>
      </div>
      <h2 className="mb-6 text-bold-18">Goods Types Badge</h2>
      <div className="mb-5">
        <GoodsTypesBadge activeImage={true} activeVideo={false} activeAudio={true} activeTokuten={true} />
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`<GoodsTypesBadge activeImage={true} activeVideo={false} activeAudio={true} activeTokuten={true} />`}</code>
        </pre>
      </div>
      <h2 className="mb-6 text-bold-18">Tags</h2>
      <div className="mb-2">
        <Tag isEditable={true}>This is a tag</Tag>
      </div>
      <div className="mb-5">
        <Tag isEditable={false}>This is a tag</Tag>
      </div>
      <div className="mb-3 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`<Tag isEditable={true}>This is a tag</Tag>
<Tag isEditable={false}>This is a tag</Tag>`}</code>
        </pre>
      </div>
    </div>
  );
};

export default BadgesExample;
