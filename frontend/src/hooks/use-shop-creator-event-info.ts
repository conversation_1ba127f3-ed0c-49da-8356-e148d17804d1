import { useGetCreatorActive } from '@/lib/client-api/ranking-event-info-endpoint/ranking-event-info-endpoint';

export const useShopCreatorEventInfo = (identityId: string, totalPrice: number) => {
  const { data: rankingEventData } = useGetCreatorActive(identityId);

  const hasShopCreatorActiveEvent = rankingEventData?.data?.event?.rankingEvent != null;

  const boostRatio = rankingEventData?.data?.event?.boost?.boostRatio ?? 1;
  const isBoostActive = boostRatio > 1;

  const yellCount = totalPrice;

  return {
    yellCount,
    boostRatio,
    hasShopCreatorActiveEvent,
    isBoostActive,
  };
};
