import { useCallback, useState, useMemo } from 'react';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import CustomToast from '@/components/atoms/toast/custom-toast';
import type { ItemForGetItem, CreateSingleOrderRequest } from '@/lib/client-api/client-shop-api.schemas';
import { createOrder } from '@/lib/client-api/single-order-endpoint/single-order-endpoint';
import { PAYMENT_METHODS } from '@/consts/order';
import { usePaymentExecution } from '@/hooks/use-payment-execution';
import { googleAnalyticsForEc } from '@/services/googleAnalyticsForEc';
import type { CartItem } from '@/types/cart';
import type { PaymentExecutor, OrderPaymentResult } from '@/types/order';
import type { BasePaymentCallbacks, BasePaymentProcessProps } from '@/types/payment';
import type { MediaType } from '@/types/shopItem';

type GachaInfo = {
  itemId: number;
  item: ItemForGetItem | null;
};

type GachaOrderCallbacks = BasePaymentCallbacks & {
  checkPullableGachaStatus: (onCloseNavigateTo?: string) => Promise<boolean | undefined>;
};

type UseGachaOrderProcessProps = BasePaymentProcessProps & {
  item: GachaInfo;
  quantity: number;
  callbacks: GachaOrderCallbacks;
};

export const useGachaOrderProcess = ({
  payment: { paymentMethod, selectedCard, convenienceParam },
  item,
  amount: { tip, totalAmount },
  identityId,
  quantity,
  callbacks: { clearOtherPaymentParams, setConvenienceResult, checkPullableGachaStatus },
  trigger,
}: UseGachaOrderProcessProps) => {
  const router = useRouter();
  const { executePayment, createApplePaySession, executeApplePayPayment } = usePaymentExecution();
  const [isProcessing, setIsProcessing] = useState(false);

  const gachaOrderExecutor = useMemo<PaymentExecutor<CreateSingleOrderRequest, OrderPaymentResult>>(
    () => ({
      createOrder: async (orderData: CreateSingleOrderRequest): Promise<OrderPaymentResult> => {
        // TODO: 将来的にすべての支払い関連の型はorvalで生成された型に統一する
        const response = await createOrder({
          itemId: item.itemId,
          quantity: quantity,
          tip: orderData.tip,
          paymentMethod: orderData.paymentMethod,
          cardParam: orderData.cardParam,
          convenienceParam: orderData.convenienceParam,
          googlePayParam: orderData.googlePayParam,
          applePayParam: orderData.applePayParam,
        });

        if (!response.data?.order?.order?.id) {
          throw new Error('注文情報が見つかりません');
        }

        return {
          statusText: 'success',
          data: {
            orderId: response.data.order.order.id.toString(),
            redirectUrl: response.data.order.redirectUrl || undefined,
            convenienceCheckout: response.data.order.convenienceCheckout
              ? {
                  convenience: response.data.order.convenienceCheckout.convenience,
                  confNo: response.data.order.convenienceCheckout.confNo,
                  receiptNo: response.data.order.convenienceCheckout.receiptNo,
                  paymentTerm: response.data.order.convenienceCheckout.paymentTerm,
                  receiptUrl: response.data.order.convenienceCheckout.receiptUrl || null,
                }
              : undefined,
          },
        };
      },
    }),
    [item.itemId, quantity],
  );

  const handlePaymentSuccess = useCallback(
    async (result: OrderPaymentResult, paymentMethod: PAYMENT_METHODS) => {
      if (!result.data?.orderId) {
        throw new Error('注文情報が見つかりません');
      }

      if (item.item) {
        const gaItem: CartItem = {
          cartId: 0,
          cartItemId: 0,
          itemId: item.item.id ?? 0,
          itemType: item.item.itemType ?? 0,
          title: item.item.name ?? '',
          type: 'image' as MediaType,
          price: item.item.price ?? 0,
          currentPrice: item.item.price ?? 0,
          marginRate: 0,
          discountRate: 0,
          quantity: quantity,
          thumbnail: item.item.thumbnailUri ?? '',
          thumbnailRatio: 1,
          fileType: 'image' as MediaType,
        };

        await googleAnalyticsForEc.purchase(result.data.orderId, totalAmount, [gaItem]);
      }

      if (paymentMethod === PAYMENT_METHODS.PAY_PAY) {
        if (result.data.redirectUrl) {
          window.location.href = result.data.redirectUrl;
          return;
        }
      } else if (paymentMethod === PAYMENT_METHODS.CONVENIENCE) {
        if (result.data.convenienceCheckout) {
          setConvenienceResult({
            convenience: result.data.convenienceCheckout.convenience,
            confNo: result.data.convenienceCheckout.confNo,
            receiptNo: result.data.convenienceCheckout.receiptNo,
            paymentTerm: result.data.convenienceCheckout.paymentTerm,
            receiptUrl: result.data.convenienceCheckout.receiptUrl ?? null,
          });
          router.push(`/payment/convenience-store/success?identityId=${identityId}`);
          return;
        }
      } else if (paymentMethod === PAYMENT_METHODS.CARD) {
        if (result.data.redirectUrl) {
          window.location.href = result.data.redirectUrl;
          return;
        } else {
          throw new Error('決済処理に失敗しました');
        }
      } else {
        router.push(`/${identityId}/item/${item.itemId}/result/gacha`);
      }
    },
    [router, item, totalAmount, identityId, setConvenienceResult, quantity],
  );

  // TODO use-cart-order-process.tsと共通化できる場合は、use-payment-execution.tsに移行する
  const routePaymentExecution = useCallback(
    async (orderData: CreateSingleOrderRequest, paymentMethod: PAYMENT_METHODS, totalAmount: number) => {
      // Apple Payはセキュリティ要件により、ユーザーのジェスチャー（クリックなど）に直接紐づいた
      // イベントハンドラー内でセッションを作成する必要があり。
      // 非同期処理を挟んだり、別のコールバック内で作成しようとすると下記エラーが発生
      // "Must create a new ApplePaySession from a user gesture handler"
      if (paymentMethod === PAYMENT_METHODS.APPLE_PAY) {
        const { canMakePayments, session } = createApplePaySession(totalAmount);
        if (!canMakePayments) {
          throw new Error('このデバイスではApple Payが利用できません');
        }
        return await executeApplePayPayment(session, orderData, gachaOrderExecutor);
      }

      return await executePayment<CreateSingleOrderRequest, OrderPaymentResult>({
        orderData,
        paymentMethod,
        totalAmount,
        executor: gachaOrderExecutor,
      });
    },
    [createApplePaySession, executeApplePayPayment, executePayment, gachaOrderExecutor],
  );

  const processPayment = useCallback(async () => {
    if (isProcessing) return;
    setIsProcessing(true);

    try {
      if (!paymentMethod) {
        alert('お支払い方法を選択してください');
        return;
      }

      const isValid = await trigger();
      if (!isValid) {
        return;
      }

      if (paymentMethod !== PAYMENT_METHODS.APPLE_PAY) {
        const isGachaValid = await checkPullableGachaStatus(`/${identityId}`);
        if (!isGachaValid) {
          return;
        }
      }

      clearOtherPaymentParams(paymentMethod);

      const singleOrderData: CreateSingleOrderRequest = {
        itemId: item.itemId,
        quantity: quantity,
        tip: tip,
        paymentMethod: paymentMethod,
        cardParam:
          paymentMethod === PAYMENT_METHODS.CARD && selectedCard?.cardSequence != null
            ? { cardSequence: selectedCard.cardSequence }
            : undefined,
        convenienceParam:
          paymentMethod === PAYMENT_METHODS.CONVENIENCE
            ? {
                convenience: convenienceParam.convenience,
                customerName: convenienceParam.customerName,
                customerKana: convenienceParam.customerKana,
                telNo: convenienceParam.telNo,
              }
            : undefined,
        googlePayParam: paymentMethod === PAYMENT_METHODS.GOOGLE_PAY ? { token: '' } : undefined,
        applePayParam: paymentMethod === PAYMENT_METHODS.APPLE_PAY ? { token: '' } : undefined,
      };

      const result = await routePaymentExecution(singleOrderData, paymentMethod, totalAmount);
      await handlePaymentSuccess(result, paymentMethod);
    } catch (error: any) {
      const errorCode = error?.response?.data?.errors?.[0]?.code;
      if (errorCode === 1102) {
        toast.custom((t) => CustomToast(t, 'error', '指定された回数は不正な値です'), {
          id: 'gacha-unique-pull-limit',
        });
        router.push(`/${identityId}/item/${item.itemId}`);
        return;
      }
      toast.custom((t) => CustomToast(t, 'error', error?.message || '処理に失敗しました'), {
        id: 'transaction-failed',
      });
      console.error('Process failed:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [
    isProcessing,
    paymentMethod,
    trigger,
    checkPullableGachaStatus,
    clearOtherPaymentParams,
    item.itemId,
    tip,
    selectedCard?.cardSequence,
    convenienceParam,
    totalAmount,
    handlePaymentSuccess,
    quantity,
    routePaymentExecution,
    router,
    identityId,
  ]);

  return {
    processPayment,
    isProcessing,
  };
};
