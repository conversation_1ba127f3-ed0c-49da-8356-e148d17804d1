import React, { useEffect, useState } from 'react';
import { useModalsStore } from '@/store/useModals';

const CONVENIENCE_DIALOG_CONFIRMED_KEY = 'convenience_dialog_confirmed';

/**
 * Custom hook for displaying the convenience store payment fee dialog
 */
export const useConvenienceDialog = () => {
  const { onModalClose, onModalOpen, setModalProps } = useModalsStore();
  const [isConfirmed, setIsConfirmed] = useState<boolean>(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const confirmed = localStorage.getItem(CONVENIENCE_DIALOG_CONFIRMED_KEY) === 'true';
      setIsConfirmed(confirmed);
    }
  }, []);

  const convenienceDialogBody = (
    <div className="p-6 pt-11 text-center">
      <div className="mb-4 text-medium-16 text-error">料金改定のお知らせ</div>
      <p className="text-medium-14">
        2025年6月25日より、 コンビニ決済時の事務手数料は 決済金額にかかわらず 一律275円 (税込) に改定しました。
      </p>
    </div>
  );

  const openConvenienceDialog = (callback: () => void) => {
    if (isConfirmed) {
      callback();
      return;
    }

    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      onConfirm: () => {
        if (typeof window !== 'undefined') {
          localStorage.setItem(CONVENIENCE_DIALOG_CONFIRMED_KEY, 'true');
          setIsConfirmed(true);
        }
        callback();
        onModalClose();
      },
      type: 'error',
      children: convenienceDialogBody,
      confirmText: '同意して決済手続きを行う',
      buttonType: 'dark',
    });
  };

  return { openConvenienceDialog };
};
