import { useState, useEffect, RefObject, useRef } from 'react';

/**
 * スクロール時にボタンを固定表示するためのカスタムフック
 * @param buttonRef ボタン要素への参照
 * @param triggerCondition フックをアクティブにする条件（オプション）
 * @param stopFloatRef フローティング停止のトリガーとなる要素への参照（オプション）
 * @returns フローティング状態を管理するための状態
 */
export const useFloatingButton = (
  buttonRef: RefObject<HTMLElement>,
  triggerCondition: boolean = true,
  stopFloatRef?: RefObject<HTMLElement>,
): { isScrolled: boolean; isVisible: boolean } => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const isScrolledRef = useRef(isScrolled);
  const initialButtonDocumentTopRef = useRef<number | null>(null); // ボタンの初期のドキュメント上部からの位置

  useEffect(() => {
    isScrolledRef.current = isScrolled;
  }, [isScrolled]);

  useEffect(() => {
    // ボタンの初期のドキュメント上部からの位置を一度だけ取得・保存
    if (buttonRef.current && initialButtonDocumentTopRef.current === null) {
      initialButtonDocumentTopRef.current = buttonRef.current.getBoundingClientRect().top + window.scrollY;
    }

    const handleScroll = () => {
      if (!buttonRef.current || !triggerCondition) {
        setIsScrolled(false);
        setIsVisible(false);
        return;
      }

      const scrollY = window.scrollY;

      if (initialButtonDocumentTopRef.current === null) {
        // 初期位置がまだ取得できていない場合は何もしないか、動的に取得
        if (buttonRef.current) {
          initialButtonDocumentTopRef.current = buttonRef.current.getBoundingClientRect().top + window.scrollY;
        } else return; // buttonRefがない場合は処理不可
      }
      const buttonInitialDocTop = initialButtonDocumentTopRef.current as number; // この時点ではnullでないはず

      // フローティング開始のスクロール位置
      // ボタンの上端がビューポート上部から48px以内に入った時
      const floatActivationScrollY = buttonInitialDocTop - 48;

      // フローティング停止のスクロール位置
      let floatInactivationScrollY: number | null = null;
      if (stopFloatRef?.current) {
        const stopElementDocumentTop = stopFloatRef.current.getBoundingClientRect().top + scrollY;
        floatInactivationScrollY = stopElementDocumentTop - 48;
      }

      const currentIsScrolled = isScrolledRef.current;

      const shouldBeScrolledFinal = scrollY >= floatActivationScrollY;
      let shouldBeStoppedFinal = false;

      if (floatInactivationScrollY !== null && scrollY >= floatInactivationScrollY) {
        shouldBeStoppedFinal = true;
      }

      if (shouldBeStoppedFinal) {
        if (currentIsScrolled) {
          setIsVisible(false);
          setIsScrolled(false);
        }
      } else {
        if (shouldBeScrolledFinal && !currentIsScrolled) {
          setIsScrolled(true);
          setTimeout(() => setIsVisible(true), 50);
        } else if (!shouldBeScrolledFinal && currentIsScrolled) {
          setIsVisible(false);
          setIsScrolled(false);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初期表示時にも一度評価

    return () => window.removeEventListener('scroll', handleScroll);
  }, [buttonRef, stopFloatRef, triggerCondition]);

  return { isScrolled, isVisible };
};

export default useFloatingButton;
