import { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import { useExhibitsStore } from '@/store/useExhibit';
import { MAX_UPLOAD_GACHA_BENEFITS } from '@/consts/inputLength';
import { useUploadProgress } from '@/hooks/useUploadProgress';
import {
  getAvailableConditions as getAvailableConditionsUtil,
  FormValues,
  type BenefitItem,
} from '@/utils/gacha-benefit-utils';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import {
  ConditionType,
  CONDITION_TYPE,
  type GachaBenefit,
  type GachaBenefitFile,
  CONDITION_TYPE_TIMES,
} from '@/types/gacha';
import { ItemFiles, MediaType, ExhibitType } from '@/types/shopItem';

const DEFAULT_CONDITION = CONDITION_TYPE.TIMES_10;

export const useGachaBenefit = ({ itemType }: { itemType?: ExhibitType } = {}) => {
  const { control, setValue } = useForm<FormValues>({
    defaultValues: {
      benefits: {},
    },
  });

  const useExhibits = useExhibitsStore();
  const { exhibits, setBenefits } = useExhibits;
  const params = useParams();
  const itemId = params.id as string;

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { benefits = [], isDuplicated = true, itemFiles } = (exhibitItem as ExhibitGachaItem) ?? {};

  const [textLengths, setTextLengths] = useState<{ [key: string]: number }>({});
  const [isCompBenefitEnabled] = useState(true);
  const { uploadProgress, handleProgress } = useUploadProgress();

  const benefitItems = useMemo(() => {
    const items: BenefitItem[] = benefits.map((benefit, index) => ({
      ...benefit,
      id: index,
      uploadProgress: uploadProgress[index.toString()] || 0,
    }));

    return items;
  }, [benefits, uploadProgress]);

  const createEmptyBenefit = useCallback(
    (conditionType: ConditionType = DEFAULT_CONDITION): GachaBenefit => ({
      id: 0,
      description: '',
      conditionType,
      files: [],
      benefitFiles: [],
    }),
    [],
  );

  const updateStoreBenefits = useCallback(
    (benefitId: number, updatedData: Partial<GachaBenefit>, shouldCheck = true) => {
      const existingBenefits = [...benefits];
      if (benefitId >= existingBenefits.length) {
        return;
      }

      existingBenefits[benefitId] = {
        ...existingBenefits[benefitId],
        id: benefitId + 1,
        ...updatedData,
      };

      if (!shouldCheck) {
        setBenefits(itemId, existingBenefits);
        return;
      }

      const hasFiles = updatedData.benefitFiles?.length || existingBenefits[benefitId]?.benefitFiles?.length;
      if (hasFiles) {
        setBenefits(itemId, existingBenefits);
      }
    },
    [benefits, itemId, setBenefits],
  );

  const handleConditionChange = useCallback(
    (benefitId: number, selectedCondition: ConditionType) => {
      setValue(`benefits.${benefitId}.conditionType`, selectedCondition);

      // Update text lengths for form validation
      const currentBenefit = benefitItems.find((item) => item.id === benefitId);
      setTextLengths((prev) => ({
        ...prev,
        [benefitId]: currentBenefit?.description?.length || 0,
      }));

      const updatedBenefits = [...benefits];
      while (updatedBenefits.length <= benefitId) {
        const newBenefit = createEmptyBenefit();
        updatedBenefits.push(newBenefit);
      }

      // 以前のconditionTypeを保存
      const previousConditionType = updatedBenefits[benefitId]?.conditionType || DEFAULT_CONDITION;

      updatedBenefits[benefitId] = {
        ...updatedBenefits[benefitId],
        id: benefitId + 1,
        conditionType: selectedCondition,
        benefitFiles:
          benefits[benefitId]?.benefitFiles?.map((file) => ({
            ...file,
            conditionType: selectedCondition,
          })) || [],
      };

      // 変更されたconditionTypeが以前より大きい場合のみ、後続項目を調整
      if (selectedCondition > previousConditionType) {
        for (let i = benefitId + 1; i < updatedBenefits.length; i++) {
          const laterBenefit = updatedBenefits[i];
          if (
            laterBenefit.conditionType < selectedCondition &&
            laterBenefit.conditionType !== CONDITION_TYPE.COMPLETED
          ) {
            updatedBenefits[i] = {
              ...laterBenefit,
              conditionType: selectedCondition,
              benefitFiles:
                laterBenefit.benefitFiles?.map((file) => ({
                  ...file,
                  conditionType: selectedCondition,
                })) || [],
            };
            setValue(`benefits.${i}.conditionType`, selectedCondition);
          }
        }
      }

      setBenefits(itemId, updatedBenefits);
    },
    [setValue, benefitItems, benefits, createEmptyBenefit, itemId, setBenefits],
  );

  const handleDescriptionChange = useCallback(
    (benefitId: number, description: string) => {
      setValue(`benefits.${benefitId}.description`, description);
      setTextLengths((prev) => ({ ...prev, [benefitId]: description.length }));

      updateStoreBenefits(benefitId, { description });
    },
    [setValue, updateStoreBenefits],
  );

  const getNextConditionType = useCallback((): Exclude<ConditionType, 100> => {
    if (benefits.length > 0) {
      const lastBenefit = benefits[benefits.length - 1];
      if (lastBenefit.conditionType !== CONDITION_TYPE.COMPLETED) {
        return lastBenefit.conditionType as Exclude<ConditionType, 100>;
      }
    }

    return DEFAULT_CONDITION;
  }, [benefits]);

  const handleFileUpload = useCallback(
    (benefitId: number, files: ItemFiles) => {
      const currentBenefit = benefitItems.find((item) => item.id === benefitId);
      const conditionType = currentBenefit?.conditionType || DEFAULT_CONDITION;
      const gachaBenefitFiles: GachaBenefitFile[] = files.map((file) => ({
        id: file.id,
        name: file.title,
        objectUri: file.src,
        fileType: file.type || 'image',
        thumbnailUri: file.preSignedThumbnailUrl,
        title: file.title,
        src: file.src,
        type: (file.type as MediaType) || 'image',
        size: file.size,
        duration: file.duration,
        thumbnail: file.preSignedThumbnailUrl,
        conditionType,
        sortOrder: file.sortIndex,
        isLoading: file.isLoading || false,
      }));

      const currentBenefits = [...benefits];

      while (currentBenefits.length <= benefitId) {
        const newBenefit = createEmptyBenefit();
        currentBenefits.push(newBenefit);
      }

      currentBenefits[benefitId] = {
        ...currentBenefits[benefitId],
        id: benefitId + 1,
        description: currentBenefit?.description || '',
        conditionType,
        benefitFiles: gachaBenefitFiles,
      };

      setBenefits(itemId, currentBenefits);
    },
    [benefitItems, benefits, itemId, setBenefits, createEmptyBenefit],
  );

  const handleFileDelete = useCallback(
    (benefitId: number) => {
      updateStoreBenefits(benefitId, { benefitFiles: [] }, false);
    },
    [updateStoreBenefits],
  );

  const handleUploadProgress = useCallback(
    (benefitId: number, progress: number) => {
      handleProgress(benefitId.toString(), progress);
    },
    [handleProgress],
  );

  const deleteBenefit = useCallback(
    (benefitId: number) => {
      if (benefits.length <= 1) return;

      // benefitIdは表示用のインデックスで、benefitItemsのidに対応
      // benefitItems内の自動追加された空の特典を削除しようとしている場合は何もしない
      if (benefitId >= benefits.length) {
        return;
      }

      // benefits配列から対応する項目を削除
      const remainingBenefits = benefits.filter((_, index) => index !== benefitId);
      const reorderedBenefits = remainingBenefits.map((benefit, index) => {
        // benefitFilesが空の場合はidを0に設定、そうでなければ通常のid（index + 1）
        const hasFiles = benefit.benefitFiles && benefit.benefitFiles.length > 0;
        return {
          ...benefit,
          id: hasFiles ? index + 1 : 0,
        };
      });

      setBenefits(itemId, reorderedBenefits);

      const newFormValues: FormValues['benefits'] = {};
      const newTextLengths: { [key: string]: number } = {};

      reorderedBenefits.forEach((benefit, index) => {
        newFormValues[index] = {
          conditionType: benefit.conditionType,
          description: (benefit as BenefitItem).description || '',
        };
        newTextLengths[index] = (benefit as BenefitItem).description?.length || 0;
      });

      setValue('benefits', newFormValues);
      setTextLengths(newTextLengths);
    },
    [benefits, itemId, setBenefits, setValue],
  );

  const addBenefitManually = useCallback(() => {
    if (benefits.length >= MAX_UPLOAD_GACHA_BENEFITS) return;

    const nextConditionType = getNextConditionType();
    const newBenefit = createEmptyBenefit(nextConditionType);
    const updatedBenefits = [...benefits, newBenefit];

    setBenefits(itemId, updatedBenefits);
  }, [benefits, itemId, setBenefits, createEmptyBenefit, getNextConditionType]);

  useEffect(() => {
    const formValues: FormValues['benefits'] = {};
    const initialTextLengths: { [key: string]: number } = {};

    benefitItems.forEach((item) => {
      const itemId = item.id ?? 0;
      formValues[itemId] = {
        conditionType: item.conditionType,
        description: item.description || '',
      };
      initialTextLengths[itemId] = item.description?.length || 0;
    });

    setValue('benefits', formValues);
    setTextLengths(initialTextLengths);
  }, [benefitItems, setValue]);

  useEffect(() => {
    if (benefits.length === 0) return;

    let needsUpdate = false;
    const updatedBenefits = [...benefits];

    if (!isDuplicated && itemFiles.length < CONDITION_TYPE_TIMES.TIMES_10) {
      updatedBenefits.forEach((benefit, index) => {
        if (benefit.conditionType !== CONDITION_TYPE.COMPLETED) {
          updatedBenefits[index] = {
            ...benefit,
            conditionType: CONDITION_TYPE.COMPLETED,
            benefitFiles:
              benefit.benefitFiles?.map((file) => ({
                ...file,
                conditionType: CONDITION_TYPE.COMPLETED,
              })) || [],
          };
          needsUpdate = true;
        }
      });
    } else if (isDuplicated) {
      // When isDuplicated is true, change COMPLETED back to DEFAULT_CONDITION
      updatedBenefits.forEach((benefit, index) => {
        if (benefit.conditionType === CONDITION_TYPE.COMPLETED) {
          updatedBenefits[index] = {
            ...benefit,
            conditionType: DEFAULT_CONDITION,
            benefitFiles:
              benefit.benefitFiles?.map((file) => ({
                ...file,
                conditionType: DEFAULT_CONDITION,
              })) || [],
          };
          needsUpdate = true;
        }
      });
    }

    if (needsUpdate) {
      setBenefits(itemId, updatedBenefits);

      // Update form values
      const formValues: FormValues['benefits'] = {};
      updatedBenefits.forEach((benefit, index) => {
        formValues[index] = {
          conditionType: benefit.conditionType,
          description: (benefit as BenefitItem).description || '',
        };
        setValue(`benefits.${index}.conditionType`, benefit.conditionType);
      });
      setValue('benefits', formValues);
    }
  }, [isDuplicated, itemFiles?.length, benefits, itemId, setBenefits, setValue]);

  return {
    // State
    benefitItems,
    textLengths,
    isCompBenefitEnabled,
    isDuplicated,

    // Form
    control,

    // Handlers
    handleConditionChange,
    handleDescriptionChange,
    handleFileUpload,
    handleFileDelete,
    handleUploadProgress,
    deleteBenefit,
    addBenefitManually,

    // Utils
    getAvailableConditions: (benefitId: number) => {
      const itemFilesCount = (exhibitItem as ExhibitGachaItem)?.itemFiles?.length || 0;
      return getAvailableConditionsUtil(
        benefitId,
        benefitItems,
        isCompBenefitEnabled,
        isDuplicated,
        itemFilesCount,
        itemType,
      );
    },
  };
};
