import { useState, useCallback } from 'react';
import { gachaRequests } from '@/services/gachaRequests';
import type { GachaItemFile, GachaItemContent } from '@/types/gacha';

export const usePullGacha = (itemId: string, identityId: string) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPulledFiles, setCurrentPulledFiles] = useState<GachaItemFile[]>([]);
  const [selectedAwardFileId, setSelectedAwardFileId] = useState<string | null>(null);
  const [gachaCollection, setGachaCollection] = useState<GachaItemContent | null>(null);

  const pullGacha = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await gachaRequests.pullGacha({
        itemId: parseInt(itemId),
        identityId,
      });
      if (!response.data) {
        throw new Error(response.error || 'ガチャの結果を取得できませんでした');
      }
      const { currentPulledFiles = [], item: gachaCollection, selectedAwardFileId } = response.data;
      setCurrentPulledFiles(currentPulledFiles);
      setGachaCollection(gachaCollection);
      setSelectedAwardFileId(selectedAwardFileId ?? null);
    } catch (error) {
      setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  }, [itemId, identityId]);

  return {
    isLoading,
    setIsLoading,
    error,
    currentPulledFiles,
    selectedAwardFileId,
    gachaCollection,
    pullGacha,
  };
};
