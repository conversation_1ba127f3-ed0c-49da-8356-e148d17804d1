import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { MAX_SINGLE_IMAGE_SIZE } from '@/consts/sizes';
import { compressImage, randomString, resetEscapedValue } from '@/utils/base';
import { uploadThumbnail } from '@/utils/thumbnail';

export const useHandleSetCustomThumbnail = () => {
  const [customThumbnailUploadUrl, setCustomThumbnailUploadUrl] = useState<string>('');

  const handleSetCustomThumbnail = useCallback(
    async (
      options: {
        title?: string;
        itemId: string;
        setThumbnailCustomImage: (itemId: string, url: string) => void;
        file?: File;
      },
      e?: React.ChangeEvent<HTMLInputElement>,
    ) => {
      const { title, setThumbnailCustomImage, itemId } = options;

      const file = e ? e.target.files?.[0] : options.file;
      if (!file) return;

      if (file.size > MAX_SINGLE_IMAGE_SIZE) {
        toast.custom((t) => CustomToast(t, 'error', '10MB以下の画像を選択してください。'), {
          id: 'upload-custom-thumbnail',
        });
        return;
      }

      let fileUrl = '';
      let compressedFileUrl = '';

      try {
        const compressedFile = (await compressImage(file, true)) as File;

        fileUrl = URL.createObjectURL(file);
        compressedFileUrl = URL.createObjectURL(compressedFile);

        const imgTitle = title ?? randomString();
        const customImage = await uploadThumbnail({
          img: compressedFile,
          fileId: imgTitle,
          fileName: resetEscapedValue(imgTitle) + new Date().getTime(),
          existUploadUrl: customThumbnailUploadUrl,
          isPublic: true,
        });

        setCustomThumbnailUploadUrl(customImage.uploadUrl);
        setThumbnailCustomImage(itemId, customImage.url);
      } catch (error) {
        toast.custom((t) => CustomToast(t, 'error', 'サムネイルのアップロードに失敗しました'), {
          id: 'upload-custom-thumbnail-error',
        });
        throw error;
      } finally {
        if (e?.target) e.target.value = '';
        if (fileUrl) URL.revokeObjectURL(fileUrl);
        if (compressedFileUrl) URL.revokeObjectURL(compressedFileUrl);
      }
    },
    [customThumbnailUploadUrl],
  );

  return {
    handleSetCustomThumbnail,
  };
};
