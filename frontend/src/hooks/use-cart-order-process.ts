import { useCallback, useState, useMemo } from 'react';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { PAYMENT_METHODS } from '@/consts/order';
import { usePaymentExecution } from '@/hooks/use-payment-execution';
import { googleAnalyticsForEc } from '@/services/googleAnalyticsForEc';
import { orderService } from '@/services/order';
import type { CreateOrderRequest } from '@/types/api/order';
import type { CartItem } from '@/types/cart';
import type { PaymentExecutor, OrderPaymentResult } from '@/types/order';
import type { BasePaymentCallbacks, BasePaymentProcessProps } from '@/types/payment';

type CartInfo = {
  cartId: number | undefined;
  cartItemIds: number[];
  cartItems: CartItem[];
};

type CartOrderCallbacks = BasePaymentCallbacks & {
  checkCartItems: () => Promise<boolean | undefined>;
};

type UseCartOrderProcessProps = BasePaymentProcessProps & {
  cart: CartInfo;
  callbacks: CartOrderCallbacks;
};

export const useCartOrderProcess = ({
  payment: { paymentMethod, selectedCard, convenienceParam },
  cart: { cartId, cartItemIds, cartItems },
  amount: { tip, totalAmount },
  identityId,
  callbacks: { clearOtherPaymentParams, setConvenienceResult, checkCartItems },
  trigger,
}: UseCartOrderProcessProps) => {
  const router = useRouter();
  const { executePayment, createApplePaySession, executeApplePayPayment } = usePaymentExecution();
  const [isProcessing, setIsProcessing] = useState(false);

  const cartOrderExecutor = useMemo<PaymentExecutor<CreateOrderRequest, OrderPaymentResult>>(
    () => ({
      createOrder: async (orderData: CreateOrderRequest): Promise<OrderPaymentResult> => {
        // TODO: 将来的にすべての支払い関連の型はorvalで生成された型に統一する
        // createOrderも、orvalで生成されたhttp clientを使用する
        const response = await orderService.createOrder(orderData);
        return {
          statusText: response.statusText === 'success' ? 'success' : 'error',
          data: {
            orderId: response.data?.orderId?.toString(),
            redirectUrl: response.data?.redirectUrl || undefined,
            convenienceCheckout: response.data?.convenienceCheckout
              ? {
                  convenience: response.data.convenienceCheckout.convenience,
                  confNo: response.data.convenienceCheckout.confNo,
                  receiptNo: response.data.convenienceCheckout.receiptNo,
                  paymentTerm: response.data.convenienceCheckout.paymentTerm,
                  receiptUrl: response.data.convenienceCheckout.receiptUrl || null,
                }
              : undefined,
          },
        };
      },
    }),
    [],
  );

  const handlePaymentSuccess = useCallback(
    async (result: OrderPaymentResult, paymentMethod: PAYMENT_METHODS) => {
      if (!result.data?.orderId) {
        throw new Error('注文情報が見つかりません');
      }

      await googleAnalyticsForEc.purchase(result.data.orderId, totalAmount, cartItems);

      if (paymentMethod === PAYMENT_METHODS.PAY_PAY) {
        if (result.data.redirectUrl) {
          window.location.href = result.data.redirectUrl;
          return;
        }
      } else if (paymentMethod === PAYMENT_METHODS.CONVENIENCE) {
        if (result.data.convenienceCheckout) {
          setConvenienceResult({
            convenience: result.data.convenienceCheckout.convenience,
            confNo: result.data.convenienceCheckout.confNo,
            receiptNo: result.data.convenienceCheckout.receiptNo,
            paymentTerm: result.data.convenienceCheckout.paymentTerm,
            receiptUrl: result.data.convenienceCheckout.receiptUrl || null,
          });
          router.push(`/payment/convenience-store/success?identityId=${identityId}`);
          return;
        }
      } else if (paymentMethod === PAYMENT_METHODS.CARD) {
        if (result.statusText === 'success' && result.data?.redirectUrl) {
          await googleAnalyticsForEc.purchase(result.data.orderId, totalAmount, cartItems);
          window.location.href = result.data.redirectUrl;
          return;
        } else {
          toast.custom((t) => CustomToast(t, 'error', '決済処理に失敗しました'), {
            id: 'transaction-failed',
          });
          return;
        }
      } else {
        router.push(`/${identityId}/order/success`);
      }
    },
    [router, cartItems, totalAmount, identityId, setConvenienceResult],
  );

  const routePaymentExecution = useCallback(
    async (orderData: CreateOrderRequest, paymentMethod: PAYMENT_METHODS, totalAmount: number) => {
      // Apple Payはセキュリティ要件により、ユーザーのジェスチャー（クリックなど）に直接紐づいた
      // イベントハンドラー内でセッションを作成する必要があり。
      // 非同期処理を挟んだり、別のコールバック内で作成しようとすると下記エラーが発生
      // "Must create a new ApplePaySession from a user gesture handler"
      if (paymentMethod === PAYMENT_METHODS.APPLE_PAY) {
        const { canMakePayments, session } = createApplePaySession(totalAmount);
        if (!canMakePayments) {
          throw new Error('このデバイスではApple Payが利用できません');
        }
        return await executeApplePayPayment(session, orderData, cartOrderExecutor);
      }

      return await executePayment({
        orderData,
        paymentMethod,
        totalAmount,
        executor: cartOrderExecutor,
      });
    },
    [createApplePaySession, executeApplePayPayment, executePayment, cartOrderExecutor],
  );

  const processPayment = useCallback(async () => {
    if (isProcessing) return;
    setIsProcessing(true);

    try {
      if (!paymentMethod) {
        alert('お支払い方法を選択してください');
        return;
      }

      const isValid = await trigger();
      if (!isValid) {
        return;
      }

      // Apple Payの場合は一旦スキップ
      if (paymentMethod !== PAYMENT_METHODS.APPLE_PAY) {
        const isCartValid = await checkCartItems();
        if (!isCartValid) {
          return;
        }
      }

      clearOtherPaymentParams(paymentMethod);

      const orderData: CreateOrderRequest = {
        cartId: Number(cartId),
        cartItemIds: cartItemIds.map(Number),
        tip: tip,
        paymentMethod: paymentMethod,
        googlePayParam: paymentMethod === PAYMENT_METHODS.GOOGLE_PAY ? { token: '' } : undefined,
        cardParam:
          paymentMethod === PAYMENT_METHODS.CARD && selectedCard?.cardSequence != null
            ? { cardSequence: selectedCard.cardSequence }
            : undefined,
        convenienceParam:
          paymentMethod === PAYMENT_METHODS.CONVENIENCE
            ? {
                convenience: convenienceParam.convenience,
                customerName: convenienceParam.customerName,
                customerKana: convenienceParam.customerKana,
                telNo: convenienceParam.telNo,
              }
            : undefined,
        applePayParam: paymentMethod === PAYMENT_METHODS.APPLE_PAY ? { token: '' } : undefined,
      };

      const result = await routePaymentExecution(orderData, paymentMethod, totalAmount);
      await handlePaymentSuccess(result, paymentMethod);
    } catch (error: any) {
      alert(error?.message || '決済処理に失敗しました');
      console.error('Payment failed:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [
    convenienceParam,
    isProcessing,
    paymentMethod,
    cartId,
    cartItemIds,
    tip,
    selectedCard,
    totalAmount,
    clearOtherPaymentParams,
    checkCartItems,
    routePaymentExecution,
    handlePaymentSuccess,
    trigger,
  ]);

  return {
    processPayment,
    isProcessing,
  };
};
