import { useCallback } from 'react';
import { PAYMENT_METHODS } from '@/consts/order';
import { APPLE_MERCHANT_ID, GOOGLE_GATEWAY_ID, TEST_GOOGLE_PAY_TOKEN } from '@/consts/payment';
import { orderService } from '@/services/order';
import type { ExecutePaymentParams, PaymentExecutor, OrderPaymentResult } from '@/types/order';
import type { BasePaymentParams, GooglePaymentRequest } from '@/types/payment';
declare global {
  interface Window {
    ApplePaySession: any;
    google: {
      payments: {
        api: {
          PaymentsClient: any;
        };
      };
    };
  }
}

export const usePaymentExecution = () => {
  const executeGooglePayPayment = async <T extends BasePaymentParams>(
    orderData: T,
    totalAmount: number,
    executor: PaymentExecutor<T, OrderPaymentResult>,
  ): Promise<OrderPaymentResult> => {
    if (process.env.APP_ENV === 'development') {
      orderData.googlePayParam = { token: TEST_GOOGLE_PAY_TOKEN };
    } else {
      const baseRequest: GooglePaymentRequest = {
        apiVersion: 2,
        apiVersionMinor: 0,
      };

      const tokenizationSpecification = {
        type: 'PAYMENT_GATEWAY',
        parameters: {
          gateway: 'gmopg',
          gatewayMerchantId: GOOGLE_GATEWAY_ID,
        },
      };

      const allowedCardNetworks = ['AMEX', 'DISCOVER', 'JCB', 'MASTERCARD', 'VISA'];
      const allowedCardAuthMethods = ['PAN_ONLY'];
      const baseCardPaymentMethod = {
        type: 'CARD',
        parameters: {
          allowedAuthMethods: allowedCardAuthMethods,
          allowedCardNetworks: allowedCardNetworks,
        },
      };

      const cardPaymentMethod = Object.assign(
        { tokenizationSpecification: tokenizationSpecification },
        baseCardPaymentMethod,
      );

      const paymentsClient = new window.google.payments.api.PaymentsClient({
        environment: process.env.APP_ENV === 'production' ? 'PRODUCTION' : 'TEST',
      });

      const paymentDataRequest: GooglePaymentRequest = Object.assign({}, baseRequest);
      paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod];
      paymentDataRequest.transactionInfo = {
        totalPriceStatus: 'FINAL',
        totalPrice: `${totalAmount}`,
        currencyCode: 'JPY',
        countryCode: 'JP',
      };

      paymentDataRequest.merchantInfo = {
        merchantName: 'FANME',
        merchantId: GOOGLE_GATEWAY_ID,
      };

      const response = await paymentsClient.loadPaymentData(paymentDataRequest);
      const googleToken = response.paymentMethodData.tokenizationData.token;
      const parseToken = JSON.parse(googleToken);
      const token = btoa(JSON.stringify(parseToken));

      orderData.googlePayParam = { token };
    }

    return executor.createOrder(orderData);
  };

  const createApplePaySession = (totalAmount: number): { canMakePayments: boolean; session: any } => {
    if (!window.ApplePaySession) {
      throw new Error('Apple Pay is not supported');
    }

    // Apple Payのセキュリティ要件により、セッションの作成はユーザーのジェスチャーイベントハンドラー内で
    // 直接行う必要があります。そのため、この関数は他の支払い処理とは異なり、
    // ユーザーアクションに直接紐づけて呼び出す必要があります。
    const merchantIdentifier = APPLE_MERCHANT_ID;
    const canMakePayments = window.ApplePaySession.canMakePaymentsWithActiveCard(merchantIdentifier);

    const session = new window.ApplePaySession(14, {
      merchantIdentifier: merchantIdentifier,
      displayName: 'FANME',
      supportedNetworks: ['visa', 'masterCard', 'amex', 'discover'],
      merchantCapabilities: ['supports3DS'],
      countryCode: 'JP',
      currencyCode: 'JPY',
      total: {
        label: 'FANME',
        amount: totalAmount,
      },
    });

    return { canMakePayments, session };
  };

  const executeApplePayPayment = async <T extends BasePaymentParams>(
    session: any,
    orderData: T,
    executor: PaymentExecutor<T, OrderPaymentResult>,
  ): Promise<OrderPaymentResult> => {
    return new Promise((resolve, reject) => {
      session.onvalidatemerchant = async (event: any) => {
        try {
          const validationData = await orderService.getApplePayToken(event.validationURL);
          session.completeMerchantValidation(validationData);
        } catch (error) {
          console.error('Apple Pay validation error:', error);
          reject(new Error('Apple Payが利用できません'));
        }
      };

      session.onpaymentauthorized = async (event: any) => {
        try {
          const paymentToken = event.payment.token.paymentData;
          const objJsonStr = JSON.stringify(paymentToken);
          orderData.applePayParam!.token = window.btoa(objJsonStr);

          const result = await executor.createOrder(orderData);
          if (result.statusText === 'success' && result.data) {
            session.completePayment(window.ApplePaySession.STATUS_SUCCESS);
            resolve(result);
          } else {
            session.completePayment(window.ApplePaySession.STATUS_FAILURE);
            reject(new Error('決済処理に失敗しました'));
          }
        } catch (error) {
          session.completePayment(window.ApplePaySession.STATUS_FAILURE);
          reject(error);
        }
      };

      session.oncancel = () => {
        reject(new Error('決済がキャンセルされました'));
      };

      session.begin();
    });
  };

  const executeCreditCardPayment = async <T extends BasePaymentParams>(
    orderData: T,
    executor: PaymentExecutor<T, OrderPaymentResult>,
  ): Promise<OrderPaymentResult> => {
    return executor.createOrder(orderData);
  };

  const executePayPayPayment = async <T extends BasePaymentParams>(
    orderData: T,
    executor: PaymentExecutor<T, OrderPaymentResult>,
  ): Promise<OrderPaymentResult> => {
    return executor.createOrder(orderData);
  };

  const executeConveniencePayment = async <T extends BasePaymentParams>(
    orderData: T,
    executor: PaymentExecutor<T, OrderPaymentResult>,
  ): Promise<OrderPaymentResult> => {
    return executor.createOrder(orderData);
  };

  /**
   * 決済処理を実行する
   * @template TRequest - カート決済（CreateOrderRequest）またはガチャ決済（CreateSingleOrderRequest）のリクエスト型
   * @template TResponse - カート決済（CartPaymentResult）またはガチャ決済（GachaPaymentResult）のレスポンス型
   * @param params - 決済実行に必要なパラメータ
   * @returns カートまたはガチャの決済結果
   */
  const executePayment = useCallback(
    async <TRequest extends BasePaymentParams, TResponse>({
      orderData,
      paymentMethod,
      totalAmount,
      executor,
    }: ExecutePaymentParams<TRequest, TResponse>): Promise<TResponse> => {
      // TODO アサーションを削除
      try {
        switch (paymentMethod) {
          case PAYMENT_METHODS.GOOGLE_PAY:
            return executeGooglePayPayment(
              orderData,
              totalAmount,
              executor as PaymentExecutor<TRequest, OrderPaymentResult>,
            ) as unknown as TResponse;
          case PAYMENT_METHODS.CARD:
            return executeCreditCardPayment(
              orderData,
              executor as PaymentExecutor<TRequest, OrderPaymentResult>,
            ) as unknown as TResponse;
          case PAYMENT_METHODS.PAY_PAY:
            return executePayPayPayment(
              orderData,
              executor as PaymentExecutor<TRequest, OrderPaymentResult>,
            ) as unknown as TResponse;
          case PAYMENT_METHODS.CONVENIENCE:
            return executeConveniencePayment(
              orderData,
              executor as PaymentExecutor<TRequest, OrderPaymentResult>,
            ) as unknown as TResponse;
          default:
            throw new Error(`Unsupported payment method: ${paymentMethod}`);
        }
      } catch (error) {
        throw error;
      }
    },
    [],
  );

  return {
    executePayment,
    createApplePaySession,
    executeApplePayPayment,
  };
};
