import { useSyncExternalStore } from 'react';
import { useIsPC<PERSON>rowser } from './useIsPCBrowser';

export const useIsLandscape = () => {
  const isPCBrowser = useIsPCBrowser();

  const getIsLandscapeFromMediaQuery = () => {
    if (typeof window === 'undefined') return false;

    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
    const isMobileContext = isMobileUserAgent || !isPCBrowser;
    const mediaQueryResult = window.matchMedia('(orientation: landscape)').matches;

    return isMobileContext ? mediaQueryResult : false;
  };

  const subscribeToOrientationChanges = (notifyOrientationChanged: () => void) => {
    const landscapeMediaQuery = window.matchMedia('(orientation: landscape)');

    landscapeMediaQuery.addEventListener('change', notifyOrientationChanged);

    // ウィンドウサイズ変化も監視（DevToolsでspとpcのビューを切り替えた際も検知するため）
    window.addEventListener('resize', notifyOrientationChanged);

    return () => {
      landscapeMediaQuery.removeEventListener('change', notifyOrientationChanged);
      window.removeEventListener('resize', notifyOrientationChanged);
    };
  };

  const getServerSideLandscapeValue = () => false;

  return useSyncExternalStore(subscribeToOrientationChanges, getIsLandscapeFromMediaQuery, getServerSideLandscapeValue);
};
