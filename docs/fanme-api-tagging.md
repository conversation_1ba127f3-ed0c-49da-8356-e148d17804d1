# FANME API タグ付けガイドライン

## 目的

fanme-apiのエンドポイントをわかりやすく管理するため、OpenAPIドキュメント上でのタグ付けを統一する。

fanme-apiは別サーバーにホストしているため、shop-apiとは区別して管理する必要がある。このタグ付けにより、フロントエンドからのアクセス先を適切に分けることができる。

## ルール

fanme-api用に作成されたエンドポイントクラスには、必ず次のタグを追加する。

```kotlin
@Tag(name = "FANME", description = "FANME APIサーバー")
```

このタグを付けることで、OpenAPIドキュメントでfanme-apiのエンドポイントがまとめて表示される。

## 対象となるファイル

次のディレクトリにあるエンドポイントクラスはすべて対象。

```
backend/src/main/kotlin/jp/co/torihada/fanme/endpoints/fanme/
```


新しくエンドポイントを追加するときも、忘れずにこのタグをつけること。

## API生成について（orval.config）

`orval.config`に以下の設定を行うことで、fanme-apiのエンドポイントだけを別ディレクトリに生成している。

```typescript
fanmeApi: {
  output: {
    mode: "tags-split",
    target: "./src/lib/fanme-api/fanme-api.ts",
    client: "swr",
    override: {
      mutator: {
        path: "./config/custom-fanme-instance.ts",
        name: "customFanmeInstance",
      },
    },
  },
  input: {
    target: "../backend/META-INF/openapi.yaml",
    filters: {
      mode: "include",
      tags: ["FANME"],
    },
  },
},
```

これにより、`FANME`タグがついたエンドポイントだけが以下に生成される。

```
./src/lib/fanme-api/
```

新しいAPIを追加するときは、この設定を考慮してタグ付けすること。
