---
description: API endpoints and communication patterns
globs:
---
# API Structure

## Endpoints
- /shops/*              # Shop management
- /shops/*/items/*      # Item management
- /shops/*/cart-items/* # Cart operations
- /orders/*            # Order management
- /cards/*             # Payment management

## Communication Flow
- Standard: frontend -> bff -> backend
- Server Actions: frontend -> backend
