---
description: Architecture and module organization
globs:
---

# Architecture Rules

## Module Structure
- fanme/     # Core functionality
- shop/      # Shop related features
- payment/   # Payment processing

## Dependencies
- shop -> fanme
- shop -> payment
- fanme -> payment

## Naming Conventions
- *.controller.ts        # Controllers
- *.service.ts          # Services
- *.model.ts            # Domain models
- *.repository.ts       # Data access
