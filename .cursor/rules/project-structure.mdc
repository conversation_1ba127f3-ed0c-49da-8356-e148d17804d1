---
description: Project structure
globs: 
---
---
description: Project structure and file organiza:
---

# Project StruDocumentation References
- [shop.yaml](mdc:fanme-shop/fanme-shop/fanme-shop/backend/docs/endpoints/shop.yaml) # API endpoint specifications
- [README.md](mdc:fanme-shop/fanme-shop/fanme-shop/backend/README.md) # Backend architecture and implementation details

## Path Patterns
Frontend:
- frontend/**/*.tsx                 # Next.js components and pages
- frontend/**/*.ts                  # TypeScript utility files
- frontend/src/app/**              # App router pages
- frontend/src/components/**       # React components

Backend:
- backend/src/main/kotlin/**       # Kotlin source files
- backend/src/test/kotlin/**       # Kotlin test files
