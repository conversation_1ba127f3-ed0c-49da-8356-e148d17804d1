---
description:
globs:
alwaysApply: false
---
# OpenAPI & Orval Integration Rules

## Overview
このプロジェクトでは、バックエンドAPIとの連携にOpenAPI仕様とOrvalを使用しています。
これにより、型安全なAPIクライアントとデータフェッチングフックが自動生成されます。
ただし、すべての型定義が自動生成されているわけではなく、手動定義の型と自動生成の型が混在しています。

## Directory Structure
```
frontend/src/
├── lib/
│   └── client-api/
│       ├── client-shop-api.schemas.ts  # OpenAPIから生成された型定義
│       └── {domain}-endpoint/          # 各ドメインのAPI Hooks
│           └── {domain}-endpoint.ts    # 自動生成されたフック
├── types/                             # 手動で定義された型
└── hooks/
    └── swr/                          # カスタムSWRフック
```

## Generated Code Usage

### 型定義の使い分け
```typescript
// ✅ APIレスポンスの型を使用する場合
import type { Item, Shop, Order } from '@/lib/client-api/client-shop-api.schemas';

// ✅ アプリケーション固有の型を使用する場合
import type { UserPreferences } from '@/types/user';  // 手動定義の型

// ❌ 避けるべき方法
import type { Item } from '@/types/item';  // APIレスポンスの型を手動で再定義
```

### データフェッチングと型の整合性
```typescript
// ✅ 正しい使用方法：Orvalで生成されたフックと型を使用
import { useGetItem } from '@/lib/client-api/item-endpoint/item-endpoint';
import type { Item } from '@/lib/client-api/client-shop-api.schemas';

// ✅ 正しい使用方法：生成されたフックをラップしたカスタムフック
import { useGetItem } from '@/lib/client-api/item-endpoint/item-endpoint';
import { useCustomItemHook } from '@/hooks/useCustomItemHook';

// ❌ 避けるべき方法：APIレスポンスの型を手動定義しながらOrvalのフックを使用
import { useGetItem } from '@/lib/client-api/item-endpoint/item-endpoint';
import type { Item } from '@/types/item';  // 不整合の可能性あり
```

## 重要なファイル
- @file: frontend/src/lib/client-api/client-shop-api.schemas.ts
  - OpenAPIから生成された型定義
  - APIレスポンスとリクエストの型定義
  - 手動での編集は禁止

- @file: frontend/src/lib/client-api/item-endpoint/item-endpoint.ts
  - 商品関連のAPI Hooks
  - `useGetItem`, `useCreateItem`などのフックを提供
  - これらのフックが返すデータは必ずschemas.tsの型を使用

## 型定義の優先順位
1. APIとの通信に関わる型 → `client-shop-api.schemas.ts`を使用
2. アプリケーション固有のロジックの型 → `@/types/`以下に手動定義
3. コンポーネントのProps型など → 各コンポーネントファイル内で定義

## 開発ワークフロー
1. バックエンドチームがOpenAPI仕様を更新
2. `yarn generate:api`でクライアントコードを生成
3. 生成された型とフックを適切に使用
   - APIレスポンスの型は生成された型を使用
   - アプリケーション固有の型は手動定義を使用

## 注意事項
- APIレスポンスの型を手動で再定義しない
- Orvalで生成されたフックを使用する場合は、必ず対応する生成された型を使用する
- アプリケーション固有の型（UIの状態など）は従来通り手動で定義する
- カスタムフックを作成する場合は、生成されたフックと型を正しく使用する

## エラー処理
生成されたフックは以下の形式でエラーハンドリングを提供します：
```typescript
const { data, error } = useGetItem(itemId);
if (error) {
  // エラーハンドリング
}
```
