name: BUILD

on:
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  build:
    name: ビルド実行
    runs-on: ubuntu-latest
    steps:
      - run: echo "ビルドを開始します"

  call-frontend-build:
    name: frontendビルド
    needs: build
    uses: ./.github/workflows/build_frontend.yml
    secrets:
      AUTH_TOKEN_FOR_GITHUBPKG: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
  #      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

  call-backend-build:
    name: backendビルド
    needs: build
    uses: ./.github/workflows/build_backend.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}

  update-charts:
    name: chartsアップデート
    runs-on: ubuntu-latest
    needs:
      [
        call-frontend-build,
        call-backend-build,
      ]
    steps:
      - name: Generate token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.TOKEN_APP_ID }}
          private-key: ${{ secrets.TOKEN_APP_PRIVATE_KEY }}

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}

      - name: Get current_sha
        run: echo "current_sha=${{ github.sha }}" >> $GITHUB_ENV

      - name: Update values.yaml
        uses: mikefarah/yq@master
        with:
          cmd: >
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-backend-fanme/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-backend-payment/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-backend-shop/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-frontend-shop/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-shop-batch/values.yaml

      - name: Set current datetime
        env:
          TZ: Asia/Tokyo
        run: >
          echo "current_datetime=$(date +'%Y%m%d%H%M%S')" >> $GITHUB_ENV &&
          echo "current_year=$(date +'%Y')" >> $GITHUB_ENV

      - name: Commit and push (main)
        if: ${{ success() && github.ref == 'refs/heads/main' }}
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "main branch build. Applied charts tag (${{ env.current_sha }})"
          file_pattern: '*.yaml'
      #          tagging_message: "release/${{ env.current_year }}/${{ env.current_datetime }}"

      - name: Commit and push (other branch)
        if: ${{ success() && github.ref != 'refs/heads/main' }}
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "${{ github.ref_name }} branch build. Applied charts tag (${{ env.current_sha }})"
          file_pattern: '*.yaml'
#          tagging_message: "build/${{ env.current_year }}/${{ env.current_datetime }}"

#      - name: Notify hash to Slack
#        if: success()
#        uses: rtCamp/action-slack-notify@master
#        env:
#          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
#          SLACK_MSG_AUTHOR: Build report
#          SLACK_COLOR: good
#          SLACK_MESSAGE: "${{ env.current_sha }}"
#          SLACK_USERNAME: "GitHub Actions"
#          SLACK_ICON_EMOJI: ":rocket:"
#
#      - name: Notify hash to Slack
#        if: failure()
#        uses: rtCamp/action-slack-notify@master
#        env:
#          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
#          SLACK_MSG_AUTHOR: Build report
#          SLACK_COLOR: "#ff0000"
#          SLACK_MESSAGE: "${{ env.current_sha }}"
#          SLACK_USERNAME: "GitHub Actions"
#          SLACK_ICON_EMOJI: ":pray:"
