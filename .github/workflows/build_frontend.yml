name: __frontend_build

on:
  workflow_call:
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL:
        required: true
      AUTH_TOKEN_FOR_GITHUBPKG:
        required: true
      SENTRY_AUTH_TOKEN:
        required: false

defaults:
  run:
    working-directory: ./frontend

permissions:
  id-token: write
  contents: read

env:
  AWS_ROLE_ARN: arn:aws:iam::638984414044:role/github-actions # GitHub Actions OIDC用IAMロール
  ECR_REGISTRY: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com # ECRレジストリURL
  ECR_REPOSITORY: fanme-shop-front
  SENTRY_ORG: torihada-inc
  SENTRY_PROJECT: fanme-shop-front
#  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          # schedule時はproduction
          ref: "${{ github.event_name == 'schedule' && 'production' || '' }}"

      - name: setup envs
        run: echo "current_sha=$(git log -1 '--format=format:%H')" >> $GITHUB_ENV

      - uses: docker/setup-buildx-action@v2

      - uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ap-northeast-1

      - uses: docker/login-action@v2
        with:
          registry: ${{ env.ECR_REGISTRY }}

      - name: create apple payment key
        run: |
          mkdir -p ./cert
          aws ssm get-parameter --name PS_APPLE_CERT_PEM --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./cert/cert.pem
          aws ssm get-parameter --name PS_APPLE_KEY_PEM --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./cert/key.pem

      - uses: docker/build-push-action@v4
        id: push_image
        with:
          push: true
          file: ./frontend/Dockerfile
          context: ./frontend/
          tags: |
            ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.current_sha || github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=min
          provenance: false
          build-args: |
            GITHUB_SHA=${{ github.sha }}
            NODE_AUTH_TOKEN=${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}
#            SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
#            SENTRY_ORG=${{env.SENTRY_ORG}}
#            SENTRY_PROJECT=fanme-shop-front

#      - name: Notify hash to Slack
#        if: success()
#        uses: rtCamp/action-slack-notify@master
#        env:
#          MSG_MINIMAL: true
#          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
#          SLACK_MSG_AUTHOR: Build report
#          SLACK_COLOR: good
#          SLACK_MESSAGE: ":+1::+1::+1::+1::+1: Build succeeded on SHOP_FRONTEND"
#          SLACK_USERNAME: "GitHub Actions"
#          SLACK_ICON_EMOJI: ":rocket:"
#
#      - name: Notify hash to Slack
#        if: failure()
#        uses: rtCamp/action-slack-notify@master
#        env:
#          MSG_MINIMAL: true
#          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
#          SLACK_MSG_AUTHOR: Build report
#          SLACK_COLOR: "#ff0000"
#          SLACK_MESSAGE: ":cold_face::cold_face::cold_face::cold_face::cold_face: Build failed on SHOP_FRONTEND"
#          SLACK_USERNAME: "GitHub Actions"
#          SLACK_ICON_EMOJI: ":rocket:"
