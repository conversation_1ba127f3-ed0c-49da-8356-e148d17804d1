name: __frontend_test
run-name: CI for Frontend

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      AUTH_TOKEN_FOR_GITHUBPKG:
        required: true

permissions:
  contents: read
  pull-requests: read

defaults:
  run:
    working-directory: ./frontend

jobs:
  ci_frontend:
    runs-on: ubuntu-22.04
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: install canvas dependencies
        run: sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev

      - uses: actions/setup-node@v3
        with:
          node-version: "20.17.0"
          registry-url: "https://npm.pkg.github.com"
          scope: "@torihada-inc"

      - name: Lint check
        run: npm i yarn -g && yarn --frozen-lockfile && yarn lint
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

      - name: TypeScript type check
        run: yarn test:types
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

      - name: Jest Tests
        run: yarn test
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

      - name: Build check
        run: yarn build
