name: __backend_test
run-name: CI for Backend

on:
  workflow_call:
    inputs:
      skip:
        type: string

permissions:
  contents: read
  pull-requests: read

defaults:
  run:
    working-directory: ./backend

jobs:
  ci_backend:
    runs-on: ubuntu-latest
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: "17"
          cache: "gradle"
          distribution: "adopt"

      - name: Lin<PERSON>, Detekt and Test (parallel)
        run: ./gradlew ktfmtCheck detekt test --info --parallel
