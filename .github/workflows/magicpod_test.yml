name: Magicpod TEST

on:
  workflow_run:
    workflows: ["BUILD"]
    types: [completed]
    branches: [main]

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  magic_pod_job:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}

    steps:
      # stgへ反映されるまで待つ
      - name: Wait 5 minutes
        run: sleep 300

      - name: Checkout
        uses: actions/checkout@v4

      - name: Run magicpod test
        env:
          MAGICPOD_API_TOKEN: ${{ secrets.MAGICPOD_API_TOKEN }}
        run: bash charts/_scripts/run_magicpod_test.sh
