# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2

registries:
  npm-github:
    type: npm-registry
    url: https://npm.pkg.github.com/
    token: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

updates:
  - package-ecosystem: 'gradle'
    directory: '/backend'
    schedule:
      interval: 'monthly'
    labels:
      - 'dependencies'
      - 'BACKEND'
  - package-ecosystem: "npm"
    directory: "/frontend"
    registries:
      - npm-github
    schedule:
      interval: "monthly"
    labels:
      - 'dependencies'
      - 'FRONTEND'
