#!/bin/sh
# ===== Settings =====
# 基本設定: 基準ブランチとその共通コミットを取得する
BASE_BRANCH=main
BASE_COMMIT=$(git merge-base HEAD $BASE_BRANCH)

# ===== Utils =====
# detect_changes: 指定されたディレクトリに変更があるかを確認する関数
detect_changes() {
  local directory="$1"
  git diff $BASE_COMMIT --name-only | grep -q "^$directory/"
}
# add_service: サービス名とコマンドを "name|command" 形式で SERVICES 配列に追加する関数
add_service() {
  local service_name="$1"
  local command="$2"
  SERVICES+=("$service_name|$command")
}

# ===== Initialize Service List =====
# SERVICES 配列の初期化（実行するサービスの一覧を保持する）
SERVICES=()

# ===== Backend Tests Setup =====
# バックエンドに変更があった場合、バックエンドのテストコマンドを設定する
if detect_changes "backend"; then
  BACKEND_DIR="cd backend"
  # BACKEND_SERVICES: 各バックエンドテストの名前とコマンドを "Name|Command" の形式で定義する
  BACKEND_SERVICES=("KtfmtCheck|./gradlew ktfmtCheck" "Detekt|./gradlew detekt" "Test|./gradlew test -PskipSlow=true --info")
  BACKEND_NAMES=""
  BACKEND_CMD_ARGS=""
  # 各バックエンドサービスについて、名前とコマンドを分解し連結する
  for service in "${BACKEND_SERVICES[@]}"; do
    name="${service%%|*}"
    cmd="${service#*|}"
    if [ -z "$BACKEND_NAMES" ]; then
      BACKEND_NAMES="$name"
      BACKEND_CMD_ARGS="\"$cmd\""
    else
      BACKEND_NAMES="$BACKEND_NAMES,$name"
      BACKEND_CMD_ARGS="$BACKEND_CMD_ARGS \"$cmd\""
    fi
  done
  # バックエンドのテスト実行コマンドを組み立て、SERVICES 配列に追加する
  BACKEND_CMD="$BACKEND_DIR && npx concurrently --group --names \"$BACKEND_NAMES\" $BACKEND_CMD_ARGS"
  add_service "Backend" "$BACKEND_CMD"
fi

# ===== Frontend Tests Setup =====
# フロントエンドに変更があった場合、フロントエンドのテストコマンドを設定する
if detect_changes "frontend"; then
  FRONTEND_DIR="cd frontend"
  # FRONTEND_SERVICES: 各フロントエンドテストの名前とコマンドを "Name|Command" の形式で定義する
  FRONTEND_SERVICES=("Lint|yarn lint" "TypeCheck|yarn test:types" "Test|yarn test")
  FRONTEND_NAMES=""
  FRONTEND_CMD_ARGS=""
  # 各フロントエンドサービスについて、名前とコマンドを分解し連結する
  for service in "${FRONTEND_SERVICES[@]}"; do
    name="${service%%|*}"
    cmd="${service#*|}"
    if [ -z "$FRONTEND_NAMES" ]; then
      FRONTEND_NAMES="$name"
      FRONTEND_CMD_ARGS="\"$cmd\""
    else
      FRONTEND_NAMES="$FRONTEND_NAMES,$name"
      FRONTEND_CMD_ARGS="$FRONTEND_CMD_ARGS \"$cmd\""
    fi
  done
  # フロントエンドのテスト実行コマンドを組み立て、SERVICES 配列に追加する
  FRONTEND_CMD="$FRONTEND_DIR && npx concurrently --group --names \"$FRONTEND_NAMES\" $FRONTEND_CMD_ARGS"
  add_service "Frontend" "$FRONTEND_CMD"
fi

# ===== Execute Services =====
# SERVICES 配列に格納された各サービスのテストコマンドを並列実行する
if [ ${#SERVICES[@]} -ne 0 ]; then
  ALL_NAMES=""
  ALL_CMDS=()
  # 各サービスから名前とコマンドを抽出し、実行用の引数を組み立てる
  for service in "${SERVICES[@]}"; do
    name="${service%%|*}"
    cmd="${service#*|}"
    if [ -z "$ALL_NAMES" ]; then
      ALL_NAMES="$name"
    else
      ALL_NAMES="$ALL_NAMES,$name"
    fi
    ALL_CMDS+=("$cmd")
  done
  # concurrently を使って、全てのテストコマンドを並列実行する
  npx concurrently --group --names "$ALL_NAMES" "${ALL_CMDS[@]}"
  RESULT=$?
  if [ $RESULT -ne 0 ]; then
    exit $RESULT
  fi
fi

exit 0
