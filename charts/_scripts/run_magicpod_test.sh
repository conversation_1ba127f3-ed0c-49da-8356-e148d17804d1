#!/bin/bash -e

# magicpod-apiクライアントの最新版を現在のディレクトリにダウンロード・解凍
# セキュリティのため、MAGICPOD_API_TOKENはGitHubの環境変数で設定
OS=linux
FILENAME=magicpod-api-client
curl -L "https://app.magicpod.com/api/v1.0/magicpod-clients/api/${OS}/latest/" -H "Authorization: Token ${MAGICPOD_API_TOKEN}" --output ${FILENAME}.zip
unzip -q ${FILENAME}.zip

# MagicPodで使う各種環境変数を設定
export MAGICPOD_ORGANIZATION=TORIHADA
export MAGICPOD_PROJECT=FANME

# 一括実行設定番号を使ってテスト一括実行（実行して終了、結果はSlackに通知される）
TEST_SETTING_NUMBER=7
./magicpod-api-client batch-run -n -S ${TEST_SETTING_NUMBER}
