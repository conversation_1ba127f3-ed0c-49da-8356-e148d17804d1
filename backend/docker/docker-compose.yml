name: fanme-shop-backend
services:
  app:
    image: openjdk:17
    environment:
      QUARKUS_ANALYTICS_DISABLED: true
      QUARKUS_PROFILE: docker
      KTOR_ENV: docker
      GRADLE_OPTS: "-Dorg.gradle.daemon=true -Dorg.gradle.parallel=true -Xmx2g -Xms1g"
      JAVA_OPTS: "-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseStringDeduplication"
    container_name: fanme-backend-app
    command: ./gradlew quarkusDev -Dquarkus.http.host=0.0.0.0 -Dquarkus.profile=local
    volumes:
      - ..:/app
      - gradle_cache:/app/.gradle
      - gradle_user_cache:/root/.gradle
    working_dir: /app
    ports:
      - "27000:8080"
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 6G
        reservations:
          cpus: '2'
          memory: 3G

  db:
    restart: on-failure
    image: mysql/mysql-server:8.0
    container_name: fanme-shop-db
    command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=512M --max-connections=200
    environment:
      MYSQL_DATABASE: shop
      MYSQL_ROOT_PASSWORD: pass
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./run/mysql:/var/lib/mysql
    ports:
      - "27001:3306"
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  valkey:
    image: valkey/valkey:8-alpine
    container_name: fanme-shop-valkey
    restart: on-failure
    command: valkey-server --appendonly yes
    volumes:
      - ./run/valkey:/data
    ports:
      - "11021:6379"
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

volumes:
  gradle_cache:
  gradle_user_cache:
