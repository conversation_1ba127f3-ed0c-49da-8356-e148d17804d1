package jp.co.torihada.fanme.modules.fanme.services.address

import io.quarkus.arc.DefaultBean
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.Config
import jp.co.torihada.fanme.modules.fanme.externals.client.kawa.SuggestAddressClient
import org.eclipse.microprofile.rest.client.inject.RestClient

@DefaultBean
@ApplicationScoped
class AddressService : IAddressService {

    @Inject @RestClient private lateinit var suggestAddressClient: SuggestAddressClient
    @Inject private lateinit var config: Config

    override fun suggestAddress(postalCode: String): SuggestedAddressEntity? {
        val response =
            suggestAddressClient.suggestAddress("Bearer ${config.kawaAuthToken()}", postalCode)
        return if (
            response?.addr_lv1 != null && response.addr_lv2 != null && response.addr_lv3 != null
        ) {
            SuggestedAddressEntity(response.addr_lv1, response.addr_lv2, response.addr_lv3)
        } else {
            null
        }
    }
}
