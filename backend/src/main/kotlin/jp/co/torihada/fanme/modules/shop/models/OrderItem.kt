package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.exception.ResourceNotFoundException

@PersistenceUnit(name = "order_item")
@Entity
@Table(name = "order_items")
class OrderItem : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "order_id", nullable = false, updatable = false)
    var order: Order = Order()

    @ManyToOne
    @JoinColumn(name = "cart_id", nullable = true, updatable = false)
    var cart: Cart? = null

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    var item: Item = Item()

    @NotNull @Column(name = "quantity") var quantity: Int = 0

    companion object : PanacheCompanion<OrderItem> {

        fun findBySingleOrderId(orderId: Long): OrderItem {
            return find("order.id", orderId).firstResult()
                ?: throw ResourceNotFoundException("OrderItem")
        }

        fun create(orderId: Long, itemId: Long, quantity: Int, cartId: Long? = null): OrderItem {
            val orderItem = OrderItem()
            orderItem.item = Item.findById(itemId)!!
            orderItem.order = Order.findById(orderId)!!
            orderItem.cart =
                if (cartId != null) {
                    Cart.findById(cartId)!!
                } else {
                    null
                }
            orderItem.quantity = quantity
            orderItem.persist()
            return orderItem
        }
    }
}
