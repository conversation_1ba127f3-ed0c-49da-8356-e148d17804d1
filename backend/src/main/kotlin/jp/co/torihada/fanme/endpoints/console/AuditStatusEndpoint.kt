package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.AuditStatusResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AuditStatusController
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/audit-groups/{audit-group-id}/status")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AuditStatusEndpoint {

    @Inject lateinit var handler: AuditStatusController

    data class UpdateStatusRequest(
        val status: Int,
        val comment: String? = null,
        val auditedUserUid: String? = null,
    )

    @PUT
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(AuditStatusResponseBody::class)
    @APIResponse(responseCode = "400", description = "Bad Request")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun updateAuditStatus(
        @PathParam("audit-group-id") auditGroupId: Long,
        @RequestBody(
            content = [Content(schema = Schema(implementation = UpdateStatusRequest::class))]
        )
        request: UpdateStatusRequest,
    ): Response {
        return try {
            val status =
                when (request.status) {
                    -1 -> AuditStatus.REJECTED
                    9 -> AuditStatus.APPROVED
                    else -> throw IllegalArgumentException("Invalid status: ${'$'}{request.status}")
                }
            handler.updateAuditStatus(
                auditGroupId = auditGroupId,
                status = status,
                comment = request.comment,
                auditedUserUid = request.auditedUserUid,
            )
            val entity = ResponseEntity(true, "result")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
