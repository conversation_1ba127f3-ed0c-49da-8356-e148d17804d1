package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.ProfileController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.usecases.profile.GetProfile

data class GetUserProfileInput(
    val accountIdentity: String,
    val currentUserUid: String,
    val currentUserRole: ConsoleUserRole,
)

data class UserProfileResponse(val profile: GetProfile.Profile)

@ApplicationScoped
class GetUserProfile
@Inject
constructor(
    private val profileController: ProfileController,
    private val securityUtils: SecurityUtils,
    private val userController: UserController,
) {

    fun execute(input: GetUserProfileInput): Result<UserProfileResponse, ConsoleException> {

        try {
            val currentConsoleUser =
                ConsoleUser.findByUserUid(input.currentUserUid)
                    ?: return Err(
                        ConsoleResourceNotFoundException("User not found: ${input.currentUserUid}")
                    )

            val targetUser =
                userController.getUserByAccountIdentity(input.accountIdentity)
                    ?: return Err(
                        ConsoleResourceNotFoundException("User not found: ${input.accountIdentity}")
                    )

            securityUtils.validateAgentForUserAccess(
                currentUserRole = input.currentUserRole.value,
                currentUserUid = input.currentUserUid,
                targetUser = targetUser,
            )

            val profile = profileController.getUserProfile(targetUser.uid!!)

            return Ok(UserProfileResponse(profile = profile))
        } catch (e: ConsoleException) {
            return Err(e)
        }
    }
}
