package jp.co.torihada.fanme.endpoints.console.request

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.NotNull

data class UpdateShopItemTypeMarginRatesRequest(val marginRates: List<ShopItemTypeMarginRateParam>)

data class ShopItemTypeMarginRateParam(
    val itemType: String,
    @field:NotNull @field:DecimalMin("0.0") @field:DecimalMax("1.0") val marginRate: Float,
)
