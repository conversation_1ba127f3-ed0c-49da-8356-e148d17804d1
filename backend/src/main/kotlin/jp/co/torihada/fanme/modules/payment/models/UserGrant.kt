package jp.co.torihada.fanme.modules.payment.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH

@Entity
@Table(name = "user_grants")
class UserGrant : BaseModel() {
    @NotNull
    @Column(name = "user_uid", nullable = false, length = USER_UID_MAX_LENGTH)
    var userUid: String = ""

    var name: String? = null

    @NotNull var amount: Int = 0

    @NotNull @Column(name = "payment_due_date") var paymentDueDate: Instant? = null

    companion object : PanacheCompanion<UserGrant> {
        fun findByPaymentDueDate(startDateTime: Instant, endDateTime: Instant): List<UserGrant> {
            return find("paymentDueDate >= ?1 and paymentDueDate <= ?2", startDateTime, endDateTime)
                .list()
        }
    }
}
