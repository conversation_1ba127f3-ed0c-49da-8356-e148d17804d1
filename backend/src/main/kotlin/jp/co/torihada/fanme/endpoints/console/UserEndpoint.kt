package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.HttpStatus
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.MonthlySellerSaleController
import jp.co.torihada.fanme.modules.console.controllers.TransactionController
import jp.co.torihada.fanme.modules.console.controllers.UserController
import jp.co.torihada.fanme.modules.console.usecases.GetUser
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.odata.OData
import kotlin.reflect.full.findAnnotation
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/users")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class UserEndpoint {

    @Inject lateinit var userController: UserController
    @Inject lateinit var monthlySellerSaleController: MonthlySellerSaleController
    @Inject lateinit var transactionController: TransactionController
    @Inject lateinit var requestContext: ContainerRequestContext

    data class UserResponse(val userResponse: GetUser.ConsoleGetUserResponse)

    data class UsersByPartialAccountIdentityResponse(
        val usersByPartialAccountIdentityResponse: List<User>
    )

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Path("/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    fun getUser(@PathParam("id") id: Long): BaseResponseBody<UserResponse> {

        val userResponse = userController.getUser(id)
        return BaseResponseBody(data = UserResponse(userResponse), errors = emptyList())
    }

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUsersByPartialAccountIdentity(
        @QueryParam("partial-account-identity") partialAccountIdentity: String?
    ): BaseResponseBody<UsersByPartialAccountIdentityResponse> {

        val usersByPartialAccountIdentityResponse =
            userController.getUsersByPartialAccountIdentity(partialAccountIdentity)
        return BaseResponseBody(
            data = UsersByPartialAccountIdentityResponse(usersByPartialAccountIdentityResponse),
            errors = emptyList(),
        )
    }

    @GET
    @Path("/{id}/sales/monthly")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUserMonthlySales(
        @PathParam("id") userId: Long,
        @QueryParam("from") from: String?,
        @QueryParam("to") to: String?,
    ): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val result = monthlySellerSaleController.getUserMonthlySales(userId, from, to, odata)
            val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
            val entity = ResponseEntity(result.monthlySalesList, "monthly_sales_list", metadata)
            Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            Response.status(status).entity(e).build()
        }
    }

    @GET
    @Path("/{id}/sales/transactions")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getUserTransaction(@PathParam("id") userId: Long): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val result = transactionController.getUserTransactions(userId, odata)
            val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
            val entity = ResponseEntity(result.purchases, "purchases", metadata)
            Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            Response.status(status).entity(e).build()
        }
    }
}
