package jp.co.torihada.fanme.modules.fanme.externals.client.salesforce

import jakarta.enterprise.context.ApplicationScoped
import jakarta.json.JsonObject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.Auth
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.CreateRecordRequestBody
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.SalesforceRecord
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.SaveRecordResponse
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

@ApplicationScoped
@RegisterRestClient(configKey = "salesforce")
interface SalesforceClient {

    @GET
    @Path("/services/oauth2/token")
    @Consumes(MediaType.APPLICATION_JSON)
    fun getToken(
        @QueryParam("client_id") clientId: String,
        @QueryParam("client_secret") clientSecret: String,
        @QueryParam("grant_type") grantType: String,
    ): Auth

    @GET
    @Path("/services/data/v62.0/query")
    @Consumes(MediaType.APPLICATION_JSON)
    fun executeQuery(
        @HeaderParam("Authorization") authorization: String,
        @QueryParam("q") queryString: String,
    ): JsonObject

    @POST
    @Path("/services/data/v62.0/composite/sobjects/")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createRecord(
        @HeaderParam("Authorization") authorization: String,
        body: CreateRecordRequestBody,
    ): List<SaveRecordResponse>

    @PATCH
    @Path("/services/data/v62.0/sobjects/{sobject}/{fieldName}/{fieldValue}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateRecord(
        @HeaderParam("Authorization") authorization: String,
        @PathParam("sobject") sobject: String,
        @PathParam("fieldName") fieldName: String,
        @PathParam("fieldValue") fieldValue: String,
        record: SalesforceRecord,
    ): SaveRecordResponse
}
