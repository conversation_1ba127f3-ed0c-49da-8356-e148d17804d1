package jp.co.torihada.fanme.modules.fanme.usecases.userPopup

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.InvalidParameterException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserPopup

@ApplicationScoped
class UpsertUserPopup {

    data class Input(
        val user: User,
        val enable: Boolean,
        val title: String?,
        val url: String,
        val buttonText: String?,
        val image: String?,
    )

    fun execute(input: Input): Result<UserPopup, Exception> {
        val existingPopup = UserPopup.findByUserId(input.user.id!!)

        if (existingPopup == null) {
            if (input.image == null) {
                return Err(InvalidParameterException("image is required"))
            }

            val newPopup =
                UserPopup.create(
                    user = input.user,
                    enable = input.enable,
                    title = input.title,
                    url = input.url,
                    buttonText = input.buttonText,
                    image = input.image,
                )

            return Ok(newPopup)
        } else {
            if (input.image == null && existingPopup.image == null) {
                return Err(InvalidParameterException("image is required"))
            }

            val updatedPopup =
                UserPopup.update(
                    existingPopup = existingPopup,
                    enable = input.enable,
                    title = input.title,
                    url = input.url,
                    buttonText = input.buttonText,
                    image = input.image,
                )

            return Ok(updatedPopup)
        }
    }
}
