package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.YearMonth
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction

@ApplicationScoped
class GetAgencySales
@Inject
constructor(
    private val monthlySellerSalesController: MonthlySellerSalesController,
    private val transactionController: TransactionController,
    private val securityUtils: SecurityUtils,
    private val userController: UserController,
) {

    data class Input(
        val agencyId: Long,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    data class MonthlySales(
        val yearMonth: String,
        val sellerSalesAmount: Int,
        val purchaserCount: Int,
        val purchaseCount: Int,
    )

    data class UserSales(
        val userUid: String,
        val userName: String,
        val thisMonth: MonthlySales?,
        val lastMonth: MonthlySales?,
    )

    data class TotalSales(val thisMonth: MonthlySales?, val lastMonth: MonthlySales?)

    data class AgencySales(val totalSales: TotalSales, val usersSales: List<UserSales>)

    fun execute(input: Input): Result<AgencySales, FanmeException> {
        securityUtils.validateAgentAccess(
            input.agencyId,
            input.currentUserUid,
            input.currentUserRole.value,
        )

        val consoleUsers = ConsoleUser.findByAgencyId(input.agencyId)

        val userUids = consoleUsers.mapNotNull { consoleUser -> consoleUser.user.uid }

        if (userUids.isEmpty()) {
            val emptyAgencySales =
                AgencySales(
                    totalSales = TotalSales(thisMonth = null, lastMonth = null),
                    usersSales = emptyList(),
                )
            return Ok(emptyAgencySales)
        }

        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        val monthlySellerSalesList =
            monthlySellerSalesController.getMonthlySellerSales(userUids, lastMonthStr, thisMonthStr)

        val thisMonthTransactions =
            transactionController.getSellerTransactions(
                sellerUserIds = userUids,
                fromYearMonth = thisMonthStr,
                toYearMonth = thisMonthStr,
            )
        val thisMonthPurchaseStats = calculateSellerPurchaseStats(thisMonthTransactions)

        val lastMonthTransactions =
            transactionController.getSellerTransactions(
                sellerUserIds = userUids,
                fromYearMonth = lastMonthStr,
                toYearMonth = lastMonthStr,
            )
        val lastMonthPurchaseStats = calculateSellerPurchaseStats(lastMonthTransactions)

        val users = userController.getUsers(userUids)
        val userIdToNameMap = users.associate { user -> user.uid to user.displayName }

        val monthlySellerSalesByUserId = monthlySellerSalesList.groupBy { it.sellerUserId }
        val thisMonthStatsByUser = thisMonthPurchaseStats.associateBy { it.sellerUserId }
        val lastMonthStatsByUser = lastMonthPurchaseStats.associateBy { it.sellerUserId }

        val userSalesList =
            userUids.map { userUid ->
                val monthlySellerSalesList = monthlySellerSalesByUserId[userUid] ?: emptyList()
                val thisMonthSales = monthlySellerSalesList.find { it.yearMonth == thisMonthStr }
                val lastMonthSales = monthlySellerSalesList.find { it.yearMonth == lastMonthStr }
                val thisMonthStats = thisMonthStatsByUser[userUid]
                val lastMonthStats = lastMonthStatsByUser[userUid]

                UserSales(
                    userUid = userUid,
                    userName = userIdToNameMap[userUid] ?: "FANMEユーザー",
                    thisMonth =
                        thisMonthSales?.let {
                            MonthlySales(
                                yearMonth = thisMonthStr,
                                sellerSalesAmount = it.sellerSalesAmount,
                                purchaserCount = thisMonthStats?.purchaserCount ?: 0,
                                purchaseCount = thisMonthStats?.purchaseCount ?: 0,
                            )
                        },
                    lastMonth =
                        lastMonthSales?.let {
                            MonthlySales(
                                yearMonth = lastMonthStr,
                                sellerSalesAmount = it.sellerSalesAmount,
                                purchaserCount = lastMonthStats?.purchaserCount ?: 0,
                                purchaseCount = lastMonthStats?.purchaseCount ?: 0,
                            )
                        },
                )
            }

        val totalSales = calculateTotalSales(userSalesList)

        val agencySales = AgencySales(totalSales = totalSales, usersSales = userSalesList)

        return Ok(agencySales)
    }

    private fun calculateTotalSales(userSalesList: List<UserSales>): TotalSales {
        val thisMonthTotal = calculateMonthTotal(userSalesList) { it.thisMonth }
        val lastMonthTotal = calculateMonthTotal(userSalesList) { it.lastMonth }
        return TotalSales(thisMonth = thisMonthTotal, lastMonth = lastMonthTotal)
    }

    private fun calculateMonthTotal(
        userSalesList: List<UserSales>,
        monthSelector: (UserSales) -> MonthlySales?,
    ): MonthlySales? {
        return if (userSalesList.any { monthSelector(it) != null }) {
            val yearMonth = userSalesList.firstNotNullOf { monthSelector(it) }.yearMonth
            val totalSalesAmount = userSalesList.sumOf { monthSelector(it)?.sellerSalesAmount ?: 0 }
            val totalPurchaseCount = userSalesList.sumOf { monthSelector(it)?.purchaseCount ?: 0 }
            val totalPurchaserCount = userSalesList.sumOf { monthSelector(it)?.purchaserCount ?: 0 }

            MonthlySales(
                yearMonth = yearMonth,
                sellerSalesAmount = totalSalesAmount,
                purchaserCount = totalPurchaserCount,
                purchaseCount = totalPurchaseCount,
            )
        } else null
    }

    private data class SellerPurchaseStats(
        val sellerUserId: String,
        val purchaserCount: Int,
        val purchaseCount: Int,
    )

    private fun calculateSellerPurchaseStats(
        transactions: List<Transaction>
    ): List<SellerPurchaseStats> {
        return transactions
            .mapNotNull { transaction ->
                transaction.sellerUserId?.let { sellerUserId -> sellerUserId to transaction }
            }
            .groupBy({ it.first }, { it.second })
            .map { (sellerUserId, sellerTransactions) ->
                SellerPurchaseStats(
                    sellerUserId = sellerUserId,
                    purchaserCount = sellerTransactions.map { it.purchaserUserId }.distinct().size,
                    purchaseCount = sellerTransactions.size,
                )
            }
    }
}
