package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetUser {
    data class Input(val accountIdentity: String)

    fun execute(input: Input): Result<User, Exception> {
        val user =
            User.findByAccountIdentity(input.accountIdentity)
                ?: return Err(ResourceNotFoundException("User"))
        return Ok(user)
    }
}
