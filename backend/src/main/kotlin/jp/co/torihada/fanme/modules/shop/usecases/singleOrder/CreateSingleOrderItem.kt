package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.OrderItem

@ApplicationScoped
class CreateSingleOrderItem {
    data class Input(val itemId: Long, val quantity: Int, val orderId: Long)

    fun execute(params: Input): Result<OrderItem, FanmeException> {
        val orderItem =
            OrderItem.create(
                orderId = params.orderId,
                itemId = params.itemId,
                quantity = params.quantity,
            )

        return Ok(orderItem)
    }
}
