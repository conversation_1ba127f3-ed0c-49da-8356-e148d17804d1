package jp.co.torihada.fanme.modules.shop.usecases.benefitLog

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.*

@ApplicationScoped
class CreateBenefitLog {

    data class BenefitWithStatus(val benefit: Benefit, val isCompleted: Boolean)

    data class Input(val itemIds: List<Long>, val purchaserUserId: String)

    @Transactional
    fun execute(params: Input): Result<Unit, FanmeException> {
        params.itemIds.forEach { itemId ->
            val benefits = Benefit.findByItemId(itemId)

            // 特典をすでに取得しているかの確認
            val benefitStatuses: List<BenefitWithStatus> =
                benefits.map { benefit ->
                    val isCompleted =
                        BenefitCompletionLog.findByUserUidAndBenefitId(
                                userUid = params.purchaserUserId,
                                benefitId = benefit.id!!,
                            )
                            ?.let { true } ?: false

                    BenefitWithStatus(benefit = benefit, isCompleted = isCompleted)
                }

            if (benefitStatuses.all { it.isCompleted }) {
                return@forEach
            }

            val uncompletedBenefits = benefitStatuses.filter { !it.isCompleted }.map { it.benefit }

            if (uncompletedBenefits.isNotEmpty()) {
                uncompletedBenefits.forEach { benefit ->
                    if (benefit.conditionType.value == BenefitCondition.PURCHASED.value) {
                        val purchasedItem =
                            PurchasedItem.findPaySuccessItemsByItemIdAndPurchaserUid(
                                itemId,
                                params.purchaserUserId,
                            )
                        if (purchasedItem.isNotEmpty()) {
                            BenefitCompletionLog.create(params.purchaserUserId, benefit.id!!)
                        }
                    } else if (
                        benefit.conditionType == BenefitCondition.PULLED_DIGITAL_GACHA_10_TIMES ||
                            benefit.conditionType ==
                                BenefitCondition.PULLED_DIGITAL_GACHA_20_TIMES ||
                            benefit.conditionType == BenefitCondition.PULLED_DIGITAL_GACHA_30_TIMES
                    ) {
                        val purchasedItems =
                            PurchasedItem.findPaySuccessItemsByItemIdAndPurchaserUid(
                                itemId = itemId,
                                purchaserUid = params.purchaserUserId,
                            )

                        val pulledCount = purchasedItems.sumOf { it.quantity }

                        createPullCountConditionTypeBenefitLog(
                            pulledCount,
                            benefit.id!!,
                            benefit.conditionType,
                            params.purchaserUserId,
                        )
                    } else if (benefit.conditionType == BenefitCondition.COMPLETE_DIGITAL_GACHA) {
                        val gachaItemFileIds =
                            GachaItemFile.findByItemId(itemId).map { it.itemFile.id }.toSet()

                        val receivedFileIds =
                            GachaReceivedFile.findByPurchaserUidAndItemId(
                                    params.purchaserUserId,
                                    itemId,
                                )
                                .mapNotNull { it?.itemFile?.id }
                                .toSet()

                        // 全てのファイルを取得しているか確認
                        val isAllFilesReceived = gachaItemFileIds.all { it in receivedFileIds }

                        if (isAllFilesReceived) {
                            BenefitCompletionLog.create(
                                userUid = params.purchaserUserId,
                                benefitId = benefit.id!!,
                            )
                        }
                    }
                }
            }
        }

        return Ok(Unit)
    }

    // 引いた回数特典
    private fun createPullCountConditionTypeBenefitLog(
        pulledCount: Int,
        id: Long,
        conditionType: BenefitCondition,
        purchaserUid: String,
    ) {
        val requiredCount =
            when (conditionType) {
                BenefitCondition.PULLED_DIGITAL_GACHA_10_TIMES -> 10
                BenefitCondition.PULLED_DIGITAL_GACHA_20_TIMES -> 20
                BenefitCondition.PULLED_DIGITAL_GACHA_30_TIMES -> 30
                else -> null
            }

        if (requiredCount != null && pulledCount >= requiredCount) {
            BenefitCompletionLog.create(userUid = purchaserUid, benefitId = id)
        }
    }
}
