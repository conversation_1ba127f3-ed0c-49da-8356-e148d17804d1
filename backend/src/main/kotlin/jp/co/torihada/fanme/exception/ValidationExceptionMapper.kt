package jp.co.torihada.fanme.exception

import jakarta.validation.ConstraintViolationException
import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider
import jp.co.torihada.fanme.dto.BaseResponseBody

@Provider
class ValidationExceptionMapper : ExceptionMapper<ConstraintViolationException> {
    override fun toResponse(exception: ConstraintViolationException): Response {
        val errors =
            exception.constraintViolations.map { violation ->
                ErrorObject(
                    code = 4000,
                    message = "${violation.propertyPath}: ${violation.message}",
                )
            }
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(BaseResponseBody(data = emptyMap<String, Any>(), errors = errors))
            .build()
    }
}
