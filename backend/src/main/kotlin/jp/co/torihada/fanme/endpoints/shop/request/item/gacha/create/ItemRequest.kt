package jp.co.torihada.fanme.endpoints.shop.request.item.gacha.create

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.ForSale
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.ItemOption
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.OnSale
import jp.co.torihada.fanme.modules.shop.controllers.requests.GachaItemRequest
import jp.co.torihada.fanme.modules.shop.models.ItemType

data class CreateGachaItemRequest(
    val name: String,
    val description: String?,
    val thumbnailUri: String,
    val thumbnailFrom: Int,
    val thumbnailBlurLevel: Int,
    val thumbnailWatermarkLevel: Int,
    val price: Int,
    val available: Boolean,
    val itemFiles: List<GachaFile>,
    val samples: List<GachaSampleFile>?,
    val benefits: List<GachaBenefit>?,
    val tags: List<String>?,
    val itemOption: ItemOption,
    val isDuplicated: Boolean,
    val awardProbabilities: List<AwardProbability>,
    val itemType: Int,
)

data class GachaFile(
    val id: Long?,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val maskedThumbnailUri: String?,
    val watermarkThumbnailUri: String?,
    val price: Int?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
    val awardType: Int,
    val isSecret: Boolean?,
)

data class GachaSampleFile(
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
)

data class GachaBenefit(
    val id: Long,
    val description: String?,
    val conditionType: Int,
    val files: List<GachaBenefitFile>,
)

data class GachaBenefitFile(
    val id: Long? = null,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
    val conditionType: Int,
)

data class AwardProbability(val awardType: Int, val probability: Int)

@ApplicationScoped
class CreateGachaItemRequestConverter() {
    fun requestToCreateItem(
        uid: String,
        request: CreateGachaItemRequest,
    ): GachaItemRequest.CreateItem {
        return GachaItemRequest.CreateItem(
            creatorUid = uid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertSampleFiles(it) },
            benefits = request.benefits?.let { convertBenefit(it) },
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
            itemType = ItemType.fromValue(request.itemType) ?: ItemType.DIGITAL_GACHA,
            isDuplicated = request.isDuplicated,
            awardProbabilities = convertAwardProbabilities(request.awardProbabilities),
        )
    }

    private fun convertAwardProbabilities(
        request: List<AwardProbability>
    ): List<GachaItemRequest.AwardProbability> {
        return request.map {
            GachaItemRequest.AwardProbability(
                awardType = it.awardType,
                probability = it.probability,
            )
        }
    }

    private fun convertFiles(request: List<GachaFile>): List<GachaItemRequest.File> {
        return request.map {
            GachaItemRequest.File(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                maskedThumbnailUri = it.maskedThumbnailUri,
                watermarkThumbnailUri = it.watermarkThumbnailUri,
                price = it.price,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
                itemThumbnailSelected = it.itemThumbnailSelected,
                sortOrder = it.sortOrder,
                awardType = it.awardType,
                isSecret = it.isSecret ?: false,
            )
        }
    }

    private fun convertSampleFiles(
        request: List<GachaSampleFile>
    ): List<GachaItemRequest.SampleFile> {
        return request.map {
            GachaItemRequest.SampleFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertBenefit(request: List<GachaBenefit>): List<GachaItemRequest.Benefit> {
        return request.map {
            GachaItemRequest.Benefit(
                id = null,
                description = it.description,
                conditionType = it.conditionType,
                files = convertBenefitFiles(it.files),
            )
        }
    }

    private fun convertBenefitFiles(
        request: List<GachaBenefitFile>
    ): List<GachaItemRequest.BenefitFile> {
        return request.map {
            GachaItemRequest.BenefitFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertForSale(request: ForSale?): GachaItemRequest.ForSale? {
        return request?.let {
            GachaItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): GachaItemRequest.OnSale? {
        return request?.let {
            GachaItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption): GachaItemRequest.ItemOption {
        return GachaItemRequest.ItemOption(
            qtyTotal = request.qtyTotal,
            forSale = convertForSale(request.forSale),
            password = request.password,
            onSale = convertOnSale(request.onSale),
        )
    }
}
