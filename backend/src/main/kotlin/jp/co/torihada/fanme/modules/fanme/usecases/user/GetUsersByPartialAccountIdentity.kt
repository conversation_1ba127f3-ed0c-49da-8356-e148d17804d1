package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetUsersByPartialAccountIdentity {

    data class Input(val partialAccountIdentity: String)

    fun execute(input: Input): Result<List<User>, FanmeException> {
        val users = User.findByPartialAccountIdentity(input.partialAccountIdentity)
        return Ok(users)
    }
}
