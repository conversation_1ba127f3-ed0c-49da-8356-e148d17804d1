package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus

@ApplicationScoped
class GetItemPublishLocked {
    fun execute(itemId: Long): Result<Boolean, FanmeException> {
        return try {
            val auditGroup = AuditGroup.findByItemId(itemId)
            val isPublishLocked =
                when (auditGroup?.status) {
                    AuditStatus.REJECTED -> true // 却下の場合は公開不可
                    else -> false // 却下以外は公開可能
                }

            Ok(isPublishLocked)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
