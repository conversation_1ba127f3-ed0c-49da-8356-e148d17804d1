package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "creator_popups")
class UserPopup : BaseModel() {
    @NotNull
    @OneToOne(optional = false)
    @JoinColumn(name = "creator_id", nullable = false)
    var user: User? = null

    @NotNull @Column(name = "enable", nullable = false) var enable: Boolean? = false

    @Size(max = 255) @Column(name = "title") var title: String? = null

    @Size(max = 255) @NotNull @Column(name = "url", nullable = false) var url: String? = null

    @Size(max = 255) @Column(name = "button_text") var buttonText: String? = null

    @Size(max = 255) @Column(name = "image") var image: String? = null

    companion object : PanacheCompanion<UserPopup> {
        fun findByUserId(userId: Long): UserPopup? {
            return find("user.id = ?1", userId).firstResult()
        }

        fun create(
            user: User,
            enable: Boolean,
            title: String?,
            url: String,
            buttonText: String?,
            image: String,
        ): UserPopup {
            val newPopup =
                UserPopup().apply {
                    this.user = user
                    this.enable = enable
                    this.url = url
                    this.image = image
                    this.title = title
                    this.buttonText = buttonText
                }
            newPopup.persist()

            return newPopup
        }

        fun update(
            existingPopup: UserPopup,
            enable: Boolean,
            title: String?,
            url: String,
            buttonText: String?,
            image: String?,
        ): UserPopup {
            existingPopup.enable = enable
            existingPopup.title = title
            existingPopup.url = url
            existingPopup.buttonText = buttonText
            if (image != null) {
                existingPopup.image = image
            }
            existingPopup.persist()

            return existingPopup
        }
    }
}
