package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.AuditGroupsResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AuditGroupController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/audit-groups")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AuditGroupEndpoint {

    @Inject private lateinit var handler: AuditGroupController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(AuditGroupsResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    @Produces(MediaType.APPLICATION_JSON)
    fun getAuditGroups(
        @QueryParam("\$top") top: Int? = null,
        @QueryParam("\$skip") skip: Int? = null,
        @QueryParam("\$count") count: Boolean? = null,
    ): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val result = handler.getAuditGroups(odata)
            val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
            val entity = ResponseEntity(result.auditGroupList, "audit_groups", metadata)
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
