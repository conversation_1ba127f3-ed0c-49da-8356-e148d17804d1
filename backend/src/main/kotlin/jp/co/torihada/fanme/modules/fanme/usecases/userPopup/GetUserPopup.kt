package jp.co.torihada.fanme.modules.fanme.usecases.userPopup

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.fanme.models.UserPopup

@ApplicationScoped
class GetUserPopup {

    data class Input(val userId: Long)

    fun execute(input: Input): Result<UserPopup?, Exception> {
        return Ok(UserPopup.findByUserId(input.userId))
    }
}
