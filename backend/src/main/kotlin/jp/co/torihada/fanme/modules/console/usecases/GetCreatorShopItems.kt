package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.*
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.usecases.item.GetItems
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop

@ApplicationScoped
class GetCreatorShopItems
@Inject
constructor(
    private val itemController: ItemController,
    private val shopController: ShopController,
    private val securityUtils: SecurityUtils,
    private val userController: UserController,
) {

    data class Input(
        val accountIdentity: String,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    fun execute(params: Input): Result<ConsoleCreatorShopItemsResponse, ConsoleException> {
        return try {
            val user =
                userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                    ?: throw ResourceNotFoundException("User")

            val userConsoleUser = user.consoleUser ?: throw ResourceNotFoundException("ConsoleUser")

            val targetAgencyId =
                userConsoleUser.agencyId ?: throw ResourceNotFoundException("Agency")

            securityUtils.validateAgentAccess(
                targetAgencyId,
                params.currentUserUid,
                params.currentUserRole.value,
            )

            val shop = shopController.getShop(user.uid!!)

            val items = itemController.getItems(user.uid!!, null, null, null, null)

            val dto = ConsoleCreatorShopItemsResponse(user = user, shop = shop, items = items)
            Ok(dto)
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }

    data class ConsoleCreatorShopItemsResponse(
        val user: User,
        val shop: GetShop.ShopForGetShop,
        val items: List<GetItems.ItemForGetItems>,
    )
}
