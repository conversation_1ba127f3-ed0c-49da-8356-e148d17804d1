package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail

@ApplicationScoped
class UpdateContentBlockDetail {

    class Input(
        val contentBlockId: Long,
        val title: String?,
        val description: String?,
        val appDescription: String?,
        val url: String?,
        val iconUrl: String?,
    )

    fun execute(input: Input): Result<ContentBlockDetail, Exception> {
        val contentBlockDetail =
            ContentBlockDetail.findById(input.contentBlockId)
                ?: return Err(ResourceNotFoundException("ContentBlockDetail"))

        val result =
            ContentBlockDetail.update(
                contentBlockDetail,
                title = input.title,
                description = input.description,
                appDescription = input.appDescription,
                url = input.url,
                icon = input.iconUrl,
            )

        return Ok(result)
    }
}
