package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController as FanmeUserController
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.odata.OData
import kotlin.math.roundToInt
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking

@ApplicationScoped
class GetUserMonthlySales {

    @Inject private lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @Inject private lateinit var transactionController: TransactionController

    @Inject private lateinit var userController: FanmeUserController

    @Inject private lateinit var securityUtils: SecurityUtils

    companion object {
        private val YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM")
    }

    data class Response(val monthlySalesList: List<MonthlySales>, val totalCount: Int? = null)

    data class MonthlySales(
        val yearMonth: String,
        val salesAmount: Int,
        val purchaserCount: Int,
        val purchaseCount: Int,
        val growthRate: Float? = null,
    )

    fun execute(
        userId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        from: YearMonth? = null,
        to: YearMonth? = null,
        odata: OData? = null,
    ): Result<Response, Exception> {

        val effectiveFrom = from ?: YearMonth.now().minusMonths(2)
        val effectiveTo = to ?: YearMonth.now()

        if (effectiveFrom.isAfter(effectiveTo)) {
            return Err(
                InvalidYearMonthFormatException("'from' date must be before or equal to 'to' date")
            )
        }

        val targetUser = userController.getUserById(userId)
        val targetUserUid = targetUser.uid ?: throw ConsoleResourceNotFoundException("User UID")

        securityUtils.validateAgentForUserAccess(targetUser, currentUserRole.value, currentUserUid)

        val (salesAmountByMonth, transactionsByMonth) =
            fetchSalesAndTransactions(
                targetUserUid,
                effectiveFrom.format(YEAR_MONTH_FORMATTER),
                effectiveTo.format(YEAR_MONTH_FORMATTER),
            )
        val userMonthlySalesList =
            calculateMonthlySalesWithGrowthRate(
                salesAmountByMonth,
                transactionsByMonth,
                effectiveFrom,
                effectiveTo,
            )

        val totalCount = if (odata?.count == true) userMonthlySalesList.size else null

        val paginatedMonthlySales = applyPagination(userMonthlySalesList, odata)

        return Ok(Response(monthlySalesList = paginatedMonthlySales, totalCount = totalCount))
    }

    private fun fetchSalesAndTransactions(
        targetUserUid: String,
        fromYearMonth: String,
        toYearMonth: String,
    ): Pair<Map<String, Int>, Map<String, List<Transaction>>> = runBlocking {
        coroutineScope {
            val salesDeferred = async {
                monthlySellerSalesController.getMonthlySellerSales(
                    sellerUserIds = listOf(targetUserUid),
                    fromYearMonth = fromYearMonth,
                    toYearMonth = toYearMonth,
                )
            }

            val transactionsDeferred = async {
                transactionController.getSellerTransactions(
                    sellerUserIds = listOf(targetUserUid),
                    fromYearMonth = fromYearMonth,
                    toYearMonth = toYearMonth,
                )
            }

            val salesList = salesDeferred.await()
            val transactions = transactionsDeferred.await()

            val salesAmountByMonth =
                salesList.associateBy { it.yearMonth }.mapValues { it.value.sellerSalesAmount }

            val transactionsByMonth =
                transactions
                    .filter { it.createdAt != null }
                    .groupBy { transaction ->
                        transaction.createdAt!!.atZone(ZoneOffset.UTC).format(YEAR_MONTH_FORMATTER)
                    }

            salesAmountByMonth to transactionsByMonth
        }
    }

    private fun calculateMonthlySalesWithGrowthRate(
        salesAmountByMonth: Map<String, Int>,
        transactionsByMonth: Map<String, List<Transaction>>,
        fromYearMonth: YearMonth,
        toYearMonth: YearMonth,
    ): List<MonthlySales> {
        val allMonthsInRange = mutableListOf<String>()
        var currentMonth = fromYearMonth
        while (!currentMonth.isAfter(toYearMonth)) {
            allMonthsInRange.add(currentMonth.format(YEAR_MONTH_FORMATTER))
            currentMonth = currentMonth.plusMonths(1)
        }

        val monthlySales = mutableListOf<MonthlySales>()
        val salesByMonth = mutableMapOf<String, Int>()

        for (yearMonth in allMonthsInRange) {
            val transactions = transactionsByMonth[yearMonth] ?: emptyList()
            val purchaserCount = transactions.map { it.purchaserUserId }.distinct().size
            val purchaseCount = transactions.size
            val salesAmount = salesAmountByMonth[yearMonth] ?: 0
            salesByMonth[yearMonth] = salesAmount

            val previousYearMonth =
                YearMonth.parse(yearMonth, YEAR_MONTH_FORMATTER)
                    .minusMonths(1)
                    .format(YEAR_MONTH_FORMATTER)
            val previousSalesAmount = salesByMonth[previousYearMonth]

            val growthRate =
                previousSalesAmount
                    ?.takeIf { it != 0 }
                    ?.let {
                        val rate = ((salesAmount - it).toFloat() / it * 100)
                        (rate * 10).roundToInt() / 10f
                    }

            monthlySales +=
                MonthlySales(
                    yearMonth = yearMonth,
                    salesAmount = salesAmount,
                    purchaserCount = purchaserCount,
                    purchaseCount = purchaseCount,
                    growthRate = growthRate,
                )
        }

        return monthlySales.sortedByDescending { it.yearMonth }
    }

    private fun applyPagination(list: List<MonthlySales>, odata: OData?): List<MonthlySales> {
        if (odata == null) return list

        var result = list
        odata.skip?.let { result = result.drop(it) }
        odata.top?.let { result = result.take(it) }
        return result
    }
}
