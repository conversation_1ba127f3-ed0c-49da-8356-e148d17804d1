package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.SnsLink
import jp.co.torihada.fanme.modules.fanme.models.SnsLinkDisplay

@ApplicationScoped
class UpdateSnsLinks {

    data class Input(val userId: Long, val snsLinks: List<SnsLinkInput>) {
        data class SnsLinkInput(
            val type: String,
            val accountIdentity: String,
            val displayable: Boolean,
        )
    }

    fun execute(input: Input): Result<CreateSnsLinks.Output, Exception> {
        val profile =
            Profile.findByUserId(input.userId) ?: return Err(ResourceNotFoundException("Profile"))

        val snsLinks = mutableListOf<SnsLink>()
        for (snsLinkInput in input.snsLinks) {
            val snsLinkType =
                Const.SnsLinkType.fromValue(snsLinkInput.type)
                    ?: return Err(
                        IllegalArgumentException("Invalid SNS link type: ${snsLinkInput.type}")
                    )
            val snsLink =
                profile.snsLinks.find { it.type == snsLinkType.dbStr }
                    ?: return Err(ResourceNotFoundException("SnsLink"))
            val snsLinkDisplay =
                snsLink.snsLinkDisplay ?: return Err(ResourceNotFoundException("SnsLinkDisplay"))

            SnsLink.update(snsLink = snsLink, accountIdentity = snsLinkInput.accountIdentity)
            SnsLinkDisplay.updateDisplayable(
                snsLinkDisplay = snsLinkDisplay,
                displayable = snsLinkInput.displayable,
            )
            snsLink.snsLinkDisplay = snsLinkDisplay
            snsLinks.add(snsLink)
        }

        return Ok(CreateSnsLinks.Output(snsLinks))
    }
}
