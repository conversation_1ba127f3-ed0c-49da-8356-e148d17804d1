package jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce

import com.fasterxml.jackson.annotation.JsonProperty

data class Auth(
    @JsonProperty("id") val id: String,
    @JsonProperty("access_token") val accessToken: String,
    @JsonProperty("token_type") val tokenType: String,
    @JsonProperty("issued_at") val issuedAt: Long,
    @JsonProperty("instance_url") val instanceUrl: String,
    @JsonProperty("signature") val signature: String,
)
