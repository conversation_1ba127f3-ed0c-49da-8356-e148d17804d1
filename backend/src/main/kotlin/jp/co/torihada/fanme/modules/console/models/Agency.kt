package jp.co.torihada.fanme.modules.console.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import java.time.Instant
import jp.co.torihada.fanme.Util
import jp.co.torihada.fanme.modules.shop.Const

@Entity
@Table(name = "agencies")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class Agency : BaseModel() {

    @Column(name = "name", nullable = false, length = 255) var name: String = ""

    @Column(name = "deleted_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var deletedAt: Instant? = null

    companion object : PanacheCompanion<Agency> {
        fun findAll(top: Int?, skip: Int?): List<Agency> {
            val orderByQuery = " ORDER BY id ASC"
            val query = "deletedAt IS NULL"
            val find = find(query + orderByQuery)
            return Util.getEntityListWithPagination(find, top, skip).filterIsInstance<Agency>()
        }
    }
}
