package jp.co.torihada.fanme.modules.fanme.services.audit

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDateTime
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType

data class AuditGroupServiceAuditObject(
    val bucket: String,
    val filePath: String,
    val assetType: AssetType,
)

@ApplicationScoped
class AuditGroupService {
    /**
     * 監査グループと監査オブジェクトを作成
     *
     * @param userUid ユーザーID
     * @param auditType 監査種別
     * @param metadata メタデータ
     * @param auditObjects 監査オブジェクトのリスト
     * @return 作成された監査グループのID
     */
    fun createAuditGroup(
        userUid: String,
        auditType: AuditType,
        operationType: OperationType,
        metadata: AuditGroupMetadata,
        auditObjects: List<AuditGroupServiceAuditObject>,
    ): Long {
        // 監査グループの作成
        val auditGroup =
            AuditGroup().apply {
                this.userUid = userUid
                this.auditType = auditType
                this.operationType = operationType
                this.metadata = jacksonObjectMapper().writeValueAsString(metadata)
                this.itemId = metadata.itemId?.toLongOrNull()
                this.status = AuditStatus.UNAUDITED
            }
        auditGroup.persist()

        // 監査オブジェクトの作成
        auditObjects.forEach { entity ->
            AuditObject()
                .apply {
                    this.auditGroupId = auditGroup.id!!
                    this.bucket = entity.bucket
                    this.filePath = entity.filePath
                    this.assetType = entity.assetType
                }
                .persist()
        }

        return auditGroup.id!!
    }

    /**
     * 監査グループのステータスを更新
     *
     * @param auditGroupId 監査グループID
     * @param status 新しいステータス
     * @param comment 監査コメント
     * @param auditedUserUid 監査実施者のユーザーID
     */
    fun updateAuditStatus(
        auditGroupId: Long,
        status: AuditStatus,
        comment: String? = null,
        auditedUserUid: String? = null,
    ) {
        val auditGroup =
            AuditGroup.findById(auditGroupId)
                ?: throw IllegalArgumentException("AuditGroup not found: $auditGroupId")
        auditGroup
            .apply {
                this.status = status
                this.comment = comment
                if (status != AuditStatus.UNAUDITED) {
                    this.auditedAt = LocalDateTime.now()
                    this.auditedUserUid = auditedUserUid
                }
            }
            .persist()
    }
}
