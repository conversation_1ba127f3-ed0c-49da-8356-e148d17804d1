package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetCurrentUser {

    data class Input(val userUid: String)

    fun execute(input: Input): Result<User, Exception> {
        val user = User.findByUuid(input.userUid) ?: return Err(ResourceNotFoundException("User"))
        return Ok(user)
    }
}
