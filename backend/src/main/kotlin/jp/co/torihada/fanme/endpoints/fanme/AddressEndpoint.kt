package jp.co.torihada.fanme.endpoints.fanme

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.SuggestAddressResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.modules.fanme.controllers.AddressController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/address/suggestion")
@Tag(name = "FANME", description = "FANME APIサーバー")
class AddressEndpoint {

    @Inject private lateinit var addressHandler: AddressController

    @GET
    @RolesAllowed("LoginUser")
    @Path("/postal-code/{postal-code}")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(SuggestAddressResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun suggestAddress(@PathParam("postal-code") postalCode: String): Response {
        return try {
            val suggestedAddress =
                addressHandler.suggestAddress(postalCode)
                    ?: return Response.status(Response.Status.NOT_FOUND).build()

            val entity = ResponseEntity(suggestedAddress, "suggestedAddress")
            Response.ok(entity).build()
        } catch (_: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }
}
