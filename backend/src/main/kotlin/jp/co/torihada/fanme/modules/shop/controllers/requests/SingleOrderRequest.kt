package jp.co.torihada.fanme.modules.shop.controllers.requests

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.ITEM_ID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PAYMENT_METHOD_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.TIP_MUST_BE_0_OR_MORE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.TIP_MUST_BE_LESS_THAN_100
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.TIP_MUST_BE_LESS_THAN_300000
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Util

class SingleOrderRequest {
    data class CreateOrder(
        @NotBlank(message = ITEM_ID_IS_REQUIRED) val itemId: Long,
        @NotBlank(message = USER_ID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = USER_ID_TOO_MANY_LENGTH)
        val userId: String,
        @Min(0, message = TIP_MUST_BE_0_OR_MORE)
        @Max(100, message = TIP_MUST_BE_LESS_THAN_100)
        val quantity: Int,
        @Min(0, message = TIP_MUST_BE_0_OR_MORE)
        @Max(300000, message = TIP_MUST_BE_LESS_THAN_300000)
        val tip: Int,
        @NotBlank(message = PAYMENT_METHOD_IS_REQUIRED) val paymentMethod: Util.PaymentMethod,
        val cardParam: OrderRequest.CardParam?,
        val convenienceParam: OrderRequest.ConvenienceParam?,
        val googlePayParam: OrderRequest.GooglePayParam?,
        val applePayParam: OrderRequest.ApplePayParam?,
    )
}
