package jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FanmeCustomerEntity(
    val creatorUid: String? = null,
    val firstName: String,
    val lastName: String,
    val firstNameKana: String,
    val lastNameKana: String,
    val postalCode: String,
    val prefecture: String,
    val city: String,
    val street: String,
    val building: String?,
    val phoneNumber: String,
)
