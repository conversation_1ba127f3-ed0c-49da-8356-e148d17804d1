package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.lib.MaskSerializer
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock as ContentBlockModel
import jp.co.torihada.fanme.modules.fanme.models.User
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class GetContentBlocks {

    class Input(val user: User)

    @Schema(name = "GetContentBlock_ContentBlock")
    data class ContentBlock(
        val id: Long,
        val contentBlockDetails: List<ContentBlockDetail>,
        val contentBlockType: Long,
        val displayOrderNumber: Int,
        val displayable: Boolean,
    ) {
        @Schema(name = "GetContentBlock_ContentBlockDetail")
        data class ContentBlockDetail(
            val id: Long,
            val contentGroupNumber: Int,
            val isSetIcon: Boolean,
            @JsonSerialize(using = MaskSerializer::class) val title: String,
            val url: String?,
            @JsonSerialize(using = MaskSerializer::class) val description: String?,
            @JsonSerialize(using = MaskSerializer::class) val appDescription: String?,
            val icon: String?,
            val style: MutableMap<String, Any>?,
        )
    }

    fun execute(input: Input): Result<List<ContentBlock>, Exception> {
        val contentBlocks = ContentBlockModel.findByUser(input.user)

        val result =
            contentBlocks.map { contentBlock ->
                ContentBlock(
                    id = contentBlock.id!!,
                    contentBlockDetails =
                        contentBlock.contentBlockGroups
                            .sortedBy { it.contentGroupNumber }
                            .map { group ->
                                val detail = group.contentBlockDetail
                                ContentBlock.ContentBlockDetail(
                                    id = detail?.id!!,
                                    appDescription = detail.appDescription,
                                    contentGroupNumber = group.contentGroupNumber!!,
                                    description = detail.description,
                                    icon = detail.iconUrl,
                                    isSetIcon = !detail.icon.isNullOrEmpty(),
                                    style = detail.style,
                                    title = detail.title ?: "",
                                    url = detail.url,
                                )
                            },
                    contentBlockType = contentBlock.contentBlockTypeId!!,
                    displayOrderNumber = contentBlock.displayOrderNumber ?: 0,
                    displayable = contentBlock.displayable ?: false,
                )
            }
        return Ok(result)
    }
}
