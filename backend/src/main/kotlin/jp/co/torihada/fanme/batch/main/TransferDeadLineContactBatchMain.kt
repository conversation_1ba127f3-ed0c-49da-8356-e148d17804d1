package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.batch.usecases.TransferDeadLineContactBatch
import org.jboss.logging.Logger

class TransferDeadLineContactBatchMain : QuarkusApplication {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var transferDeadLineContactBatch: TransferDeadLineContactBatch

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("TransferDeadLineContactBatchMain start")
        try {
            transferDeadLineContactBatch.execute()
            logger.info("TransferDeadLineContactBatchMain success")
            return 0
        } catch (e: Exception) {
            logger.error("TransferDeadLineContactBatchMain error", e)
            return 1
        }
    }
}
