package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserTutorial
import jp.co.torihada.fanme.modules.fanme.usecases.userTutorial.UpsertUserTutorial

@ApplicationScoped
class UserTutorialController : BaseController() {
    @Inject private lateinit var upsertUserTutorial: UpsertUserTutorial

    data class UpsertUserTutorialRequest(
        val userUuid: String,
        val flagKey: String,
        val displayFlg: Boolean,
    )

    fun getUserTutorial(userUuid: String, flagKey: String): UserTutorial? {
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")
        return UserTutorial.findByUserAndName(user, flagKey)
    }

    @Transactional
    fun upsertUserTutorial(@Valid request: UpsertUserTutorialRequest): UserTutorial {
        val userTutorial =
            upsertUserTutorial
                .execute(
                    UpsertUserTutorial.Input(request.userUuid, request.flagKey, request.displayFlg)
                )
                .getOrThrow()

        return userTutorial
    }
}
