package jp.co.torihada.fanme.endpoints.fanme

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.GET
import jakarta.ws.rs.PUT
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.UserTutorialResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.UserTutorialController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/users")
@Tag(name = "FANME", description = "FANME APIサーバー")
class UserTutorialEndpoint {
    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var handler: UserTutorialController

    @Inject lateinit var util: Util

    @GET
    @Path("/current/user_tutorials/{flag_key}")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(UserTutorialResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getUserTutorial(@PathParam("flag_key") flagKey: String): Response {
        try {
            val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val userTutorial = handler.getUserTutorial(userUuid, flagKey)
            return Response.ok(userTutorial).build()
        } catch (e: Exception) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }

    data class UpsertUserTutorialRequest(val displayFlg: Boolean)

    @PUT
    @Path("/current/user_tutorials/{flag_key}")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(UserTutorialResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun upsertUserTutorial(
        @PathParam("flag_key") flagKey: String,
        requestBody: UpsertUserTutorialRequest,
    ): Response {
        try {
            val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val userTutorial =
                handler.upsertUserTutorial(
                    UserTutorialController.UpsertUserTutorialRequest(
                        userUuid = userUuid,
                        flagKey = flagKey,
                        displayFlg = requestBody.displayFlg,
                    )
                )
            return Response.ok(userTutorial).build()
        } catch (e: Exception) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }
}
