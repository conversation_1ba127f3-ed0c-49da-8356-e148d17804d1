package jp.co.torihada.fanme.modules.payment.usecases.monthlysales

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale

@ApplicationScoped
class GetMonthlySellerSales {

    data class Input(
        val sellerUserIds: List<String>,
        val fromYearMonth: String? = null,
        val toYearMonth: String? = null,
    )

    data class MonthlySellerSales(
        val sellerUserId: String,
        val yearMonth: String,
        val sellerSalesAmount: Int,
    )

    fun execute(input: Input): Result<List<MonthlySellerSales>, FanmeException> {

        val allMonthlySellerSalesList =
            MonthlySellerSale.findBySellerUserIds(
                input.sellerUserIds,
                input.fromYearMonth,
                input.toYearMonth,
            )

        val monthlySalesList =
            allMonthlySellerSalesList.map { monthlySellerSales ->
                MonthlySellerSales(
                    sellerUserId = monthlySellerSales.sellerUserId,
                    yearMonth = monthlySellerSales.yearMonth ?: "",
                    sellerSalesAmount = monthlySellerSales.sellerSalesAmount,
                )
            }

        return Ok(monthlySalesList)
    }
}
