package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.services.address.IAddressService
import jp.co.torihada.fanme.modules.fanme.services.address.SuggestedAddressEntity

@ApplicationScoped
class AddressController : BaseController() {

    @Inject private lateinit var addressService: IAddressService

    fun suggestAddress(postalCode: String): SuggestedAddressEntity? {
        return addressService.suggestAddress(postalCode)
    }
}
