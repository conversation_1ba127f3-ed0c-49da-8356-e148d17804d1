package jp.co.torihada.fanme.modules.console.services

import io.quarkus.redis.datasource.RedisDataSource
import io.quarkus.redis.datasource.hash.HashCommands
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.security.SecureRandom
import java.time.Duration
import java.util.Base64

@ApplicationScoped
class ProxyAccessTokenService {
    @Inject lateinit var redisDataSource: RedisDataSource

    companion object {
        private const val REDIS_KEY_PREFIX = "proxy:"
        private const val TOKEN_EXPIRATION_SECONDS = 120L
        private const val TOKEN_BYTE_SIZE = 32
    }

    data class ProxyAccessTokenData(
        val proxyUserAccountIdentity: String,
        val targetUserAccountIdentity: String,
        val targetUserFanmeToken: String,
    )

    fun createProxyAccessToken(data: ProxyAccessTokenData): String {
        val proxyAccessToken = generateRandomToken()
        val redisKey = "$REDIS_KEY_PREFIX$proxyAccessToken"

        val hashCommands: HashCommands<String, String, String> =
            redisDataSource.hash(String::class.java)

        val redisData =
            mapOf(
                "proxy_user_account_identity" to data.proxyUserAccountIdentity,
                "target_user_account_identity" to data.targetUserAccountIdentity,
                "target_user_fanme_token" to data.targetUserFanmeToken,
            )

        hashCommands.hset(redisKey, redisData)
        redisDataSource.key().expire(redisKey, Duration.ofSeconds(TOKEN_EXPIRATION_SECONDS))

        return proxyAccessToken
    }

    private fun generateRandomToken(): String {
        val bytes = ByteArray(TOKEN_BYTE_SIZE)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
}
