package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "campaign_entries")
class CampaignEntry : BaseModel() {
    @ManyToOne @JoinColumn(name = "campaign_id", nullable = false) var campaign: Campaign? = null

    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @Column(name = "entered_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var enteredAt: Instant = Instant.now()

    companion object : PanacheCompanion<CampaignEntry> {
        fun create(campaign: Campaign, user: User): CampaignEntry {
            val entry = CampaignEntry()
            entry.campaign = campaign
            entry.user = user
            entry.enteredAt = Instant.now()
            entry.persist()
            return entry
        }

        fun find(user: User, actionType: Campaign.ActionType): List<CampaignEntry> {
            val now = Instant.now()
            return find("user = ?1 and campaign.actionType = ?2", user, actionType).list()
        }

        fun findByUser(user: User): List<CampaignEntry> {
            return list("user = ?1", user)
        }
    }
}
