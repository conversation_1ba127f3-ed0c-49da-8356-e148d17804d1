package jp.co.torihada.fanme.modules.shop.usecases.gacha

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.aws.S3

@ApplicationScoped
class GetPurchasedItemReceivedFiles {
    @Inject private lateinit var s3: S3

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class PurchasedItemReceivedFile(
        val id: Long,
        val title: String,
        val thumbnail: String?,
        val awardType: Int,
        val count: Int,
    )

    data class Input(val userId: String, val purchasedItemId: Long)

    data class Output(val files: List<PurchasedItemReceivedFile>)

    fun execute(input: Input): Result<Output, FanmeException> {
        val purchasedItem =
            PurchasedItem.find("id = ?1 and purchaserUid = ?2", input.purchasedItemId, input.userId)
                .firstResult() ?: return Err(ResourceNotFoundException("PurchasedItem not found"))

        val item = purchasedItem.item

        val files =
            purchasedItem.gachaReceivedFile
                .groupBy { it.itemFile.id }
                .map { (_, files) ->
                    val itemFile = files.first().itemFile
                    val gachaItemFile = itemFile.gachaItemFile

                    val thumbnailUrl =
                        if (item.itemType.isPrintGacha()) {
                            itemFile.watermarkThumbnailUri?.let { uri -> s3.getObjectUri(uri) }
                        } else {
                            itemFile.thumbnailUri?.let { uri -> s3.getObjectUri(uri) }
                        }

                    PurchasedItemReceivedFile(
                        id = itemFile.id!!,
                        title = itemFile.name,
                        thumbnail = thumbnailUrl,
                        awardType = gachaItemFile?.awardType?.value ?: 0,
                        count = files.size,
                    )
                }

        return Ok(Output(files))
    }
}
