package jp.co.torihada.fanme.modules.payment.usecases.transaction

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.Transaction

@ApplicationScoped
class GetSellerTransactions {

    data class Input(
        val sellerUserIds: List<String>,
        val fromYearMonth: String? = null,
        val toYearMonth: String? = null,
        val top: Int? = null,
        val skip: Int? = null,
    )

    fun execute(params: Input): Result<List<Transaction>, FanmeException> {
        val transactions =
            Transaction.findBySellerUserIdsWithOptions(
                Const.DEFAULT_TENANT,
                params.sellerUserIds,
                params.fromYearMonth,
                params.toYearMonth,
                params.top,
                params.skip,
            )
        return Ok(transactions)
    }
}
