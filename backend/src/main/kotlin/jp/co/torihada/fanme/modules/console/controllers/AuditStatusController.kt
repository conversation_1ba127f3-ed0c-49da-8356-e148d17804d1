package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.usecases.UpdateAuditStatus
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus

@ApplicationScoped
class AuditStatusController {

    @Inject private lateinit var updateAuditStatus: UpdateAuditStatus

    fun updateAuditStatus(
        auditGroupId: Long,
        status: AuditStatus,
        comment: String?,
        auditedUserUid: String?,
    ) {
        updateAuditStatus
            .execute(
                UpdateAuditStatus.Input(
                    auditGroupId = auditGroupId,
                    status = status,
                    comment = comment,
                    auditedUserUid = auditedUserUid,
                )
            )
            .getOrElse { throw it }
    }
}
