package jp.co.torihada.fanme.modules.payment.usecases.payments.card

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.externals.BaseExternals
import jp.co.torihada.fanme.modules.payment.externals.client.shop.ExtShopClient
import jp.co.torihada.fanme.modules.payment.externals.entity.shop.FinalizeCreditCard3DSecure
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseExecTransaction
import org.eclipse.microprofile.rest.client.inject.RestClient

@ApplicationScoped
class PostCreditCard3DSecureSingleOrderProcessor : BaseExecTransaction() {
    @Inject private lateinit var baseExternals: BaseExternals
    @Inject @RestClient private lateinit var extShopClient: ExtShopClient

    data class Input(val checkoutId: Long, val transactionId: Long)

    class Output

    @Transactional
    fun execute(params: Input): Result<Output, FanmeException> {
        val checkout =
            Checkout.findById(params.checkoutId)
                ?: return Err(ResourceNotFoundException("Checkout"))
        val transaction =
            Transaction.findById(params.transactionId)
                ?: return Err(ResourceNotFoundException("Transaction"))

        extShopClient.finalizeCreditCard3DSecureSingleOrder(
            authorization = baseExternals.getShopAuthorization(),
            FinalizeCreditCard3DSecure.Request(
                transactionId = transaction.id!!,
                checkoutId = checkout.id!!,
            ),
        )

        checkout.status = Const.CheckoutStatus.PAYSUCCESS.value
        transaction.status = PaymentConst.TransactionStatus.Success.value

        // クリエイターとテナントの売上を登録
        saveSellerAndTenantRevenue(transaction, checkout)

        return Ok(Output())
    }
}
