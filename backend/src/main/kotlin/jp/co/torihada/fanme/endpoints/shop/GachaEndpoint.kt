package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.BadgeRankingResponseBody
import jp.co.torihada.fanme.dto.CompleteBadgeResponseBody
import jp.co.torihada.fanme.dto.GachaPullResponseBody
import jp.co.torihada.fanme.dto.GachaPullableCountResponseBody
import jp.co.torihada.fanme.dto.PurchasedItemReceivedFilesResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.lib.MaskConfig
import jp.co.torihada.fanme.modules.shop.controllers.GachaController
import jp.co.torihada.fanme.modules.shop.controllers.requests.GachaItemRequest
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/gacha")
class GachaEndpoint {
    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var handler: GachaController
    @Inject private lateinit var util: Util
    @Inject private lateinit var maskConfig: MaskConfig

    @POST
    @Path("/pull")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(GachaPullResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun pull(requestBody: GachaItemRequest.PullGachaRequest): Response {
        return try {
            val purchaserUid =
                util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.pullGacha(requestBody.itemId, purchaserUid)
            val entity = ResponseEntity(result, "files")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/pullable-count")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(GachaPullableCountResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getPullableGachaCount(@PathParam("item_id") itemId: Long): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getPullableGachaCount(userUid, itemId)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/complete-badge")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(CompleteBadgeResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCompleteBadge(@PathParam("item_id") itemId: Long): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getGachaCompleteBadge(userUid, itemId)
            val entity = ResponseEntity(result, "badge")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/complete-badge-ranking")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(BadgeRankingResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCompleteBadgeRanking(@PathParam("item_id") itemId: Long): Response {
        return try {
            val result = handler.getGachaCompleteBadgeRanking(itemId)
            val entity = ResponseEntity(result, "ranking")

            maskConfig.enabled = true
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/purchased-items/{purchasedItemId}/received-files")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(PurchasedItemReceivedFilesResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getPurchasedItemReceivedFiles(
        @PathParam("purchasedItemId") purchasedItemId: Long
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val files = handler.getPurchasedItemReceivedFiles(userUid, purchasedItemId)
            val entity = ResponseEntity(files, "files")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
