package jp.co.torihada.fanme.endpoints.shop.request.item.gacha.update

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.ForSale
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.OnSale
import jp.co.torihada.fanme.modules.shop.controllers.requests.GachaItemRequest

data class UpdateGachaItemRequest(
    val name: String,
    val description: String?,
    val thumbnailUri: String,
    val thumbnailFrom: Int,
    val thumbnailBlurLevel: Int,
    val thumbnailWatermarkLevel: Int,
    val price: Int,
    val available: Boolean,
    val itemFiles: List<GachaFileForUpdate>,
    val samples: List<GachaSampleFileForUpdate>?,
    val benefits: List<GachaBenefitForUpdate>?,
    val tags: List<String>?,
    val itemOption: ItemOption?,
)

data class GachaFileForUpdate(val id: Long, val name: String, val isSecret: Boolean)

// 特典の種類は更新できない
data class GachaBenefitForUpdate(
    val id: Long?,
    val description: String?,
    val files: List<GachaBenefitFileForUpdate>,
)

data class GachaBenefitFileForUpdate(
    val id: Long,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
)

data class GachaSampleFileForUpdate(
    val id: Long?,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
)

data class ItemOption(val password: String?, val onSale: OnSale?, val forSale: ForSale?)

@ApplicationScoped
class UpdateGachaItemRequestConverter() {
    fun requestToUpdateItem(
        creatorUid: String,
        itemId: Long,
        request: UpdateGachaItemRequest,
    ): GachaItemRequest.UpdateItem {
        return GachaItemRequest.UpdateItem(
            itemId = itemId,
            creatorUid = creatorUid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            itemOption = convertItemOption(request.itemOption),
            price = request.price,
            available = request.available,
            samples = request.samples?.let { convertSampleFiles(it) },
            benefits = request.benefits?.let { convertBenefitFiles(it) },
            tags = request.tags,
            files = convertUpdateFiles(request.itemFiles),
        )
    }

    private fun convertForSale(request: ForSale?): GachaItemRequest.ForSale? {
        return request?.let {
            GachaItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): GachaItemRequest.OnSale? {
        return request?.let {
            GachaItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption?): GachaItemRequest.ItemOption? {
        return request?.let {
            GachaItemRequest.ItemOption(
                qtyTotal = null,
                forSale = convertForSale(request.forSale),
                password = request.password,
                onSale = convertOnSale(request.onSale),
            )
        }
    }

    private fun convertSampleFiles(
        request: List<GachaSampleFileForUpdate>
    ): List<GachaItemRequest.SampleFile> {
        return request.map {
            GachaItemRequest.SampleFile(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertUpdateFiles(
        request: List<GachaFileForUpdate>
    ): List<GachaItemRequest.UpdateFile> {
        return request.map {
            GachaItemRequest.UpdateFile(id = it.id, name = it.name, isSecret = it.isSecret)
        }
    }

    private fun convertBenefitFiles(
        request: List<GachaBenefitForUpdate>
    ): List<GachaItemRequest.Benefit> {
        return request.map {
            GachaItemRequest.Benefit(
                id = it.id,
                description = it.description,
                conditionType = null,
                files =
                    it.files.map { file ->
                        GachaItemRequest.BenefitFile(
                            id = file.id,
                            name = file.name,
                            objectUri = file.objectUri.substringBefore("?"),
                            thumbnailUri = file.thumbnailUri,
                            fileType = file.fileType,
                            size = file.size,
                            duration = file.duration,
                        )
                    },
            )
        }
    }
}
