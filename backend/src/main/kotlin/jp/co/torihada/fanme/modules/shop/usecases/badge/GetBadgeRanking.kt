package jp.co.torihada.fanme.modules.shop.usecases.badge

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.BadgeType
import jp.co.torihada.fanme.modules.shop.models.UserBadge

@ApplicationScoped
class GetBadgeRanking {
    data class Input(val itemId: Long, val badgeType: BadgeType, val limit: Int)

    fun execute(
        params: Input
    ): Result<List<UserBadge.Companion.UserBadgeWithRank>, FanmeException> {
        val userBadgeWithRank =
            UserBadge.findByCreatedAtLimit(
                itemId = params.itemId,
                badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
                limit = params.limit,
            )

        return Ok(userBadgeWithRank)
    }
}
