package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetUserTransactions
@Inject
constructor(
    private val transactionController: TransactionController,
    private val orderController: OrderController,
    private val userController: UserController,
    private val securityUtils: SecurityUtils,
) {

    data class PurchaseHistory(
        val purchaseDate: String,
        val purchaserUserId: String,
        val itemNames: List<String>,
        val sellerSalesAmount: Int,
        val quantity: Int,
        val appName: String? = null,
    )

    data class Output(val purchases: List<PurchaseHistory>, val totalCount: Long? = null)

    fun execute(
        userId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        odata: OData?,
    ): Result<Output, Exception> {
        return try {
            val targetUser = userController.getUserById(userId)
            val targetUserUid = targetUser.uid ?: throw ConsoleResourceNotFoundException("User UID")

            securityUtils.validateAgentForUserAccess(
                targetUser,
                currentUserRole.value,
                currentUserUid,
            )

            val transactions =
                transactionController.getSellerTransactions(
                    sellerUserIds = listOf(targetUserUid),
                    top = odata?.top,
                    skip = odata?.skip,
                )
            val purchases =
                transactions.map { transaction ->
                    val itemNames = getItemNamesForTransaction(transaction)

                    val sellerSalesAmount =
                        transaction.amount
                            ?: throw ConsoleResourceNotFoundException(
                                "Transaction amount for transaction ${transaction.id}"
                            )

                    val purchaserUserId =
                        transaction.purchaserUserId
                            ?: throw ConsoleResourceNotFoundException(
                                "Purchaser user ID for transaction ${transaction.id}"
                            )

                    PurchaseHistory(
                        purchaseDate = transaction.createdAt.toString(),
                        purchaserUserId = purchaserUserId,
                        itemNames = itemNames,
                        sellerSalesAmount = sellerSalesAmount,
                        quantity = itemNames.size,
                        appName = transaction.appName,
                    )
                }
            val totalCount =
                if (odata?.count == true) {
                    transactionController.countBySellerUserId(targetUserUid)
                } else {
                    null
                }

            Ok(Output(purchases = purchases, totalCount = totalCount))
        } catch (e: Exception) {
            Err(e)
        }
    }

    private fun getItemNamesForTransaction(transaction: Transaction): List<String> {
        if (transaction.appName != null) {
            return listOf(transaction.appName!!)
        }

        val transactionId = transaction.id ?: return listOf("Shop購入")

        return try {
            val orders = orderController.getOrders(transactionId)
            val order = orders.firstOrNull() ?: return listOf("Shop購入")
            val items = order.orderItems.map { it.item.name }
            if (items.isNotEmpty()) items else listOf("Shop購入")
        } catch (_: Exception) {
            listOf("Shop購入")
        }
    }
}
