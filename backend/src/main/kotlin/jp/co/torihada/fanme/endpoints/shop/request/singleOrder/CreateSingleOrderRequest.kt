package jp.co.torihada.fanme.endpoints.shop.request.singleOrder

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.endpoints.shop.request.order.ApplePayParam
import jp.co.torihada.fanme.endpoints.shop.request.order.CardParam
import jp.co.torihada.fanme.endpoints.shop.request.order.ConvenienceParam
import jp.co.torihada.fanme.endpoints.shop.request.order.GooglePayParam
import jp.co.torihada.fanme.modules.shop.Util.PaymentMethod
import jp.co.torihada.fanme.modules.shop.controllers.requests.OrderRequest
import jp.co.torihada.fanme.modules.shop.controllers.requests.SingleOrderRequest

data class CreateSingleOrderRequest(
    val itemId: Long,
    val quantity: Int,
    val tip: Int,
    val paymentMethod: String,
    val cardParam: CardParam?,
    val convenienceParam: ConvenienceParam?,
    val googlePayParam: GooglePayParam?,
    val applePayParam: ApplePayParam?,
)

@ApplicationScoped
class RequestConverter() {
    fun requestToSingleCreateOrder(
        uid: String,
        requestBody: CreateSingleOrderRequest,
    ): SingleOrderRequest.CreateOrder {
        return SingleOrderRequest.CreateOrder(
            userId = uid,
            itemId = requestBody.itemId,
            quantity = requestBody.quantity,
            tip = requestBody.tip,
            paymentMethod =
                PaymentMethod.fromString(requestBody.paymentMethod) ?: PaymentMethod.CREDIT_CARD,
            cardParam =
                requestBody.cardParam?.let {
                    OrderRequest.CardParam(cardSequence = it.cardSequence)
                },
            convenienceParam =
                requestBody.convenienceParam?.let {
                    OrderRequest.ConvenienceParam(
                        convenience = it.convenience,
                        customerName = it.customerName,
                        customerKana = it.customerKana,
                        telNo = it.telNo,
                    )
                },
            googlePayParam =
                requestBody.googlePayParam?.let { OrderRequest.GooglePayParam(token = it.token) },
            applePayParam =
                requestBody.applePayParam?.let { OrderRequest.ApplePayParam(token = it.token) },
        )
    }
}
