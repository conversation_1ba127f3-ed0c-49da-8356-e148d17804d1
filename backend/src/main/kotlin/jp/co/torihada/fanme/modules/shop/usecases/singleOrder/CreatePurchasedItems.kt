package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.PurchasedItemStatus
import jp.co.torihada.fanme.modules.shop.models.OrderItem
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem

@ApplicationScoped
class CreatePurchasedItems {
    data class Input(
        val purchaserUserId: String,
        val orderId: Long,
        val amounts: OrderAmounts,
        val status: PurchasedItemStatus,
        val orderItemId: Long,
    )

    data class Output(val purchasedItems: PurchasedItem)

    fun execute(params: Input): Result<Output, FanmeException> {

        val orderItem =
            OrderItem.findById(params.orderItemId) ?: throw ResourceNotFoundException("OrderItem")

        val purchasedItems =
            PurchasedItem.create(
                orderId = params.orderId,
                purchaserUid = params.purchaserUserId,
                itemId = orderItem.item.id!!,
                itemFileId = null,
                price = params.amounts.unitPrices.find { it.item.id == orderItem.item.id }!!.price,
                quantity = orderItem.quantity,
                status = params.status.value,
            )

        return Ok(Output(purchasedItems))
    }
}
