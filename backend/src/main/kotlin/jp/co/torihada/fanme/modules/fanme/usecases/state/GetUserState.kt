package jp.co.torihada.fanme.modules.fanme.usecases.state

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserState

@ApplicationScoped
class GetUserState {

    class Input(val userUuid: String, val key: String)

    fun execute(input: Input): Result<UserState?, Exception> {
        val user = User.findByUuid(input.userUuid) ?: return Err(ResourceNotFoundException("User"))
        val userState = UserState.findByUserIdAndKey(user.id!!, input.key)

        return Ok(userState)
    }
}
// NOTE: keyがDBの予約語として扱われエラーになるためテストは一旦書かない
