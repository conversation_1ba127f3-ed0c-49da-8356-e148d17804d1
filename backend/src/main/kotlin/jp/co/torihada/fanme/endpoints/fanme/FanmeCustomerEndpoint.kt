package jp.co.torihada.fanme.endpoints.fanme

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.FanmeCustomerResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.FanmeCustomerController
import jp.co.torihada.fanme.modules.fanme.controllers.requests.FanmeCustomerRequest
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/fanme-customers")
@Tag(name = "FANME", description = "FANME APIサーバー")
class FanmeCustomerEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var fanmeCustomerHandler: FanmeCustomerController

    @Inject private lateinit var util: Util

    data class SaveFanmeCustomerRequest(
        @JsonProperty("firstName") val firstName: String,
        @JsonProperty("lastName") val lastName: String,
        @JsonProperty("firstNameKana") val firstNameKana: String,
        @JsonProperty("lastNameKana") val lastNameKana: String,
        @JsonProperty("postalCode") val postalCode: String,
        @JsonProperty("prefecture") val prefecture: String,
        @JsonProperty("city") val city: String,
        @JsonProperty("street") val street: String,
        @JsonProperty("building") val building: String?,
        @JsonProperty("phoneNumber") val phoneNumber: String,
    )

    @GET
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(FanmeCustomerResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getFanmeCustomer(): Response {
        return try {
            val creatorUid =
                util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val fanmeCustomer = fanmeCustomerHandler.getFanmeCustomer(creatorUid)
            val entity = fanmeCustomer?.let { ResponseEntity(it, "fanmeCustomer") }

            Response.ok(entity).build()
        } catch (e: Exception) {
            e.printStackTrace()
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }

    @POST
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    @APIResponse(responseCode = "200")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun saveFanmeCustomer(request: SaveFanmeCustomerRequest): Response {
        return try {
            val creatorUid =
                util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()

            fanmeCustomerHandler.saveFanmeCustomer(
                FanmeCustomerRequest.SaveFanmeCustomer(
                    creatorUid = creatorUid,
                    firstName = request.firstName,
                    lastName = request.lastName,
                    firstNameKana = request.firstNameKana,
                    lastNameKana = request.lastNameKana,
                    postalCode = request.postalCode,
                    prefecture = request.prefecture,
                    city = request.city,
                    street = request.street,
                    building = request.building,
                    phoneNumber = request.phoneNumber,
                )
            )

            Response.ok().build()
        } catch (e: Exception) {
            e.printStackTrace()
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }
}
