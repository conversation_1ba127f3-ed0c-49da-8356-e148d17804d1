package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetAgencies {
    data class Input(
        val odata: OData?,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    fun execute(params: Input): Result<List<Agency>, FanmeException> {
        val allowedRolesForFindAll = listOf(ConsoleUserRole.SUPER, ConsoleUserRole.BIZ)

        if (params.currentUserRole in allowedRolesForFindAll) {
            return Ok(Agency.findAll(params.odata?.top, params.odata?.skip))
        }

        val consoleUser = ConsoleUser.findByUserUid(params.currentUserUid) ?: return Ok(emptyList())
        val agency = consoleUser.agency?.takeIf { it.deletedAt == null } ?: return Ok(emptyList())

        return Ok(listOf(agency))
    }
}
