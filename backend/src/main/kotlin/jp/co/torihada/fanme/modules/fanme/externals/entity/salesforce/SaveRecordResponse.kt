package jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.runtime.annotations.RegisterForReflection

@RegisterForReflection
data class SaveRecordResponse(
    @JsonProperty("id") val id: String? = null,
    @JsonProperty("success") val success: Boolean?,
    @JsonProperty("errors") val errors: List<Error>? = emptyList(),
) {
    data class Error(
        @JsonProperty("statusCode") val statusCode: String? = null,
        @JsonProperty("message") val message: String? = null,
        @JsonProperty("fields") val fields: List<String> = emptyList(),
    )
}
