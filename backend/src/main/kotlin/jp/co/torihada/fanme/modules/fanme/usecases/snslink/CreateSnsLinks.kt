package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.SnsLink
import jp.co.torihada.fanme.modules.fanme.models.SnsLinkDisplay
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class CreateSnsLinks {

    data class Input(val userId: Long, val snsLinks: List<SnsLinkInput>) {
        data class SnsLinkInput(
            val type: String,
            val accountIdentity: String,
            val displayOrderNumber: Int,
            val displayable: <PERSON>olean,
        )
    }

    @Schema(name = "CreateSnsLinks_Response") data class Output(val snsLinks: List<SnsLink>)

    fun execute(input: Input): Result<Output, Exception> {
        val profile =
            Profile.findByUserId(input.userId) ?: return Err(ResourceNotFoundException("Profile"))

        val snsLinks = mutableListOf<SnsLink>()
        for (snsLinkInput in input.snsLinks) {
            val snsLinkType =
                Const.SnsLinkType.fromValue(snsLinkInput.type)
                    ?: return Err(
                        IllegalArgumentException("Invalid SNS link type: ${snsLinkInput.type}")
                    )
            val snsLink =
                SnsLink.create(
                    profile = profile,
                    type = snsLinkType.dbStr,
                    accountIdentity = snsLinkInput.accountIdentity,
                )
            val snsLinkDisplay =
                SnsLinkDisplay.create(
                    profile = profile,
                    snsLink = snsLink,
                    displayOrderNumber = snsLinkInput.displayOrderNumber,
                    displayable = snsLinkInput.displayable,
                )

            snsLink.snsLinkDisplay = snsLinkDisplay
            snsLinks.add(snsLink)
        }

        return Ok(Output(snsLinks))
    }
}
