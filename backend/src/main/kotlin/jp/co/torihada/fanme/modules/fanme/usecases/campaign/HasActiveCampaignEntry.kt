package jp.co.torihada.fanme.modules.fanme.usecases.campaign

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.CampaignEntry
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class HasActiveCampaignEntry {
    companion object {
        private const val SECONDS_PER_DAY = 86400L
    }

    fun execute(userUid: String, actionType: Campaign.ActionType): Result<Boolean, FanmeException> {
        val user =
            User.Companion.findByUuid(userUid) ?: return Err(ResourceNotFoundException("User"))

        val campaignEntries = CampaignEntry.find(user, actionType)
        if (campaignEntries.isEmpty()) {
            return Ok(false)
        }

        val now = Instant.now()
        val hasActiveEntry =
            campaignEntries.any { entry ->
                val campaign = entry.campaign
                val validUntil =
                    entry.enteredAt.plusSeconds(
                        (campaign?.actionDurationDays ?: 0) * SECONDS_PER_DAY
                    )
                entry.enteredAt <= now && now <= validUntil
            }
        return Ok(hasActiveEntry)
    }
}
