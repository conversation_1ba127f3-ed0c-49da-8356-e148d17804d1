package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController as FanmeAuditGroupController
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditGroupsListResult
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetAuditGroups {
    @Inject private lateinit var auditGroupController: FanmeAuditGroupController

    data class Input(val odata: OData?)

    fun execute(params: Input): Result<AuditGroupsListResult, FanmeException> {
        return try {
            val auditGroups = auditGroupController.getAuditGroups(params.odata)
            Ok(auditGroups)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
