package jp.co.torihada.fanme.modules.fanme.controllers.requests

import jakarta.enterprise.context.ApplicationScoped
import jakarta.validation.constraints.Pattern
import jp.co.torihada.fanme.lib.validator.ValueOfEnum
import jp.co.torihada.fanme.modules.fanme.Const

@ApplicationScoped
class UserRequest {

    data class UpdateUser(
        val userUuid: String,
        val icon: String? = null,
        val name: String? = null,
        @field:ValueOfEnum(enumClass = Const.Gender::class) val gender: String? = null,
        @Pattern(
            regexp = "^\\d{4}-\\d{2}-\\d{2}$",
            message = "Birthday must be in yyyy-mm-dd format",
        )
        val birthday: String? = null,
        @field:ValueOfEnum(enumClass = Const.Purpose::class) val purpose: Int? = null,
        val accountIdentity: String? = null,
    )
}
