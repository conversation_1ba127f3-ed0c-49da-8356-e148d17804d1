package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover

@ApplicationScoped
class UpdateProfileCover {

    data class Input(val userId: Long, val coverImageVisibility: Boolean?, val brightness: String?)

    fun execute(input: Input): Result<ProfileCover, Exception> {
        val profileCover =
            Profile.findByUserId(input.userId)?.cover
                ?: return Err(ResourceNotFoundException("Profile cover"))

        val result =
            ProfileCover.update(
                profileCover = profileCover,
                coverVisibility = input.coverImageVisibility,
                brightness = input.brightness,
            )

        return Ok(result)
    }
}
