package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import io.vertx.ext.web.RoutingContext
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.Context
import jakarta.ws.rs.core.HttpHeaders
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.SingleOrderResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.order.FinalizeCreditCard3DSecureRequest
import jp.co.torihada.fanme.endpoints.shop.request.singleOrder.CreateSingleOrderRequest
import jp.co.torihada.fanme.endpoints.shop.request.singleOrder.RequestConverter
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.SingleOrderController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/single-order")
class SingleOrderEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var handler: SingleOrderController
    @Inject lateinit var requestConverter: RequestConverter
    @Inject lateinit var util: Util

    @POST
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    @APIResponse(responseCode = "200")
    @APIResponseSchema(SingleOrderResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun createOrder(
        @Context headers: HttpHeaders,
        requestBody: CreateSingleOrderRequest,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val fanmeToken =
                headers
                    .getHeaderString("Authorization")
                    .takeIf { it.startsWith("Bearer ") }
                    ?.removePrefix("Bearer ")
            val request = requestConverter.requestToSingleCreateOrder(userUid, requestBody)
            val result = handler.createOrder(request, fanmeToken)
            val entity = ResponseEntity(result, "order")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @POST
    @Path("/finalize-credit-card-3d-secure")
    @Consumes("application/json")
    @Produces("application/json")
    fun finalizeCreditCard3DSecureSingleOrder(
        requestBody: FinalizeCreditCard3DSecureRequest,
        @Context ctx: RoutingContext,
    ): Response {
        return try {
            if (!util.doBasicAuth(ctx)) {
                return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(UnAuthorizedException())
                    .build()
            }
            handler.finalizeCreditCard3DSecureSingleOrder(
                requestBody.transactionId,
                requestBody.checkoutId,
            )
            Response.ok().build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
