package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.Profile

@ApplicationScoped
class UpdateProfile {

    class Input(
        val userId: Long,
        val bio: String? = null,
        val headerImage: String? = null,
        val snsLinkColor: String? = null,
    )

    fun execute(input: Input): Result<Profile, Exception> {
        val profile =
            Profile.findByUserId(input.userId) ?: return Err(ResourceNotFoundException("Profile"))

        val result =
            Profile.update(
                profile,
                bio = input.bio,
                headerImage = input.headerImage,
                snsLinkColor = input.snsLinkColor,
            )

        return Ok(result)
    }
}
