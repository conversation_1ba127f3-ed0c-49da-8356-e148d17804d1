package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.endpoints.console.UserContentBlockEndpoint.UpdateContentBlockDetailRequest
import jp.co.torihada.fanme.modules.console.usecases.CreateContentBlock
import jp.co.torihada.fanme.modules.console.usecases.GetUserContentBlocks
import jp.co.torihada.fanme.modules.console.usecases.UpdateContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks

@ApplicationScoped
class UserContentBlockController {

    @Inject lateinit var getUserContentBlocks: GetUserContentBlocks
    @Inject lateinit var createContentBlock: CreateContentBlock
    @Inject lateinit var updateContentBlockDetail: UpdateContentBlockDetail

    fun getUserContentBlocks(
        accountIdentity: String,
        currentUserRole: String?,
        currentUserUid: String?,
    ): List<GetContentBlocks.ContentBlock> {
        return getUserContentBlocks
            .execute(accountIdentity, currentUserRole, currentUserUid)
            .getOrThrow()
    }

    @Transactional(rollbackOn = [Exception::class])
    fun createContentBlock(
        accountIdentity: String,
        request: CreateContentBlock.CreateContentBlockRequest,
        currentUserRole: String?,
        currentUserUid: String?,
    ): ContentBlock {
        return createContentBlock
            .execute(accountIdentity, request, currentUserRole, currentUserUid)
            .getOrThrow()
    }

    @Transactional(rollbackOn = [Exception::class])
    fun updateContentBlockDetail(
        accountIdentity: String,
        request: UpdateContentBlockDetailRequest,
        currentUserRole: String?,
        currentUserUid: String?,
    ): ContentBlockDetail {
        return updateContentBlockDetail
            .execute(accountIdentity, request, currentUserRole, currentUserUid)
            .getOrThrow()
    }
}
