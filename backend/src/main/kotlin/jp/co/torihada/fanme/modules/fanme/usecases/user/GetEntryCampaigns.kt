package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.CampaignEntry
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetEntryCampaigns {
    companion object {
        private const val SECONDS_PER_DAY = 86400L
    }

    data class Input(val userUid: String)

    fun execute(params: Input): Result<List<Campaign>, FanmeException> {
        val user =
            User.findByUuid(params.userUid)
                ?: return Err(jp.co.torihada.fanme.exception.ResourceNotFoundException("User"))
        return try {
            val now = Instant.now()
            val campaignEntries = CampaignEntry.findByUser(user)
            val campaigns =
                campaignEntries.mapNotNull { entry ->
                    val campaign = entry.campaign
                    val validUntil =
                        entry.enteredAt.plusSeconds(
                            (campaign?.actionDurationDays ?: 0) * SECONDS_PER_DAY
                        )
                    if (campaign != null && entry.enteredAt <= now && now <= validUntil) {
                        campaign
                    } else {
                        null
                    }
                }
            Ok(campaigns)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
