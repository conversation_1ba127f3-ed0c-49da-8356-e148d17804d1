package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util.Companion.getCurrentPrice
import jp.co.torihada.fanme.modules.shop.models.Item as ItemModel
import jp.co.torihada.fanme.modules.shop.models.ItemFile
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.aws.S3

@ApplicationScoped
class GetCurrentItem {

    @Inject private lateinit var s3: S3

    @Inject private lateinit var config: Config

    @Inject private lateinit var userController: UserController

    @Inject private lateinit var auditGroupController: AuditGroupController

    data class Input(val itemId: Long, val creatorUid: String, val userUid: String)

    fun execute(params: Input): Result<GetItem.ItemForGetItem, FanmeException> {
        val item =
            ItemModel.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        if (item.shop.id != shop.id) return Err(ResourceNotFoundException("Item"))

        val creator = userController.getUser(shop.creatorUid!!)
        val files =
            item.files
                .takeIf { it.isNotEmpty() }
                ?.let {
                    if (item.itemType.isGacha()) {
                        it.sortedWith(
                            compareByDescending<ItemFile> { file ->
                                    file.gachaItemFile?.awardType?.value
                                }
                                .thenBy { file -> file.id }
                        )
                    } else {
                        item.sortedFiles()
                    }
                }
        if ((item.isDigital || item.itemType.isPrintGacha()) && files.isNullOrEmpty()) {
            return Err(ResourceNotFoundException("ItemFile"))
        }

        val option = item.option ?: return Err(ResourceNotFoundException("ItemOption"))
        val forSale =
            GetItem.ForSaleData(
                startAt = option.forSaleStartAt?.toString(),
                endAt = option.forSaleEndAt?.toString(),
            )
        val benefits = item.benefits
        val samples = item.samples.sortedBy { it.id }
        val onSale = item.onSale
        val tags = item.tags.map { it.tag.tag }
        val remainingAmount =
            if (option.qtyTotal != null)
                option.qtyTotal!! - PurchasedItem.countPurchasedItem(params.itemId)
            else null
        val isDuplicatedDigitalGachaItems = item.gachaItem?.isDuplicated ?: false
        val isPublishLocked = auditGroupController.getItemPublishLocked(item.id!!)

        val output =
            GetItem.ItemForGetItem(
                id = item.id!!,
                creatorAccountIdentity = creator.accountIdentity ?: "",
                name = item.name,
                description = item.description,
                thumbnailUri = item.thumbnailUri,
                thumbnailFrom = item.thumbnailFrom,
                thumbnailBlurLevel = item.thumbnailBlurLevel,
                thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                price = item.price,
                currentPrice = getCurrentPrice(item),
                fileType = item.fileType.toString(2).padStart(3, '0').reversed(),
                available = item.available,
                awardProbabilities =
                    item.gachaItem?.gachaProbability?.map {
                        GetItem.AwardProbability(
                            awardType = it.awardType.value,
                            probability = it.probability,
                        )
                    },
                isDuplicatedDigitalGachaItems = isDuplicatedDigitalGachaItems,
                itemType = item.itemType.value,
                files =
                    files?.map {
                        GetItem.File(
                            id = it.id,
                            name = it.name,
                            objectUri = s3.getObjectUri(it.objectUri!!),
                            thumbnailUri = s3.getObjectUri(it.thumbnailUri!!),
                            price = it.price,
                            currentPrice = getCurrentPrice(it),
                            fileType = it.fileType,
                            size = it.size,
                            duration = it.duration,
                            itemThumbnailSelected = it.itemThumbnailSelected,
                            awardType = it.gachaItemFile?.awardType?.value,
                            isSecret = it.gachaItemFile?.isSecret ?: false,
                        )
                    },
                samples =
                    if (samples.isNotEmpty()) {
                        samples.map { sample ->
                            GetItem.File(
                                id = sample.id,
                                name = sample.name,
                                objectUri = sample.objectUri,
                                thumbnailUri = sample.thumbnailUri,
                                fileType = sample.fileType,
                                size = sample.size,
                                duration = sample.duration,
                            )
                        }
                    } else {
                        null
                    },
                benefits =
                    if (benefits.isNotEmpty()) {
                        benefits.map { benefit ->
                            GetItem.BenefitParam(
                                id = benefit.id!!,
                                description = benefit.description,
                                conditionType = benefit.conditionType.value,
                                files =
                                    benefit.files.map { file ->
                                        GetItem.BenefitFile(
                                            id = file.id,
                                            name = file.name,
                                            objectUri = s3.getObjectUri(file.objectUri),
                                            thumbnailUri =
                                                file.thumbnailUri?.let { s3.getObjectUri(it) },
                                            fileType = file.fileType,
                                            size = file.size,
                                            duration = file.duration,
                                        )
                                    },
                            )
                        }
                    } else {
                        null
                    },
                tags = tags,
                itemOption =
                    GetItem.OptionData(
                        isSingleSales = option.isSingleSales,
                        qtyTotal = option.qtyTotal,
                        qtyPerUser = option.qtyPerUser,
                        remainingAmount = remainingAmount,
                        forSale = forSale,
                        password = option.password,
                        onSale =
                            if (onSale != null) {
                                GetItem.OnSaleData(
                                    discountRate = onSale.discountRate,
                                    startAt = onSale.startAt?.toString(),
                                    endAt = onSale.endAt?.toString(),
                                )
                            } else {
                                null
                            },
                    ),
                isPublishLocked = isPublishLocked,
            )

        return Ok(output)
    }
}
