package jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer

import jp.co.torihada.fanme.externals.client.salesforce.entities.fanmeCustomer.FanmeCustomerRecord
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.Attributes

object FanmeCustomerMapper {
    fun fromRecord(record: FanmeCustomerRecord): FanmeCustomerEntity {
        val names = record.name.split("　")
        val namesKana = record.nameKana.split("　")

        return FanmeCustomerEntity(
            creatorUid = record.fanmeUserId,
            firstName = names[1],
            lastName = names[0],
            firstNameKana = namesKana[1],
            lastNameKana = namesKana[0],
            postalCode = record.postalCode,
            prefecture = record.prefecture,
            city = record.city,
            street = record.street,
            building = record.building,
            phoneNumber = record.phoneNumber,
        )
    }

    fun toRecord(entity: FanmeCustomerEntity): FanmeCustomerRecord {
        return FanmeCustomerRecord(
            attributes = Attributes(type = "FanmeCustomer__c"),
            fanmeUserId = entity.creatorUid,
            name = "${entity.lastName}　${entity.firstName}",
            nameKana = "${entity.lastNameKana}　${entity.firstNameKana}",
            postalCode = entity.postalCode,
            prefecture = entity.prefecture,
            city = entity.city,
            street = entity.street,
            building = entity.building,
            phoneNumber = entity.phoneNumber,
        )
    }
}
