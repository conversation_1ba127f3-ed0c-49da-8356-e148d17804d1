package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetAgencyUsers {

    @Inject private lateinit var securityUtils: SecurityUtils

    data class Input(
        val agencyId: Long,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    fun execute(input: Input): Result<List<User>, Exception> {
        securityUtils.validateAgentAccess(
            input.agencyId,
            input.currentUserUid,
            input.currentUserRole.value,
        )

        val consoleUsers = ConsoleUser.findByAgencyId(input.agencyId)
        val users = consoleUsers.map { it.user }

        return Ok(users)
    }
}
