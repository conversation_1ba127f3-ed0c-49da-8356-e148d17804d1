package jp.co.torihada.fanme.modules.shop.services

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.shop.models.ItemType

@ApplicationScoped
class ItemCostService {
    fun getItemCost(itemType: Int): Int {
        val itemTypeData = ItemType.fromValue(itemType)

        if (itemTypeData != null) {
            return itemTypeData.cost
        }

        return 0
    }
}
