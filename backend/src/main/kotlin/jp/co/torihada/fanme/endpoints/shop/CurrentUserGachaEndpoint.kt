package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.ItemResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.item.gacha.create.CreateGachaItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.gacha.create.CreateGachaItemRequestConverter
import jp.co.torihada.fanme.endpoints.shop.request.item.gacha.update.UpdateGachaItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.gacha.update.UpdateGachaItemRequestConverter
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.GachaController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/current/gacha")
class CurrentUserGachaEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var requestContext: ContainerRequestContext
    @Inject private lateinit var handler: GachaController
    @Inject private lateinit var createGachaConverter: CreateGachaItemRequestConverter
    @Inject private lateinit var updateGachaConverter: UpdateGachaItemRequestConverter
    @Inject private lateinit var util: Util

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ItemResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun createGachaItem(requestBody: CreateGachaItemRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = createGachaConverter.requestToCreateItem(userUid, requestBody)
            val result = handler.createItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @PUT
    @RolesAllowed("LoginUser")
    @Path("/{item_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ItemResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun updateGachaItem(
        @PathParam("item_id") id: Long,
        requestBody: UpdateGachaItemRequest,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = updateGachaConverter.requestToUpdateItem(userUid, id, requestBody)
            val result = handler.updateItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
