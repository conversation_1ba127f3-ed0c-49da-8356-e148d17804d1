package jp.co.torihada.fanme.modules.shop.usecases.salesHIstory

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.controllers.CheckoutController
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetSalesHistories {

    @Inject private lateinit var userController: UserController
    @Inject private lateinit var checkoutController: CheckoutController

    data class Input(val creatorUid: String, val odata: OData?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class SalesHistory(
        val orderId: Long,
        val title: String,
        val thumbnailUri: String,
        val amount: Int,
        val tipAmount: Int,
        val soledAt: String,
        val purchaser: Purchaser,
        val salesDetail: SalesDetail,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Purchaser(val name: String, val iconUri: String)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class SalesDetail(
        val shopName: String,
        val shopHeaderImageUri: String,
        val soledItems: List<SoledItem>,
        val status: String,
        val soledAt: String,
        val sales: Sales,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class SoledItem(
        val itemName: String,
        val itemThumbnailUri: String,
        val unitPrice: Int,
        val quantity: Int,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Sales(
        val itemAmount: Int,
        val tipAmount: Int,
        val profit: Int,
        val fee: Int,
        val deliveryFee: Int,
    )

    fun execute(params: Input): Result<List<SalesHistory>, FanmeException> {
        val result = mutableListOf<SalesHistory>()
        val shop = Shop.findByCreatorUid(params.creatorUid)
        if (shop != null) {
            val orders = Order.findSucceedOrdersByShopId(shop.id!!, params.odata)
            val checkoutIds = orders.mapNotNull { it.checkoutId }
            val checkouts = checkoutController.getCheckouts(checkoutIds)

            val purchaserUids = orders.map { it.purchaserUid }.distinct()
            val purchasers = userController.getUsers(purchaserUids)
            for (order in orders) {
                val amount = order.purchasedItems.sumOf { it.price * it.quantity }
                val checkout = checkouts.find { it.id == order.checkoutId }
                val purchaser = purchasers.find { it.uid == order.purchaserUid }
                if (checkout == null) {
                    continue
                }
                val soledAt = Util.toJSTLocalDateTimeString(order.updatedAt!!)
                val salesHistory =
                    SalesHistory(
                        orderId = order.id!!,
                        title = order.shop.name,
                        thumbnailUri = order.shop.headerImageUri ?: "",
                        amount = amount,
                        tipAmount = checkout.tip?.amount ?: 0,
                        soledAt = soledAt,
                        purchaser =
                            Purchaser(
                                name = purchaser?.name ?: "",
                                iconUri = purchaser?.iconUrl ?: "",
                            ),
                        salesDetail =
                            SalesDetail(
                                shopName = shop.name,
                                shopHeaderImageUri = shop.headerImageUri ?: "",
                                soledItems =
                                    order.purchasedItems
                                        .sortedBy { it.id }
                                        .map {
                                            SoledItem(
                                                itemName =
                                                    if (it.itemFile != null)
                                                        it.item.name + " - " + it.itemFile!!.name
                                                    else it.item.name,
                                                itemThumbnailUri = it.item.thumbnailUri,
                                                unitPrice = it.price,
                                                quantity = it.quantity,
                                            )
                                        },
                                status =
                                    Const.CheckoutStatus.fromValue(
                                            order.purchasedItems.first().status
                                        )
                                        ?.displayName ?: "",
                                soledAt = soledAt,
                                sales =
                                    Sales(
                                        itemAmount = amount,
                                        tipAmount = checkout.tip?.amount ?: 0,
                                        profit = checkout.sellerSalesAmount ?: 0,
                                        fee = checkout.tenantSalesAmount ?: 0,
                                        deliveryFee = checkout.deliveryFee ?: 0,
                                    ),
                            ),
                    )
                result.add(salesHistory)
            }
        }
        result.sortByDescending { it.soledAt }

        return Ok(result)
    }
}
