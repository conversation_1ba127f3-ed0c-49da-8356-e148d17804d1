package jp.co.torihada.fanme.exception

import io.quarkus.security.ForbiddenException
import jakarta.ws.rs.NotFoundException
import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider
import jp.co.torihada.fanme.dto.BaseResponseBody
import kotlin.reflect.full.findAnnotation

@Provider
class FanmeExceptionMapper : ExceptionMapper<Throwable> {

    override fun toResponse(exception: Throwable): Response {
        return when (exception) {
            is ForbiddenException -> {
                Response.status(Response.Status.FORBIDDEN)
                    .entity(
                        BaseResponseBody(
                            data = null,
                            errors = listOf(ErrorObject(code = 403, message = "Access forbidden")),
                        )
                    )
                    .build()
            }
            is NotFoundException -> {
                Response.status(Response.Status.NOT_FOUND)
                    .entity(
                        BaseResponseBody(
                            data = null,
                            errors = listOf(ErrorObject(code = 404, message = "Resource not found")),
                        )
                    )
                    .build()
            }
            is FanmeException -> {
                val status =
                    exception::class.findAnnotation<HttpStatus>()?.status
                        ?: Response.Status.INTERNAL_SERVER_ERROR
                Response.status(status)
                    .entity(
                        BaseResponseBody(data = emptyMap<String, Any>(), errors = exception.errors)
                    )
                    .build()
            }
            else -> {
                Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(
                        BaseResponseBody(
                            data = null,
                            errors =
                                listOf(ErrorObject(code = 500, message = "Internal Server Error")),
                        )
                    )
                    .build()
            }
        }
    }
}
