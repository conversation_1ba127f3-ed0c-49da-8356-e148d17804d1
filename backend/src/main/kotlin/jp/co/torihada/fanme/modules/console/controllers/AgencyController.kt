package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Pattern
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.GetAgencies
import jp.co.torihada.fanme.modules.console.usecases.GetAgencyMonthlySales
import jp.co.torihada.fanme.modules.console.usecases.GetAgencySales
import jp.co.torihada.fanme.modules.console.usecases.GetAgencyUsers
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class AgencyController {

    @Inject private lateinit var getAgency: GetAgencies
    @Inject private lateinit var getAgencySales: GetAgencySales
    @Inject private lateinit var getAgencyUsers: GetAgencyUsers
    @Inject private lateinit var getAgencyMonthlySales: GetAgencyMonthlySales

    fun getAgencies(
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        odata: OData?,
    ): List<Agency> {
        return getAgency
            .execute(GetAgencies.Input(odata, currentUserUid, currentUserRole))
            .getOrThrow()
    }

    fun getAgencyUsers(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): List<User> {
        val input =
            GetAgencyUsers.Input(
                agencyId = agencyId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )
        return getAgencyUsers.execute(input).getOrThrow()
    }

    fun getAgencySales(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): GetAgencySales.AgencySales {
        val input =
            GetAgencySales.Input(
                agencyId = agencyId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )

        return getAgencySales.execute(input).getOrThrow()
    }

    fun getAgencyMonthlySales(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        @Pattern(regexp = "^\\d{6}$", message = "Date must be in YYYYMM format") from: String?,
        @Pattern(regexp = "^\\d{6}$", message = "Date must be in YYYYMM format") to: String?,
        odata: OData?,
    ): GetAgencyMonthlySales.AgencyMonthlySalesResult {
        val formatter = DateTimeFormatter.ofPattern("yyyyMM")
        val fromYearMonth = from?.let { YearMonth.parse(it, formatter) }
        val toYearMonth = to?.let { YearMonth.parse(it, formatter) }
        return getAgencyMonthlySales
            .execute(
                agencyId = agencyId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
                from = fromYearMonth,
                to = toYearMonth,
                odata = odata,
            )
            .getOrThrow()
    }
}
