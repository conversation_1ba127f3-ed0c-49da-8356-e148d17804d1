package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.UserAccountIdentityAlreadyExistsException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class CheckAccountIdentityAvailable {

    data class Input(val accountIdentity: String)

    fun execute(input: Input): Result<Boolean, FanmeException> {
        User.findNotDeletedByAccountIdentity(input.accountIdentity) ?: return Ok(true)

        return Err(UserAccountIdentityAlreadyExistsException())
    }
}
