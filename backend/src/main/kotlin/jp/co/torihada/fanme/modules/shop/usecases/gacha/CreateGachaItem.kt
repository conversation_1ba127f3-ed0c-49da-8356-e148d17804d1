package jp.co.torihada.fanme.modules.shop.usecases.gacha

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.BenefitFile as BenefitFileModel
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService

@ApplicationScoped
class CreateGachaItem {
    @jakarta.inject.Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val creatorUid: String,
        val name: String,
        val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int,
        val thumbnailBlurLevel: Int,
        val thumbnailWatermarkLevel: Int,
        val price: Int,
        val available: Boolean,
        val isDuplicated: Boolean,
        val files: List<File>,
        val samples: List<SampleFile>?,
        val benefit: List<BenefitParam>?,
        val tags: List<String>?,
        val itemOption: ItemOptionParam,
        val awardProbabilities: List<AwardProbability>,
        val itemType: ItemType = ItemType.DIGITAL_GACHA, // デフォルトはDIGITAL_GACHA（後方互換性のため）
    )

    data class File(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val maskedThumbnailUri: String?,
        val watermarkThumbnailUri: String?,
        val price: Int?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean = false,
        val sortOrder: Int = 0,
        val awardType: AwardType? = null,
        val isSecret: Boolean = false,
    )

    data class SampleFile(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    data class BenefitFile(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    data class AwardProbability(val probability: Int, val awardType: AwardType)

    data class BenefitParam(
        val description: String?,
        val conditionType: Int,
        val files: List<BenefitFile>,
    )

    data class ItemOptionParam(
        val qtyTotal: Int?,
        val forSale: ForSale?,
        val password: String?,
        val onSale: OnSale?,
    )

    data class ForSale(val startAt: Instant?, val endAt: Instant?)

    data class OnSale(val discountRate: Float, val startAt: Instant?, val endAt: Instant?)

    fun execute(params: Input): Result<Item, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        val itemTypeMarginRate =
            ShopItemTypeMarginRate.find(shop.id!!, params.itemType)
                ?: return Err(ResourceNotFoundException("ShopItemTypeMarginRate"))

        val item =
            Item.create(
                shop.id!!,
                params.name,
                params.description,
                params.thumbnailUri,
                params.thumbnailFrom,
                params.thumbnailBlurLevel,
                params.thumbnailWatermarkLevel,
                params.price,
                0,
                params.available,
                params.itemType,
                itemTypeMarginRate.marginRate,
            )

        val gachaItem = GachaItem.create(item.id!!, params.isDuplicated)

        params.awardProbabilities.forEach {
            GachaProbability.create(gachaItem.id!!, it.awardType, it.probability)
        }

        params.files.map {
            val itemFile =
                ItemFile.create(
                    item.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    it.maskedThumbnailUri,
                    it.watermarkThumbnailUri,
                    it.price,
                    it.fileType,
                    it.size,
                    it.duration,
                    it.itemThumbnailSelected,
                    it.sortOrder,
                )

            GachaItemFile.create(itemFile.id!!, it.awardType ?: AwardType.C, isSecret = it.isSecret)
        }

        val files = ItemFile.findByItemId(item.id!!)
        val itemFileType = Util.getItemFileType(files)
        item.fileType = itemFileType
        item.persist()

        ItemOption.create(
            itemId = item.id!!,
            isSingleSales = false, // ガチャの単品販売はない
            qtyTotal = params.itemOption.qtyTotal,
            forSaleStartAt = params.itemOption.forSale?.startAt,
            forSaleEndAt = params.itemOption.forSale?.endAt,
            password = params.itemOption.password,
        )
        params.itemOption.onSale?.let {
            ItemOnSale.create(item.id!!, it.discountRate, it.startAt, it.endAt)
        }
        val samples =
            params.samples?.map {
                Sample.create(
                    item.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    it.fileType,
                    it.size,
                    it.duration,
                )
            }

        var benefitFiles: List<BenefitFileModel> = mutableListOf()
        if (params.benefit != null) {
            params.benefit.forEach {
                val benefit =
                    Benefit.create(
                        item.id!!,
                        it.description,
                        BenefitCondition.fromValue(it.conditionType),
                    )
                benefitFiles +=
                    it.files.map { file ->
                        BenefitFileModel.create(
                            benefit.id!!,
                            file.name,
                            file.objectUri,
                            file.thumbnailUri,
                            file.fileType,
                            file.size,
                            file.duration,
                        )
                    }
            }
        }

        if (params.tags != null) {
            val existTags = Tag.findByShopId(shop.id!!)
            params.tags.forEach {
                if (existTags.find { t -> t.tag == it } == null) {
                    Tag.create(shop.id!!, it)
                }
                ItemTag.create(item.id!!, it)
            }
        }

        // 監査データの作成
        shopAuditService.createAuditDataForShopItem(
            params.creatorUid,
            ShopAuditService.OperationType.INSERT,
            item,
            files,
            samples,
            benefitFiles.ifEmpty { null },
        )
        return Ok(item)
    }
}
