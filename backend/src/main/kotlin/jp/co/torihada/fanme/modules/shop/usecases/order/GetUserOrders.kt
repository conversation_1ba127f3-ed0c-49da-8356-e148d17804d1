package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetUserOrders {
    data class Params(val userId: String, val odata: OData)

    fun execute(params: Params): Result<List<Order>, FanmeException> {
        return Ok(Order.findByPurchaserUid(params.userId, params.odata.top, params.odata.skip))
    }
}
