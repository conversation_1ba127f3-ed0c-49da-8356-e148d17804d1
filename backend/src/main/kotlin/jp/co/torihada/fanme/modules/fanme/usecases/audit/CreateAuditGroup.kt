package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.OperationType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupService
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupServiceAuditObject

@ApplicationScoped
class CreateAuditGroup @Inject constructor(private val auditGroupService: AuditGroupService) {
    fun execute(
        userUid: String,
        auditType: AuditType,
        operationType: OperationType,
        metadata: AuditGroupMetadata,
        auditObjects: List<AuditGroupServiceAuditObject>,
    ): Result<Long, Exception> {
        return try {
            val auditGroupId =
                auditGroupService.createAuditGroup(
                    auditType = auditType,
                    userUid = userUid,
                    operationType = operationType,
                    metadata = metadata,
                    auditObjects = auditObjects,
                )
            Ok(auditGroupId)
        } catch (e: Exception) {
            Err(e)
        }
    }
}
