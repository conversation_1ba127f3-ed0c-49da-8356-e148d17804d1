package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.console.CommissionControlEndpoint.ItemMarginRateItem
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.ItemMarginRateController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController

@ApplicationScoped
class UpdateItemMarginRates
@Inject
constructor(
    private val userController: UserController,
    private val shopController: ShopController,
    private val itemMarginRateController: ItemMarginRateController,
) {

    data class Input(
        val accountIdentity: String,
        val items: List<ItemMarginRateItem>,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    data class ItemMarginRateResult(val shopId: Long, val itemId: Long, val marginRate: Float)

    fun execute(params: Input): Result<List<ItemMarginRateResult>, ConsoleException> {
        return try {
            val user =
                userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                    ?: throw ResourceNotFoundException("User")

            val shopDto = shopController.getShop(user.uid!!)

            val updatedItems =
                itemMarginRateController.updateItemMarginRates(
                    params.items.map {
                        ItemMarginRateController.ItemRateParam(it.itemId, it.marginRate)
                    }
                )

            Ok(updatedItems.map { ItemMarginRateResult(shopDto.id, it.itemId, it.marginRate) })
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: FanmeException) {
            Err(ConsoleException(0, e.message ?: "FanmeException"))
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }
}
