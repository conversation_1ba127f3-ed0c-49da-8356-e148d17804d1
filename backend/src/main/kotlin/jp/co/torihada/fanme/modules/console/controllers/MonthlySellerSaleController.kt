package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrElse
import io.quarkus.security.identity.SecurityIdentity
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Pattern
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.console.usecases.GetUserMonthlySales
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class MonthlySellerSaleController
@Inject
constructor(
    private val getUserMonthlySales: GetUserMonthlySales,
    private val securityIdentity: SecurityIdentity,
    private val util: Util,
) {

    fun getUserMonthlySales(
        userId: Long,
        @Pattern(regexp = "^\\d{6}$", message = "Date must be in YYYYMM format") from: String?,
        @Pattern(regexp = "^\\d{6}$", message = "Date must be in YYYYMM format") to: String?,
        odata: OData?,
    ): GetUserMonthlySales.Response {
        val currentUserUid =
            util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val currentUserRole =
            util.getCurrentUserRole(securityIdentity) ?: throw UnAuthorizedException()

        val formatter = DateTimeFormatter.ofPattern("yyyyMM")
        val fromYearMonth = from?.let { YearMonth.parse(it, formatter) }
        val toYearMonth = to?.let { YearMonth.parse(it, formatter) }

        return getUserMonthlySales
            .execute(
                userId = userId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
                from = fromYearMonth,
                to = toYearMonth,
                odata = odata,
            )
            .getOrElse { throw it }
    }
}
