package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.ProxyAccessTokenController
import org.eclipse.microprofile.openapi.annotations.tags.Tag

data class ProxyAccessTokenData(val proxyAccessToken: String)

class ProxyAccessTokenResponse(
    data: ProxyAccessTokenData,
    errors: List<ErrorObject> = emptyList(),
) : BaseResponseBody<ProxyAccessTokenData>(data, errors)

@Path("/console/users/{account-identity}/proxy-access-token")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class ProxyAccessTokenEndpoint {

    @Inject private lateinit var controller: ProxyAccessTokenController

    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var util: Util

    @POST
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun createProxyAccessToken(
        @PathParam("account-identity") accountIdentity: String
    ): ProxyAccessTokenResponse {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)?.name

        val result =
            controller.createProxyAccessToken(
                targetUserAccountIdentity = accountIdentity,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid,
            )

        return ProxyAccessTokenResponse(
            data = ProxyAccessTokenData(proxyAccessToken = result.proxyAccessToken)
        )
    }
}
