package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController as FanmeAuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerUpdateStatusInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus

@ApplicationScoped
class UpdateAuditStatus {
    @Inject private lateinit var auditGroupController: FanmeAuditGroupController

    data class Input(
        val auditGroupId: Long,
        val status: AuditStatus,
        val comment: String? = null,
        val auditedUserUid: String? = null,
    )

    fun execute(params: Input): Result<Unit, FanmeException> {
        return try {
            auditGroupController.updateAuditStatus(
                AuditGroupControllerUpdateStatusInput(
                    auditGroupId = params.auditGroupId,
                    status = params.status,
                    comment = params.comment,
                    auditedUserUid = params.auditedUserUid,
                )
            )
            Ok(Unit)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
