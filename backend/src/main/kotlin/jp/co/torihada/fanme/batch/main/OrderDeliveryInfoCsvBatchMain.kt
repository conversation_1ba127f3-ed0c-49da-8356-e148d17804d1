package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.batch.usecases.OrderDeliveryInfoCsvBatch
import org.jboss.logging.Logger

class OrderDeliveryInfoCsvBatchMain : QuarkusApplication {

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var orderDeliveryInfoCsvBatch: OrderDeliveryInfoCsvBatch

    companion object {
        private val DATE_FORMATTER: DateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE
    }

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("OrderDeliveryInfoCsvBatchMain start")

        logger.info("Args List is: ${args.toList()}")

        val targetDate =
            try {
                determineDates(args)
            } catch (e: IllegalArgumentException) {
                logger.error("Argument error: ${e.message}")
                return 1
            } catch (e: Exception) {
                logger.error("Unexpected error during argument parsing", e)
                return 1
            }

        try {
            orderDeliveryInfoCsvBatch.execute(targetDate)
            logger.info("OrderDeliveryInfoCsvBatchMain end")
            return 0
        } catch (e: Exception) {
            logger.error("OrderDeliveryInfoCsvBatchMain error", e)
            return 1
        }
    }

    private fun determineDates(args: Array<out String?>): LocalDate {
        // "OrderDeliveryInfoCsvBatchMain"は必ず1つ目の引数として渡されるため、2つ目の引数をチェック
        if (args.size < 2) {
            val today = LocalDate.now(ZoneId.of("Asia/Tokyo"))
            val yesterday = today.minusDays(1)
            return yesterday
        }
        require(args.size == 2) { "Exactly 1 arguments required: <targetDate>" }

        return validateAndParseDate(args[1])
    }

    private fun validateAndParseDate(targetDateStr: String?): LocalDate {
        require(targetDateStr != null) { "Target date must not be null" }
        try {
            val targetDate = LocalDate.parse(targetDateStr, DATE_FORMATTER)
            return targetDate
        } catch (e: Exception) {
            throw IllegalArgumentException("Dates must be in ISO format (yyyy-MM-dd)", e)
        }
    }
}
