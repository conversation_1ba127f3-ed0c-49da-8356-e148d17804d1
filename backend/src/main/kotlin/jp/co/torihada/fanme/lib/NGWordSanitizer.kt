package jp.co.torihada.fanme.lib

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.util.regex.Pattern
import org.jboss.logging.Logger

@ApplicationScoped
class NGWordSanitizer {
    @Inject private lateinit var logger: Logger

    private val NG_WORDS_REGEXP by lazy {
        try {
            val escapedWords =
                NGWordsConst.NG_WORDS.sortedByDescending { it.length }
                    .map { word -> Pattern.quote(word) }
            escapedWords.joinToString("|").toRegex(RegexOption.IGNORE_CASE)
        } catch (e: Exception) {
            logger.error("Failed to compile NG words regexp", e)
            "".toRegex()
        }
    }

    /**
     * 文字列内のNG用語をマスキング
     *
     * @param text マスキング対象の文字列
     * @return マスキング処理後の文字列
     */
    fun maskNGWords(text: String?): String? {
        if (text.isNullOrEmpty()) return text

        return try {
            if (!NG_WORDS_REGEXP.containsMatchIn(text)) return text
            NG_WORDS_REGEXP.replace(text) { "***" }
        } catch (e: Exception) {
            logger.error("Error masking NG words", e)
            text
        }
    }
}
