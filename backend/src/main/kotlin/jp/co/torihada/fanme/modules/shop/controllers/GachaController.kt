package jp.co.torihada.fanme.modules.shop.controllers

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jp.co.torihada.fanme.lib.MaskSerializer
import jp.co.torihada.fanme.modules.fanme.Config as FanmeConfig
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.Util.Companion.stringToInstantOnJST
import jp.co.torihada.fanme.modules.shop.controllers.requests.GachaItemRequest
import jp.co.torihada.fanme.modules.shop.models.AwardType
import jp.co.torihada.fanme.modules.shop.models.BadgeType
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.badge.CreateGachaCompleteBadge
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadge
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadgeRanking
import jp.co.torihada.fanme.modules.shop.usecases.benefitLog.CreateBenefitLog
import jp.co.torihada.fanme.modules.shop.usecases.gacha.CreateGachaItem
import jp.co.torihada.fanme.modules.shop.usecases.gacha.CreateGachaItem.AwardProbability
import jp.co.torihada.fanme.modules.shop.usecases.gacha.GetPullableGachaCount
import jp.co.torihada.fanme.modules.shop.usecases.gacha.GetPurchasedItemReceivedFiles
import jp.co.torihada.fanme.modules.shop.usecases.gacha.PullGachaItems
import jp.co.torihada.fanme.modules.shop.usecases.gacha.UpdateGachaItem

@ApplicationScoped
class GachaController {

    @Inject private lateinit var createGachaItem: CreateGachaItem
    @Inject private lateinit var updateGachaItem: UpdateGachaItem
    @Inject private lateinit var pullGachaItems: PullGachaItems

    @Inject private lateinit var getPullableGachaCount: GetPullableGachaCount
    @Inject private lateinit var createGachaCompleteBadge: CreateGachaCompleteBadge
    @Inject private lateinit var getGachaCompleteBadge: GetBadge
    @Inject private lateinit var createBenefitLog: CreateBenefitLog
    @Inject private lateinit var getGachaCompleteBadgeRanking: GetBadgeRanking
    @Inject private lateinit var userController: UserController
    @Inject private lateinit var fanmeConfig: FanmeConfig
    @Inject private lateinit var getPurchasedItemReceivedFilesUseCase: GetPurchasedItemReceivedFiles

    @Transactional
    fun createItem(@Valid item: GachaItemRequest.CreateItem): Item {
        return createGachaItem
            .execute(
                CreateGachaItem.Input(
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    isDuplicated = item.isDuplicated,
                    files = item.files.map { mapToCreateFile(it) },
                    samples = item.samples?.map { mapToCreateSampleFile(it) },
                    benefit =
                        item.benefits?.map {
                            CreateGachaItem.BenefitParam(
                                description = it.description,
                                conditionType = it.conditionType!!,
                                files = it.files.map { file -> mapToCreateBenefitFile(file) },
                            )
                        },
                    tags = item.tags,
                    itemOption =
                        CreateGachaItem.ItemOptionParam(
                            qtyTotal = item.itemOption.qtyTotal,
                            forSale =
                                CreateGachaItem.ForSale(
                                    startAt =
                                        if (item.itemOption.forSale?.startAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.startAt),
                                    endAt =
                                        if (item.itemOption.forSale?.endAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                ),
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    CreateGachaItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                    awardProbabilities =
                        item.awardProbabilities.map {
                            AwardProbability(
                                awardType = AwardType.fromValue(it.awardType),
                                probability = it.probability,
                            )
                        },
                    itemType = item.itemType,
                )
            )
            .getOrElse { throw it }
    }

    fun getPullableGachaCount(userId: String, itemId: Long): GetPullableGachaCount.Output {
        return getPullableGachaCount
            .execute(GetPullableGachaCount.Input(purchaserUserId = userId, itemId = itemId))
            .getOrElse { throw it }
    }

    @Transactional
    fun updateItem(@Valid item: GachaItemRequest.UpdateItem): Item {
        return updateGachaItem
            .execute(
                UpdateGachaItem.Input(
                    itemId = item.itemId,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    available = item.available,
                    price = item.price,
                    itemFiles = item.files?.map { mapToUpdateFile(it) },
                    itemOption =
                        UpdateGachaItem.ItemOptionParam(
                            password = item.itemOption?.password,
                            onSale =
                                if (item.itemOption?.onSale != null) {
                                    CreateGachaItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                            forSale =
                                if (item.itemOption?.forSale != null) {
                                    CreateGachaItem.ForSale(
                                        startAt =
                                            if (item.itemOption.forSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.forSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.forSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                    tags = item.tags,
                    sample = item.samples?.map { mapToUpdateSampleFile(it) },
                    benefits =
                        item.benefits?.map {
                            UpdateGachaItem.BenefitParam(
                                id = it.id!!,
                                description = it.description,
                                files = it.files.map { file -> mapToUpdateBenefitFile(file) },
                            )
                        },
                )
            )
            .getOrElse { throw it }
    }

    @Transactional
    fun pullGacha(
        itemId: Long,
        purchaserUid: String,
        pullCount: Int? = 0,
    ): List<PullGachaItems.FileForPullGachaItems> {

        val result =
            pullGachaItems
                .execute(
                    PullGachaItems.Input(
                        itemId = itemId,
                        purchaserUid = purchaserUid,
                        pullCount = pullCount,
                    )
                )
                .getOrElse { throw it }

        // 特典取得
        createBenefitLog.execute(
            CreateBenefitLog.Input(itemIds = listOf(itemId), purchaserUserId = purchaserUid)
        )

        // ガチャコンプリートバッジ付与
        createGachaCompleteBadge.execute(
            CreateGachaCompleteBadge.Input(userUid = purchaserUid, itemId = itemId)
        )

        return result
    }

    // ガチャコンプリートバッジを持っているか確認
    fun getGachaCompleteBadge(userUid: String, itemId: Long): GetBadge.Output {
        return getGachaCompleteBadge
            .execute(
                GetBadge.Input(
                    userUid = userUid,
                    itemId = itemId,
                    badgeType = BadgeType.DIGITAL_GACHA_COMPLETE, // 既存のbadgeTypeを継続使用
                )
            )
            .getOrElse { throw it }
    }

    data class GetGachaCompleteBadgeRankingResponse(
        val userUid: String,
        val userAccountIdentity: String,
        @JsonSerialize(using = MaskSerializer::class) val userName: String,
        val userIcon: String,
        val getBadgeAt: String,
    )

    fun getGachaCompleteBadgeRanking(itemId: Long): List<GetGachaCompleteBadgeRankingResponse> {
        val badgeRanking =
            getGachaCompleteBadgeRanking
                .execute(
                    GetBadgeRanking.Input(
                        itemId = itemId,
                        badgeType = BadgeType.DIGITAL_GACHA_COMPLETE, // 既存のbadgeTypeを継続使用
                        limit = 100, // 上位100件を取得
                    )
                )
                .getOrElse { throw it }

        val userUids = badgeRanking.map { it.userUid }.distinct()
        // ユーザー情報を取得して Map 化（uid をキーに）
        val users = userController.getUsers(userUids).associateBy { it.uid }

        return badgeRanking.map { ranking ->
            val user = users[ranking.userUid]
            val response =
                GetGachaCompleteBadgeRankingResponse(
                    userUid = ranking.userUid,
                    userAccountIdentity = user?.accountIdentity ?: UNKNOWN_USER_ACCOUNT_IDENTITY,
                    userName = user?.name ?: UNKNOWN_USER_NAME,
                    userIcon =
                        user?.iconUrl
                            ?: "${fanmeConfig.fanmeApiServerUrl()}/${Const.FANME_DEFAULT_CREATOR_ICON_PATH}",
                    getBadgeAt = ranking.createdAt.toString(),
                )
            response
        }
    }

    private fun mapToCreateFile(file: GachaItemRequest.File): CreateGachaItem.File {
        return CreateGachaItem.File(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            maskedThumbnailUri = file.maskedThumbnailUri,
            watermarkThumbnailUri = file.watermarkThumbnailUri,
            price = file.price,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
            itemThumbnailSelected = file.itemThumbnailSelected ?: false,
            sortOrder = file.sortOrder ?: 0,
            awardType = AwardType.fromValue(file.awardType),
            isSecret = file.isSecret,
        )
    }

    private fun mapToCreateSampleFile(
        file: GachaItemRequest.SampleFile
    ): CreateGachaItem.SampleFile {
        return CreateGachaItem.SampleFile(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToCreateBenefitFile(
        file: GachaItemRequest.BenefitFile
    ): CreateGachaItem.BenefitFile {
        return CreateGachaItem.BenefitFile(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToUpdateFile(file: GachaItemRequest.UpdateFile): UpdateGachaItem.File {
        return UpdateGachaItem.File(id = file.id, name = file.name, isSecret = file.isSecret)
    }

    private fun mapToUpdateSampleFile(
        file: GachaItemRequest.SampleFile
    ): UpdateGachaItem.SampleFile {
        return UpdateGachaItem.SampleFile(
            id = file.id,
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToUpdateBenefitFile(
        file: GachaItemRequest.BenefitFile
    ): UpdateGachaItem.BenefitFile {
        return UpdateGachaItem.BenefitFile(
            id = file.id,
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    fun getPurchasedItemReceivedFiles(
        userId: String,
        purchasedItemId: Long,
    ): List<GetPurchasedItemReceivedFiles.PurchasedItemReceivedFile> {
        return getPurchasedItemReceivedFilesUseCase
            .execute(GetPurchasedItemReceivedFiles.Input(userId, purchasedItemId))
            .getOrElse { throw it }
            .files
    }

    companion object {
        private const val UNKNOWN_USER_ACCOUNT_IDENTITY = ""
        private const val UNKNOWN_USER_NAME = "不明なユーザー"
    }
}
