package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.services.ItemCostService

@ApplicationScoped
class GetItemCost {
    @Inject private lateinit var itemCostService: ItemCostService

    data class Cost(val cost: Int)

    fun execute(itemType: Int): Result<Cost, FanmeException> {
        val cost = itemCostService.getItemCost(itemType)
        val output = Cost(cost = cost)
        return Ok(output)
    }
}
