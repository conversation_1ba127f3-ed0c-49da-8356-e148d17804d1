package jp.co.torihada.fanme.modules.payment.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.controllers.responses.SellerAccountBalanceResponse
import jp.co.torihada.fanme.modules.payment.usecases.balance.GetSellerAccountBalances

@ApplicationScoped
class SellerAccountBalanceController {
    @Inject private lateinit var getSellerAccountBalances: GetSellerAccountBalances

    fun getSellerAccountBalances(
        sellerUserIds: List<String>
    ): SellerAccountBalanceResponse.GetBalances {
        val response =
            getSellerAccountBalances
                .execute(GetSellerAccountBalances.Input(sellerUserIds))
                .getOrThrow()

        return SellerAccountBalanceResponse.GetBalances(
            balances =
                response.balances.map { balance ->
                    SellerAccountBalanceResponse.Balance(
                        sellerUserId = balance.sellerUserId,
                        accumulatedSales = balance.accumulatedSales,
                        withdrawableAmount = balance.amount,
                    )
                }
        )
    }
}
