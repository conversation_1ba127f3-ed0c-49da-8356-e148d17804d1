package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jp.co.torihada.fanme.modules.fanme.models.UserState
import jp.co.torihada.fanme.modules.fanme.usecases.state.CreateUserState
import jp.co.torihada.fanme.modules.fanme.usecases.state.GetUserState

@ApplicationScoped
class UserStateController {
    companion object {
        /**
         * UserStateのキーとして許可されるパターン
         * - visit:xxx.123
         * - doc.xxx-yyy:123
         * - doc.xxx-yyy:123.ext
         */
        const val KEY_PATTERN = "^(visit:([^\\.]+).(\\d)+|doc\\.[a-z0-9-]+:\\d+(\\.[a-z]+)?)$"
    }

    @Inject private lateinit var getUserState: GetUserState
    @Inject private lateinit var createUserState: CreateUserState

    fun getUserState(
        @NotNull userUuid: String,
        @NotNull @Pattern(regexp = KEY_PATTERN, message = "Invalid key format") key: String,
    ): UserState? {
        return getUserState.execute(GetUserState.Input(userUuid, key)).getOrElse { throw it }
    }

    @Transactional
    fun createUserState(
        @NotNull userUuid: String,
        @NotNull @Pattern(regexp = KEY_PATTERN, message = "Invalid key format") key: String,
        value: String,
    ): UserState {
        return createUserState.execute(CreateUserState.Input(userUuid, key, value)).getOrElse {
            throw it
        }
    }
}
