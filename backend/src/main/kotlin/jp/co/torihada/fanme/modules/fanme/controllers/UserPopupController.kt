package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.unwrap
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserPopup
import jp.co.torihada.fanme.modules.fanme.usecases.userPopup.GetUserPopup
import jp.co.torihada.fanme.modules.fanme.usecases.userPopup.UpsertUserPopup

@ApplicationScoped
class UserPopupController {
    @Inject private lateinit var getUserPopup: GetUserPopup
    @Inject private lateinit var upsertUserPopup: UpsertUserPopup

    fun getUserPopup(user: User): UserPopup? {
        return getUserPopup.execute(GetUserPopup.Input(userId = user.id!!)).unwrap()
    }

    data class SaveUserPopupRequest(
        val user: User,
        val enable: Boolean,
        val url: String,
        val title: String?,
        val buttonText: String?,
        val image: String?,
    )

    @Transactional
    fun saveUserPopup(request: SaveUserPopupRequest): UserPopup {
        val input =
            UpsertUserPopup.Input(
                user = request.user,
                enable = request.enable,
                title = request.title,
                url = request.url,
                buttonText = request.buttonText,
                image = request.image,
            )
        return upsertUserPopup.execute(input).unwrap()
    }
}
