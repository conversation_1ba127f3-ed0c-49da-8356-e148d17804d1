package jp.co.torihada.fanme.modules.fanme.usecases.userTutorial

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserTutorial as UserTutorialModel

@ApplicationScoped
class UpsertUserTutorial {
    data class Input(val userUuid: String, val flagKey: String, val displayFlg: Boolean)

    fun execute(input: Input): Result<UserTutorialModel, Exception> {
        val user = User.findByUuid(input.userUuid) ?: throw ResourceNotFoundException("User")

        val result =
            UserTutorialModel.upsert(
                user = user,
                name = input.flagKey,
                displayFlg = input.displayFlg,
            )

        return Ok(result)
    }
}
