package jp.co.torihada.fanme.endpoints.fanme

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.ProfileController
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover
import jp.co.torihada.fanme.modules.fanme.models.ProfileCoverImage
import jp.co.torihada.fanme.modules.fanme.usecases.profile.GetProfile
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/profiles")
@Tag(name = "FANME", description = "FANME APIサーバー")
class ProfileEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var util: Util
    @Inject private lateinit var handler: ProfileController

    data class GetProfileResponse(
        override val data: GetProfileData,
        override val errors: List<ErrorObject>,
    ) : BaseResponseBody<GetProfileResponse.GetProfileData>(data = data, errors = errors) {
        data class GetProfileData(val profile: GetProfile.Profile)
    }

    @GET
    @Path("/current")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun getCurrentProfile(): GetProfileResponse {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val profile = handler.getUserProfile(userUuid)
        return GetProfileResponse(
            data = GetProfileResponse.GetProfileData(profile = profile),
            errors = emptyList(),
        )
    }

    @GET
    @Path("/{creator_account_identity}")
    @Produces(MediaType.APPLICATION_JSON)
    fun getProfile(
        @PathParam("creator_account_identity") creatorAccountIdentity: String
    ): GetProfileResponse {
        val userUid = util.getCreatorUid(requestContext)
        val profile = handler.getUserProfile(userUid)
        return GetProfileResponse(
            data = GetProfileResponse.GetProfileData(profile = profile),
            errors = emptyList(),
        )
    }

    data class UpdateProfileRequest(
        val bio: String? = null,
        val headerImage: String? = null,
        val snsLinkColor: String? = null,
    )

    data class UpdateProfileResponse(
        override val data: UpdateProfileData,
        override val errors: List<ErrorObject>,
    ) : BaseResponseBody<UpdateProfileResponse.UpdateProfileData>(data = data, errors = errors) {
        data class UpdateProfileData(val profile: Profile)
    }

    @PUT
    @Path("/current")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateProfile(requestBody: UpdateProfileRequest): UpdateProfileResponse {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val profile =
            handler.updateUserProfile(
                userUuid = userUuid,
                bio = requestBody.bio,
                headerImage = requestBody.headerImage,
                snsLinkColor = requestBody.snsLinkColor,
            )
        return UpdateProfileResponse(
            data = UpdateProfileResponse.UpdateProfileData(profile = profile),
            errors = emptyList(),
        )
    }

    data class UpdateProfileCoverRequest(
        val coverImageVisibility: Boolean? = null,
        val brightness: String? = null,
    )

    data class UpdateProfileCoverResponse(
        override val data: UpdateProfileCoverData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<UpdateProfileCoverResponse.UpdateProfileCoverData>(
            data = data,
            errors = errors,
        ) {
        data class UpdateProfileCoverData(val profileCover: ProfileCover)
    }

    @PUT
    @Path("/current/cover")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateProfileCover(requestBody: UpdateProfileCoverRequest): UpdateProfileCoverResponse {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val profileCover =
            handler.updateUserProfileCover(
                userUuid = userUuid,
                coverImageVisibility = requestBody.coverImageVisibility,
                brightness = requestBody.brightness,
            )
        return UpdateProfileCoverResponse(
            data = UpdateProfileCoverResponse.UpdateProfileCoverData(profileCover = profileCover),
            errors = emptyList(),
        )
    }

    data class UpdateProfileCoverImageRequest(val coverImage: String, val resourceType: String)

    data class UpdateProfileCoverImageResponse(
        override val data: UpdateProfileCoverImageData,
        override val errors: List<ErrorObject>,
    ) :
        BaseResponseBody<UpdateProfileCoverImageResponse.UpdateProfileCoverImageData>(
            data = data,
            errors = errors,
        ) {
        data class UpdateProfileCoverImageData(val profileCoverImage: ProfileCoverImage)
    }

    @PUT
    @Path("/current/cover/image")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateProfileCoverImage(
        requestBody: UpdateProfileCoverImageRequest
    ): UpdateProfileCoverImageResponse {
        val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val profileCoverImage =
            handler.updateUserProfileCoverImage(
                userUuid = userUuid,
                coverImage = requestBody.coverImage,
                resourceType = requestBody.resourceType,
            )
        return UpdateProfileCoverImageResponse(
            data =
                UpdateProfileCoverImageResponse.UpdateProfileCoverImageData(
                    profileCoverImage = profileCoverImage
                ),
            errors = emptyList(),
        )
    }
}
