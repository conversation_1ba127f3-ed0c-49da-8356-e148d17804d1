package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.ResourceAlreadyExistsException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class UpdateUser {

    data class Input(
        val userUuid: String,
        val icon: String? = null,
        val name: String? = null,
        val gender: String? = null,
        val birthday: Instant? = null,
        val purpose: Int? = null,
        val accountIdentity: String? = null,
    )

    fun execute(input: Input): Result<User, Exception> {
        val user = User.findByUuid(input.userUuid) ?: return Err(ResourceNotFoundException("User"))

        if (input.accountIdentity != null) {
            val existedUser = User.findByAccountIdentity(input.accountIdentity)
            if (existedUser != null && existedUser.uid != input.userUuid) {
                return Err(ResourceAlreadyExistsException("Account identity"))
            }
        }

        val result =
            User.update(
                user = user,
                icon = input.icon,
                name = input.name,
                gender = input.gender,
                birthday = input.birthday,
                purpose = input.purpose,
                accountIdentity = input.accountIdentity,
            )

        return Ok(result)
    }
}
