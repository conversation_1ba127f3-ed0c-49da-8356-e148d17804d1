package jp.co.torihada.fanme.modules.console.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.quarkus.hibernate.orm.panache.kotlin.PanacheEntityBase
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const.DATE_RESPONSE_FORMAT
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_LOCAL_TIME_ZONE

@MappedSuperclass
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
abstract class BaseModel : PanacheEntityBase {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY) open var id: Long? = null

    @NotNull
    @Column(name = "created_at", nullable = false)
    @JsonFormat(pattern = DATE_RESPONSE_FORMAT, timezone = DEFAULT_LOCAL_TIME_ZONE)
    open var createdAt: Instant? = null

    @NotNull
    @Column(name = "updated_at", nullable = false)
    @JsonFormat(pattern = DATE_RESPONSE_FORMAT, timezone = DEFAULT_LOCAL_TIME_ZONE)
    open var updatedAt: Instant? = null

    @PrePersist
    fun prePersist() {
        val now = Instant.now()
        if (createdAt == null) {
            createdAt = now
        }
        if (updatedAt == null) {
            updatedAt = now
        }
    }

    @PreUpdate
    fun preUpdate() {
        updatedAt = Instant.now()
    }
}
