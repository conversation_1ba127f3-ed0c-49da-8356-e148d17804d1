package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.endpoints.console.CommissionControlEndpoint.ItemMarginRateItem
import jp.co.torihada.fanme.endpoints.console.request.ShopItemTypeMarginRateParam
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.UpdateItemMarginRates
import jp.co.torihada.fanme.modules.console.usecases.UpdateItemMarginRates.ItemMarginRateResult
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopItemTypeMarginRate
import jp.co.torihada.fanme.modules.console.usecases.UpdateShopItemTypeMarginRate.ShopItemTypeMarginRateResult

@ApplicationScoped
class CommissionControlController {
    @Inject lateinit var updateShopItemTypeMarginRate: UpdateShopItemTypeMarginRate
    @Inject lateinit var updateItemMarginRatesUsecase: UpdateItemMarginRates

    @Transactional
    fun updateShopItemTypeMarginRates(
        accountIdentity: String,
        shopId: Long,
        marginRates: List<ShopItemTypeMarginRateParam>,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): List<ShopItemTypeMarginRateResult> =
        updateShopItemTypeMarginRate
            .execute(
                UpdateShopItemTypeMarginRate.Input(
                    accountIdentity = accountIdentity,
                    shopId = shopId,
                    marginRates = marginRates,
                    currentUserUid = currentUserUid,
                    currentUserRole = currentUserRole,
                )
            )
            .getOrThrow()

    @Transactional
    fun updateItemMarginRates(
        accountIdentity: String,
        items: List<ItemMarginRateItem>,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): List<ItemMarginRateResult> =
        updateItemMarginRatesUsecase
            .execute(
                UpdateItemMarginRates.Input(
                    accountIdentity = accountIdentity,
                    items = items,
                    currentUserUid = currentUserUid,
                    currentUserRole = currentUserRole,
                )
            )
            .getOrThrow()
}
