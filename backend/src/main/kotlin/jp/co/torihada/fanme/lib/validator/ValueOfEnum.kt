package jp.co.torihada.fanme.lib.validator

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [ValueOfEnumValidator::class])
annotation class ValueOfEnum(
    val enumClass: KClass<out Enum<*>>,
    val message: String = "must be any of enum {enumClass}",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class ValueOfEnumValidator : ConstraintValidator<ValueOfEnum, Any> {
    private lateinit var enumValues: Array<out Enum<*>>

    override fun initialize(annotation: ValueOfEnum) {
        enumValues = annotation.enumClass.java.enumConstants
    }

    override fun isValid(value: Any?, context: ConstraintValidatorContext): Boolean {
        if (value == null) {
            return true
        }

        return when (value) {
            is String -> enumValues.any { it.name == value }
            is Int ->
                enumValues.any {
                    try {
                        val valueField = it.javaClass.getDeclaredField("value")
                        valueField.isAccessible = true
                        val enumValue = valueField.get(it)
                        enumValue == value
                    } catch (e: Exception) {
                        false
                    }
                }
            else -> false
        }
    }
}
