package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.GetUserProfile
import jp.co.torihada.fanme.modules.console.usecases.GetUserProfileInput
import jp.co.torihada.fanme.modules.console.usecases.UserProfileResponse

@ApplicationScoped
class UserProfileController {

    @Inject lateinit var getUserProfile: GetUserProfile

    fun getUserProfile(
        accountIdentity: String,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): UserProfileResponse {
        val input =
            GetUserProfileInput(
                accountIdentity = accountIdentity,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )

        return getUserProfile.execute(input).getOrThrow()
    }
}
