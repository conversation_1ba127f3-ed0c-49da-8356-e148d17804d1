package jp.co.torihada.fanme.modules.fanme.usecases.state

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserState

@ApplicationScoped
class CreateUserState {

    class Input(val userUuid: String, val key: String, val value: String)

    fun execute(input: Input): Result<UserState, Exception> {
        val user = User.findByUuid(input.userUuid) ?: return Err(ResourceNotFoundException("User"))
        val existingState = UserState.findByUserIdAndKey(user.id!!, input.key)

        val result =
            if (existingState != null) {
                UserState.update(userState = existingState, value = input.value)
            } else {
                UserState.create(user = user, key = input.key, value = input.value)
            }

        return Ok(result)
    }
}
// NOTE: keyがDBの予約語として扱われエラーになるためテストは一旦書かない
