package jp.co.torihada.fanme.modules.fanme.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue

/** JSON処理のためのユーティリティクラス */
object JsonUtil {
    /** 共有のJacksonオブジェクトマッパー */
    val mapper: ObjectMapper by lazy { jacksonObjectMapper() }

    /**
     * JSON文字列をクリーンアップして安全に処理できる形式にする
     *
     * @param json クリーンアップするJSON文字列
     * @return クリーンアップされたJSON文字列
     */
    fun cleanJsonString(json: String): String {
        return json
            .trim()
            .replace(Regex("^\""), "")
            .replace(Regex("\"$"), "")
            .replace("\\\"", "\"")
            .replace("\\\\", "\\")
    }

    /**
     * JSON文字列をMapにパースする
     *
     * @param json パースするJSON文字列
     * @return パース結果のMapオブジェクト、エラー時は元の文字列
     */
    fun parseJsonToMap(json: String?): Any? {
        if (json == null) return null

        return try {
            val cleanedJson = cleanJsonString(json)
            mapper.readValue<Map<String, Any?>>(cleanedJson)
        } catch (e: Exception) {
            json
        }
    }

    /**
     * JSON文字列を指定した型のオブジェクトにパースする
     *
     * @param json パースするJSON文字列
     * @return 指定した型のオブジェクト、エラー時はnull
     */
    inline fun <reified T> parseJson(json: String?): T? {
        if (json == null) return null

        return try {
            val cleanedJson = cleanJsonString(json)
            mapper.readValue<T>(cleanedJson)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * オブジェクトをJSON文字列にシリアライズする
     *
     * @param obj シリアライズするオブジェクト
     * @return JSON文字列
     */
    fun toJsonString(obj: Any?): String? {
        if (obj == null) return null

        return try {
            mapper.writeValueAsString(obj)
        } catch (e: Exception) {
            "{}"
        }
    }
}
