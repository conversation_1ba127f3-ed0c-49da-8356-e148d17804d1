package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.requests.FanmeCustomerRequest
import jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer.FanmeCustomerEntity
import jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer.IFanmeCustomerService

@ApplicationScoped
class FanmeCustomerController : BaseController() {

    @Inject private lateinit var fanmeCustomerService: IFanmeCustomerService

    fun getFanmeCustomer(creatorUid: String): FanmeCustomerEntity? {
        val fanmeCustomer = fanmeCustomerService.get(creatorUid)
        return fanmeCustomer
    }

    fun listFanmeCustomers(creatorUids: List<String>): List<FanmeCustomerEntity> {
        if (creatorUids.isEmpty()) return emptyList()
        return fanmeCustomerService.list(creatorUids)
    }

    fun saveFanmeCustomer(request: FanmeCustomerRequest.SaveFanmeCustomer) {
        val isAlreadyExist = fanmeCustomerService.get(request.creatorUid) != null
        val newEntity =
            FanmeCustomerEntity(
                creatorUid = if (!isAlreadyExist) request.creatorUid else null,
                firstName = request.firstName,
                lastName = request.lastName,
                firstNameKana = request.firstNameKana,
                lastNameKana = request.lastNameKana,
                postalCode = request.postalCode,
                prefecture = request.prefecture,
                city = request.city,
                street = request.street,
                building = request.building,
                phoneNumber = request.phoneNumber,
            )

        if (isAlreadyExist) {
            fanmeCustomerService.update(request.creatorUid, newEntity)
        } else {
            fanmeCustomerService.create(newEntity)
        }
    }
}
