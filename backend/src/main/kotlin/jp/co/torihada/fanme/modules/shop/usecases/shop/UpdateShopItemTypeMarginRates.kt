package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.models.ShopItemTypeMarginRate

@ApplicationScoped
class UpdateShopItemTypeMarginRates {

    data class Input(val shopId: Long, val items: List<Pair<ItemType, Float>>)

    fun execute(params: Input): Result<List<ShopItemTypeMarginRate>, FanmeException> {
        return try {
            val shop = Shop.findById(params.shopId) ?: throw ResourceNotFoundException("Shop")

            val updated =
                params.items.map { (itemType, rate) ->
                    val record =
                        ShopItemTypeMarginRate.find(params.shopId, itemType)
                            ?: ShopItemTypeMarginRate.create(shop, itemType, rate)

                    record.marginRate = rate
                    record.persist()
                    record
                }
            Ok(updated)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(code = 0, message = e.message ?: "Unknown error"))
        }
    }
}
