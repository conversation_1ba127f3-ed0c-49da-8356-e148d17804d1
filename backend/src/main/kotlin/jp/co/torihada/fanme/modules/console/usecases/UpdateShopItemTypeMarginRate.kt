package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.console.request.ShopItemTypeMarginRateParam
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.MarginRateController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController

@ApplicationScoped
class UpdateShopItemTypeMarginRate
@Inject
constructor(
    private val userController: UserController,
    private val shopController: ShopController,
    private val marginRateController: MarginRateController,
) {

    data class Input(
        val accountIdentity: String,
        val shopId: Long,
        val marginRates: List<ShopItemTypeMarginRateParam>,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    data class ShopItemTypeMarginRateResult(
        val shopId: Long,
        val itemType: String,
        val marginRate: Float,
    )

    fun execute(params: Input): Result<List<ShopItemTypeMarginRateResult>, ConsoleException> {
        return try {
            val user =
                userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                    ?: throw ResourceNotFoundException("User")

            val shopDto = shopController.getShop(user.uid!!)
            if (shopDto.id != params.shopId) throw ResourceNotFoundException("Shop")

            val updatedRecords =
                marginRateController.updateShopItemTypeMarginRates(
                    params.shopId,
                    params.marginRates.map { it.itemType to it.marginRate },
                )

            Ok(
                updatedRecords.map {
                    ShopItemTypeMarginRateResult(it.shop.id!!, it.itemType.name, it.marginRate)
                }
            )
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: FanmeException) {
            Err(ConsoleException(0, e.message ?: "FanmeException"))
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }
}
