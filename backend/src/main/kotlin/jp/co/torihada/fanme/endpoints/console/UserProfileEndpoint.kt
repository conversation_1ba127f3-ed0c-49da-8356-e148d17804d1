package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.UserProfileController
import jp.co.torihada.fanme.modules.console.usecases.UserProfileResponse
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/users/{account-identity}/profile")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class UserProfileEndpoint {

    @Inject lateinit var handler: UserProfileController

    @Inject lateinit var util: Util

    @Inject lateinit var securityIdentity: SecurityIdentity

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getProfile(
        @PathParam("account-identity") accountIdentity: String
    ): BaseResponseBody<UserProfileResponse> {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)

        if (currentUserUid == null || currentUserRole == null) {
            throw IllegalStateException("User authentication information is missing")
        }

        try {
            val result = handler.getUserProfile(accountIdentity, currentUserUid, currentUserRole)
            return BaseResponseBody(data = result, errors = emptyList())
        } catch (e: Exception) {
            throw e
        }
    }
}
