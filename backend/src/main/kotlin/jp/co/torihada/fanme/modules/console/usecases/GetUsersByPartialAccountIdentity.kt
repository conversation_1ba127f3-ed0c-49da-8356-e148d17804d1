package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetUsersByPartialAccountIdentity
@Inject
constructor(private val userController: UserController) {
    data class Input(val partialAccountIdentity: String)

    fun execute(input: Input): Result<List<User>, ConsoleException> {
        val result = userController.getUsersByPartialAccountIdentity(input.partialAccountIdentity)
        return Ok(result)
    }
}
