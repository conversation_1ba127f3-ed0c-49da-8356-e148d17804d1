package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.console.usecases.CreateProxyAccessToken

@ApplicationScoped
class ProxyAccessTokenController {
    @Inject lateinit var createProxyAccessToken: CreateProxyAccessToken

    @Transactional
    fun createProxyAccessToken(
        targetUserAccountIdentity: String,
        currentUserRole: String?,
        currentUserUid: String?,
    ): CreateProxyAccessToken.CreateProxyAccessTokenResponse {
        val input =
            CreateProxyAccessToken.Input(
                targetUserAccountIdentity = targetUserAccountIdentity,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid ?: throw IllegalStateException("User UID not found"),
            )

        return createProxyAccessToken.execute(input).getOrThrow()
    }
}
