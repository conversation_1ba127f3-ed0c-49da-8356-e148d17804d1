package jp.co.torihada.fanme.modules.fanme.externals.client.kawa

import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.fanme.externals.entity.kawa.SuggestAddressResponse
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

@ApplicationScoped
@RegisterRestClient(configKey = "suggest-address")
interface SuggestAddressClient {

    @GET
    @Path("/api/address/suggestion/jpn/postal-code/{postalCode}")
    @Consumes(MediaType.APPLICATION_JSON)
    fun suggestAddress(
        @HeaderParam("Authorization") authorization: String,
        @PathParam("postalCode") postalCode: String,
    ): SuggestAddressResponse?
}
