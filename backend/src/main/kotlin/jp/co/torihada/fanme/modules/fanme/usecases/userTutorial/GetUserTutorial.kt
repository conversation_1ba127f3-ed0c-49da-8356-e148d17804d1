package jp.co.torihada.fanme.modules.fanme.usecases.userTutorial

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserTutorial as UserTutorialModel
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class GetUserTutorial {
    data class Input(val userUuid: String, val flagKey: String)

    @Schema(name = "GetUserTutorial_UserTutorial")
    data class UserTutorial(val userUuid: String, val name: String, val displayFlg: Boolean)

    fun execute(input: Input): Result<UserTutorialModel?, Exception> {
        val user = User.findByUuid(input.userUuid) ?: throw ResourceNotFoundException("User")

        val userTutorial = UserTutorialModel.findByUserAndName(user = user, name = input.flagKey)

        return Ok(userTutorial)
    }
}
