package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.AgenciesResponseBody
import jp.co.torihada.fanme.dto.AgencySalesResponseBody
import jp.co.torihada.fanme.dto.UsersResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.HttpStatus
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AgencyController
import jp.co.torihada.fanme.odata.OData
import kotlin.reflect.full.findAnnotation
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/agencies")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AgencyEndpoint {

    @Inject private lateinit var handler: AgencyController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var util: Util

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(AgenciesResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    @Produces(MediaType.APPLICATION_JSON)
    fun getAgencies(): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val result = handler.getAgencies(currentUserUid, currentUserRole, odata)
            val entity = ResponseEntity(result, "agencies")
            Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            Response.status(status).entity(e).build()
        }
    }

    @GET
    @Path("/{agency-id}/users")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(
        value = UsersResponseBody::class,
        responseCode = "200",
        responseDescription = "成功",
    )
    @APIResponse(responseCode = "400", description = "Bad Request")
    @APIResponse(responseCode = "401", description = "Unauthorized")
    @APIResponse(responseCode = "403", description = "Forbidden")
    @APIResponse(responseCode = "404", description = "Not Found")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getAgencyUsers(@PathParam("agency-id") agencyId: Long): Response {
        try {
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val users = handler.getAgencyUsers(agencyId, currentUserUid, currentUserRole)
            val entity = ResponseEntity(users, "users")
            return Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            return Response.status(status).entity(e).build()
        }
    }

    @GET
    @Path("/{agency-id}/sales")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(
        value = AgencySalesResponseBody::class,
        responseCode = "200",
        responseDescription = "成功",
    )
    @APIResponse(responseCode = "400", description = "Bad Request")
    @APIResponse(responseCode = "401", description = "Unauthorized")
    @APIResponse(responseCode = "403", description = "Forbidden")
    @APIResponse(responseCode = "404", description = "Not Found")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getAgencySales(@PathParam("agency-id") agencyId: Long): Response {

        try {
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val agencySales = handler.getAgencySales(agencyId, currentUserUid, currentUserRole)

            val entity = ResponseEntity(agencySales, "agencySales")
            return Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            return Response.status(status).entity(e).build()
        }
    }

    @GET
    @Path("/{agency-id}/sales/monthly")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Produces(MediaType.APPLICATION_JSON)
    fun getAgencyMonthlySales(
        @PathParam("agency-id") agencyId: Long,
        @QueryParam("from") from: String?,
        @QueryParam("to") to: String?,
    ): Response {
        try {
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val odata = requestContext.getProperty("odata") as OData?
            val result =
                handler.getAgencyMonthlySales(
                    agencyId,
                    currentUserUid,
                    currentUserRole,
                    from,
                    to,
                    odata,
                )

            val agencySales = mapOf("monthly_sales_list" to result.monthlySalesList)
            val metadata = result.totalCount?.let { mapOf("@odata.count" to it) }
            val entity = ResponseEntity(agencySales, "agency_sales", metadata)

            return Response.ok(entity).build()
        } catch (e: FanmeException) {
            val status =
                e::class.findAnnotation<HttpStatus>()?.status
                    ?: Response.Status.INTERNAL_SERVER_ERROR
            return Response.status(status).entity(e).build()
        }
    }
}
