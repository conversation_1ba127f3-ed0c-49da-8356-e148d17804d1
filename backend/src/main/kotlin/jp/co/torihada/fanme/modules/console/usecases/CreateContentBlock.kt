package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock

@ApplicationScoped
class CreateContentBlock {
    @Inject private lateinit var userController: UserController
    @Inject private lateinit var contentBlockController: ContentBlockController
    @Inject private lateinit var securityUtils: SecurityUtils

    data class CreateContentBlockRequest(
        val contentBlockType: ContentBlockType,
        val title: String?,
        val description: String?,
        val appDescription: String?,
        val url: String?,
        val iconUrl: String?,
    )

    fun execute(
        accountIdentity: String,
        request: CreateContentBlockRequest,
        currentUserRole: String?,
        currentUserUid: String?,
    ): Result<ContentBlock, FanmeException> {
        return try {
            val user =
                userController.getUserByAccountIdentity(accountIdentity)
                    ?: throw ResourceNotFoundException(
                        "User not found for account identity: $accountIdentity"
                    )

            securityUtils.validateAgentForUserAccess(user, currentUserRole, currentUserUid)

            val createRequest =
                ContentBlockController.CreateContentBlockWithDetailRequest(
                    creatorUid = user.uid!!,
                    contentBlockType = request.contentBlockType,
                    title = request.title,
                    description = request.description,
                    appDescription = request.appDescription,
                    url = request.url,
                    iconUrl = request.iconUrl,
                )

            val contentBlock = contentBlockController.createContentBlockWithDetail(createRequest)
            Ok(contentBlock)
        } catch (e: FanmeException) {
            Err(e)
        }
    }
}
