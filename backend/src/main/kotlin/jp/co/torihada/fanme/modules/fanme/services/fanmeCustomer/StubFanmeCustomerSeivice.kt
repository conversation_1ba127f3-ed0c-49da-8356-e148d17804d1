package jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer

import io.quarkus.arc.profile.IfBuildProfile
import jakarta.enterprise.context.ApplicationScoped

@IfBuildProfile(anyOf = ["local", "test"])
@ApplicationScoped
class StubFanmeCustomerService : IFanmeCustomerService {
    override fun get(creatorUid: String): FanmeCustomerEntity? {
        println("StubFanmeCustomerService creatorUid: $creatorUid")
        return FanmeCustomerEntity(
            creatorUid = creatorUid,
            firstName = "太郎",
            lastName = "山田",
            firstNameKana = "タロウ",
            lastNameKana = "ヤマダ",
            postalCode = "1500043",
            prefecture = "東京都",
            city = "渋谷区",
            street = "道玄坂",
            building = "スタブビル 101",
            phoneNumber = "09012345678",
        )
    }

    override fun list(creatorUids: List<String>): List<FanmeCustomerEntity> {
        println("StubFanmeCustomerService creatorUids: $creatorUids")
        return creatorUids.map { uid ->
            FanmeCustomerEntity(
                creatorUid = uid,
                firstName = "太郎",
                lastName = "山田",
                firstNameKana = "タロウ",
                lastNameKana = "ヤマダ",
                postalCode = "1500043",
                prefecture = "東京都",
                city = "渋谷区",
                street = "道玄坂",
                building = "スタブビル 101",
                phoneNumber = "09012345678",
            )
        }
    }

    override fun create(entity: FanmeCustomerEntity) {
        println("【Stub】create called with: $entity")
    }

    override fun update(creatorUid: String, entity: FanmeCustomerEntity) {
        println("【Stub】update called with: $entity")
    }
}
