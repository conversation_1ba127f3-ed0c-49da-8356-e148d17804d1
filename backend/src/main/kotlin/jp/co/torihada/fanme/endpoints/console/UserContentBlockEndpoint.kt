package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import io.vertx.ext.web.FileUpload
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.UserContentBlockController
import jp.co.torihada.fanme.modules.console.usecases.CreateContentBlock
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_DESCRIPTION_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_TITLE_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import org.hibernate.validator.constraints.URL

@Path("/console/users/{account-identity}/content-blocks")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class UserContentBlockEndpoint {
    @Inject lateinit var userContentBlockController: UserContentBlockController

    @Inject lateinit var securityIdentity: SecurityIdentity

    @Inject lateinit var util: Util

    data class ContentBlocks(val contentBlocks: List<GetContentBlocks.ContentBlock>)

    data class CreateContentWithDetailRequest(
        @field:NotNull val contentBlockType: Long,
        @field:Size(max = CONTENT_TITLE_MAX_LENGTH) val title: String?,
        @field:Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val description: String?,
        @field:Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val appDescription: String?,
        @field:URL val url: String?,
        @field:URL val iconUrl: String?,
    )

    data class ContentBlockResponse(val contentBlock: ContentBlock)

    data class UpdateContentBlockDetailRequest(
        @field:NotNull val contentBlockDetailId: Long,
        @field:Size(max = CONTENT_TITLE_MAX_LENGTH) val title: String?,
        @field:Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val description: String?,
        @field:Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val appDescription: String?,
        @field:URL val url: String?,
        @field:URL val iconUrl: String?,
        val fileUpload: FileUpload? = null,
    )

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun getUserContentBlocks(
        @PathParam("account-identity") accountIdentity: String
    ): BaseResponseBody<ContentBlocks> {

        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)?.name

        val contentBlocks =
            userContentBlockController.getUserContentBlocks(
                accountIdentity = accountIdentity,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid,
            )

        return BaseResponseBody(
            data = ContentBlocks(contentBlocks = contentBlocks),
            errors = emptyList(),
        )
    }

    @POST
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun createContentBlock(
        @PathParam("account-identity") accountIdentity: String,
        @Valid request: CreateContentWithDetailRequest,
    ): BaseResponseBody<ContentBlockResponse> {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)?.name

        val contentBlockType =
            Const.ContentBlockType.entries.find { it.id == request.contentBlockType }
                ?: throw BadRequestException(
                    "Invalid contentBlockType: ${request.contentBlockType}"
                )

        val createRequest =
            CreateContentBlock.CreateContentBlockRequest(
                contentBlockType = contentBlockType,
                title = request.title,
                description = request.description,
                appDescription = request.appDescription,
                url = request.url,
                iconUrl = request.iconUrl,
            )

        val contentBlock =
            userContentBlockController.createContentBlock(
                accountIdentity = accountIdentity,
                request = createRequest,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid,
            )

        return BaseResponseBody(
            data = ContentBlockResponse(contentBlock = contentBlock),
            errors = emptyList(),
        )
    }

    data class ContentBlockDetailResponse(val contentBlockDetail: ContentBlockDetail)

    @PUT
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun updateContentBlockDetail(
        @PathParam("account-identity") accountIdentity: String,
        @Valid request: UpdateContentBlockDetailRequest,
    ): BaseResponseBody<ContentBlockDetailResponse> {
        val currentUserUid = util.getCurrentUserUid(securityIdentity)
        val currentUserRole = util.getCurrentUserRole(securityIdentity)?.name

        val contentBlockDetail =
            userContentBlockController.updateContentBlockDetail(
                accountIdentity = accountIdentity,
                request = request,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid,
            )

        return BaseResponseBody(
            data = ContentBlockDetailResponse(contentBlockDetail = contentBlockDetail),
            errors = emptyList(),
        )
    }
}
