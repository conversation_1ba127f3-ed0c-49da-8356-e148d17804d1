package jp.co.torihada.fanme.modules.payment.usecases.balance

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance

@ApplicationScoped
class GetSellerAccountBalances {

    data class Input(val sellerUserIds: List<String>)

    data class Output(val balances: List<SellerBalance>)

    data class SellerBalance(val sellerUserId: String, val accumulatedSales: Int, val amount: Int)

    fun execute(input: Input): Result<Output, Exception> {
        return try {
            val balances =
                SellerAccountBalance.findBySellerUserIds(input.sellerUserIds).map {
                    SellerBalance(
                        sellerUserId = it.sellerUserId,
                        accumulatedSales = it.accumulatedSales ?: 0,
                        amount = it.amount,
                    )
                }

            Ok(Output(balances))
        } catch (e: Exception) {
            Err(e)
        }
    }
}
