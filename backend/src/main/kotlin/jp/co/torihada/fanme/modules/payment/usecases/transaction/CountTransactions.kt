package jp.co.torihada.fanme.modules.payment.usecases.transaction

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.models.Transaction

@ApplicationScoped
class CountTransactions {

    data class Input(val sellerUserId: String)

    fun execute(input: Input): Result<Long, FanmeException> {
        val count = Transaction.countBySellerUserId(input.sellerUserId)

        return Ok(count)
    }
}
