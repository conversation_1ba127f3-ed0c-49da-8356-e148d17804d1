package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks

@ApplicationScoped
class GetUserContentBlocks {
    @Inject private lateinit var contentBlockController: ContentBlockController
    @Inject private lateinit var userController: UserController
    @Inject private lateinit var securityUtils: SecurityUtils

    fun execute(
        accountIdentity: String,
        currentUserRole: String?,
        currentUserUid: String?,
    ): Result<List<GetContentBlocks.ContentBlock>, ConsoleException> {
        return try {
            val user =
                userController.getUserByAccountIdentity(accountIdentity)
                    ?: throw ResourceNotFoundException(
                        "User not found for account identity: $accountIdentity"
                    )

            securityUtils.validateAgentForUserAccess(user, currentUserRole, currentUserUid)

            val contentBlocks =
                contentBlockController.getUserContentBlocks(
                    creatorAccountIdentity = user.accountIdentity!!
                )
            Ok(contentBlocks)
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }
}
