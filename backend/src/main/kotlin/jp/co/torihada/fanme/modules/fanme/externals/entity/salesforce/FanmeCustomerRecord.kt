package jp.co.torihada.fanme.externals.client.salesforce.entities.fanmeCustomer

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.runtime.annotations.RegisterForReflection
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.Attributes
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.SalesforceRecord

@RegisterForReflection
data class FanmeCustomerRecord(
    @JsonProperty("Id") val id: String? = null,
    @JsonProperty("attributes") val attributes: Attributes?,
    @JsonProperty("FanmeUserId__c") val fanmeUserId: String?,
    @JsonProperty("Name__c") val name: String,
    @JsonProperty("NameKana__c") val nameKana: String,
    @JsonProperty("PostalCode__c") val postalCode: String,
    @JsonProperty("Prefecture__c") val prefecture: String,
    @JsonProperty("City__c") val city: String,
    @JsonProperty("Street__c") val street: String,
    @JsonProperty("Building__c") val building: String?,
    @JsonProperty("PhoneNumber__c") val phoneNumber: String,
) : SalesforceRecord
