package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserWithdrawal
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import org.eclipse.microprofile.rest.client.inject.RestClient

@ApplicationScoped
class WithdrawUser {

    @Inject @RestClient private lateinit var authClient: ExtAuthClient

    data class Input(val userUuid: String, val token: String, val reason: Int, val detail: String?)

    fun execute(input: Input): Result<Unit, Exception> {
        try {
            val reason =
                UserWithdrawal.Reason.fromValue(input.reason)
                    ?: return Err(IllegalArgumentException("Invalid reason value: ${input.reason}"))
            val user =
                User.findByUuid(input.userUuid) ?: return Err(ResourceNotFoundException("User"))

            authClient.deleteUser(input.token)

            UserWithdrawal.create(user, reason, input.detail)
            User.softDelete(user)

            return Ok(Unit)
        } catch (e: Exception) {
            return Err(e)
        }
    }
}
