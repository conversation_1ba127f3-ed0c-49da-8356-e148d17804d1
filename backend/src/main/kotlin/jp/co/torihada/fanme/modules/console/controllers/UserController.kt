package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Min
import jp.co.torihada.fanme.modules.console.usecases.GetUser
import jp.co.torihada.fanme.modules.console.usecases.GetUsersByPartialAccountIdentity
import jp.co.torihada.fanme.modules.fanme.models.User
import kotlin.getOrThrow

@ApplicationScoped
class UserController
@Inject
constructor(
    private val getUser: GetUser,
    private val getUsersByPartialAccountIdentity: GetUsersByPartialAccountIdentity,
) {

    fun getUser(@Min(1) id: Long): GetUser.ConsoleGetUserResponse {
        return getUser.execute(GetUser.Input(id)).getOrThrow()
    }

    fun getUsersByPartialAccountIdentity(partialAccountIdentity: String?): List<User> {

        if (partialAccountIdentity.isNullOrBlank()) {
            return emptyList()
        }

        return getUsersByPartialAccountIdentity
            .execute(GetUsersByPartialAccountIdentity.Input(partialAccountIdentity))
            .getOrThrow()
    }
}
