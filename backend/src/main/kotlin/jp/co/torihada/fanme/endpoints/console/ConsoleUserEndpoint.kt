package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.ConsoleUsersResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.ConsoleUserController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/console-users")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class ConsoleUserEndpoint {

    @Inject private lateinit var handler: ConsoleUserController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(ConsoleUsersResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    @Produces(MediaType.APPLICATION_JSON)
    fun getConsoleUsers(): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val result = handler.getConsoleUsers(odata)
            val entity = ResponseEntity(result, "consoleUsers")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
