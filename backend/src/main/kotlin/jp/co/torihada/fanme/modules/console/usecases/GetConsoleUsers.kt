package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ConsoleUserFetchException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetConsoleUsers {

    data class Input(val odata: OData?)

    fun execute(input: Input): Result<List<ConsoleUser>, FanmeException> {
        return try {
            val consoleUsers = ConsoleUser.findAll(input.odata?.top, input.odata?.skip)
            Ok(consoleUsers)
        } catch (e: Exception) {
            Err(ConsoleUserFetchException(e.message))
        }
    }
}
