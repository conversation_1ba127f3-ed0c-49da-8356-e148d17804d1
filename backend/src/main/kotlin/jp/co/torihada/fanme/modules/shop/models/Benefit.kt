package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

enum class BenefitCondition(val value: Int) {
    PURCHASED(1), // 購入特典
    COMPLETE_DIGITAL_GACHA(100), // ガチャコンプリート特典
    PULLED_DIGITAL_GACHA_10_TIMES(101), // ガチャ10回引いたら特典
    PULLED_DIGITAL_GACHA_20_TIMES(102), // ガチャ20回引いたら特典
    PULLED_DIGITAL_GACHA_30_TIMES(103); // ガチャ30回引いたら特典

    companion object {
        fun fromValue(value: Int): BenefitCondition {
            return entries.find { it.value == value } ?: PURCHASED
        }
    }
}

@Converter(autoApply = true)
class BenefitConditionConverter : AttributeConverter<BenefitCondition, Int> {
    override fun convertToDatabaseColumn(attribute: BenefitCondition?): Int? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(dbData: Int?): BenefitCondition? {
        return dbData?.let { value -> BenefitCondition.entries.find { it.value == value } }
    }
}

@PersistenceUnit(name = "benefit")
@Entity
@Table(name = "benefits")
class Benefit : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    var item: Item = Item()

    @Size(max = 100) @Column(nullable = true, length = 100) var description: String? = null

    @NotNull
    @Column(name = "condition_type", nullable = false)
    var conditionType: BenefitCondition = BenefitCondition.PURCHASED

    @OneToMany(mappedBy = "benefit", orphanRemoval = true)
    var files: MutableSet<BenefitFile> = mutableSetOf()

    @OneToMany(mappedBy = "benefit", orphanRemoval = true)
    var benefitCompletionLogs: MutableSet<BenefitCompletionLog> = mutableSetOf()

    companion object : PanacheCompanion<Benefit> {
        fun findByItemId(itemId: Long): List<Benefit> {
            return find("item.id = ?1", itemId).list()
        }

        fun create(
            itemId: Long,
            description: String?,
            benefitCondition: BenefitCondition = BenefitCondition.PURCHASED,
        ): Benefit {
            val benefit = Benefit()
            benefit.item = Item.findById(itemId)!!
            benefit.description = description
            benefit.conditionType = benefitCondition
            benefit.persist()
            return benefit
        }

        fun update(id: Long, description: String?): Benefit {
            val benefit = findById(id)!!
            benefit.description = description
            benefit.persist()
            return benefit
        }

        fun delete(itemId: Long) {
            val benefits = findByItemId(itemId)
            if (benefits.isNotEmpty()) {
                delete("delete from Benefit where item.id = ?1", itemId)
            }
        }
    }
}
