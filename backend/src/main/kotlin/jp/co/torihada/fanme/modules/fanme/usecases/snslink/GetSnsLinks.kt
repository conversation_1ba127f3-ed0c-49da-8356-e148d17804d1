package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.fanme.models.SnsLink

@ApplicationScoped
class GetSnsLinks {

    data class Input(val userUuid: String)

    fun execute(input: Input): Result<List<SnsLink>, Exception> {
        return Ok(
            SnsLink.findByUserUuid(input.userUuid).sortedBy {
                it.snsLinkDisplay?.displayOrderNumber
            }
        )
    }
}
