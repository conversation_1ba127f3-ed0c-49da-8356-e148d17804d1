package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock

@ApplicationScoped
class DeleteContentBlock {

    data class Input(val contentBlockId: Long)

    fun execute(input: Input): Result<Unit, Exception> {
        val contentBlock =
            ContentBlock.findById(input.contentBlockId)
                ?: return Err(ResourceNotFoundException("ContentBlock"))
        contentBlock.delete()
        return Ok(Unit)
    }
}
