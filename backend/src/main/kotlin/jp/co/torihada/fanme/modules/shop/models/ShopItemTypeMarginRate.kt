package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "shop_item_type_margin_rate")
@Entity
@Table(name = "shop_item_type_margin_rates")
class ShopItemTypeMarginRate : BaseModel() {
    @NotNull
    @ManyToOne
    @JoinColumn(name = "shop_id", nullable = false, updatable = false)
    var shop: Shop = Shop()

    @NotNull
    @Column(name = "item_type", nullable = false)
    @Convert(converter = ItemTypeConverter::class)
    var itemType: ItemType = ItemType.DIGITAL_BUNDLE

    @NotNull
    @Column(name = "margin_rate", nullable = false)
    var marginRate: Float = Const.DEFAULT_MARGIN_RATE

    companion object : PanacheCompanion<ShopItemTypeMarginRate> {
        fun find(shopId: Long, itemType: ItemType): ShopItemTypeMarginRate? {
            return find("shop.id = ?1 and itemType = ?2", shopId, itemType).firstResult()
        }

        fun findByShopId(shopId: Long): List<ShopItemTypeMarginRate> {
            return find("shop.id = ?1", shopId).list()
        }

        fun create(shop: Shop, itemType: ItemType, marginRate: Float): ShopItemTypeMarginRate {
            val rate =
                ShopItemTypeMarginRate().apply {
                    this.shop = shop
                    this.itemType = itemType
                    this.marginRate = marginRate
                }
            rate.persist()
            return rate
        }
    }
}
