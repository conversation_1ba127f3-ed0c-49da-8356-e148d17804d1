package jp.co.torihada.fanme.modules.fanme.services.address

import io.quarkus.arc.profile.IfBuildProfile
import jakarta.enterprise.context.ApplicationScoped

@IfBuildProfile(anyOf = ["local", "test"])
@ApplicationScoped
class StubAddressService : IAddressService {
    override fun suggestAddress(postalCode: String): SuggestedAddressEntity? {
        println("StubAddressService postalCode: $postalCode")
        return SuggestedAddressEntity(prefecture = "東京都", city = "渋谷区", street = "道玄坂1-1-1")
    }
}
