package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile as ProfileModel
import jp.co.torihada.fanme.modules.fanme.models.SnsLinkDisplay
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class MoveSnsLinks {

    data class Input(val userId: Long, val snsLinks: List<String>)

    @Schema(name = "MoveSnsLinks_Response") data class Output(val success: Boolean = true)

    fun execute(input: Input): Result<Output, Exception> {
        val profile =
            ProfileModel.findByUserId(input.userId)
                ?: return Err(ResourceNotFoundException("Profile"))

        val snsLinkDisplays =
            profile.snsLinks
                .mapNotNull { it.snsLinkDisplay }
                .filter { it.displayable == true }
                .sortedBy { it.displayOrderNumber }

        val validateResult = validateInput(input, snsLinkDisplays)
        if (validateResult.isErr) {
            return Err(validateResult.error)
        }

        snsLinkDisplays.forEach { snsLinkDisplay ->
            SnsLinkDisplay.updateDisplayOrderNumber(
                snsLinkDisplay = snsLinkDisplay,
                displayOrderNumber = snsLinkDisplay.displayOrderNumber!! + 1000,
            )
        }

        val newMapping = input.snsLinks.mapIndexed { index, type -> type to index + 1 }.toMap()
        snsLinkDisplays.forEachIndexed { index, snsLinkDisplay ->
            val type = Const.SnsLinkType.fromDbStr(snsLinkDisplay.snsLink?.type!!)!!.value
            val newOrder =
                newMapping[type]
                    ?: return Err(IllegalArgumentException("Invalid SNS link type: $type"))

            SnsLinkDisplay.updateDisplayOrderNumber(
                snsLinkDisplay = snsLinkDisplay,
                displayOrderNumber = newOrder,
            )
        }

        return Ok(Output())
    }

    private fun validateInput(
        input: Input,
        snsLinkDisplays: List<SnsLinkDisplay>,
    ): Result<Unit, IllegalArgumentException> {
        if (input.snsLinks.size != snsLinkDisplays.size) {
            return Err(IllegalArgumentException("Request is invalid: link count mismatch"))
        }

        val registeredTypes =
            snsLinkDisplays.map { Const.SnsLinkType.fromDbStr(it.snsLink?.type!!) }.toSet()
        val inputTypes =
            input.snsLinks
                .map {
                    Const.SnsLinkType.fromValue(it)
                        ?: return Err(IllegalArgumentException("Invalid SNS link type: $it"))
                }
                .toSet()
        if (registeredTypes != inputTypes) {
            return Err(IllegalArgumentException("Request is invalid: SNS link types mismatch"))
        }

        return Ok(Unit)
    }
}
