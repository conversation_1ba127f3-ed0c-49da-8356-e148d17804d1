package jp.co.torihada.fanme.modules.shop.services.audit

import jakarta.enterprise.context.ApplicationScoped
import java.net.URI

@ApplicationScoped
class BucketPathResolver {
    data class ResolvedPath(val bucketName: String, val filePath: String)

    fun resolve(url: String): ResolvedPath {
        val uri = URI(url)
        val path = uri.path

        // パスをスラッシュで分割
        val pathParts = path.split("/").filter { it.isNotEmpty() }

        // バケット名は最初のパス部分
        val bucketName =
            pathParts.firstOrNull() ?: throw IllegalArgumentException("Invalid path: $url")

        // ファイルパスは残りの部分を結合
        val filePath = pathParts.drop(1).joinToString("/")

        return ResolvedPath(bucketName = bucketName, filePath = filePath)
    }

    // バケット名のみを取得
    fun getBucketName(url: String): String = resolve(url).bucketName

    // ファイルパスのみを取得
    fun getFilePath(url: String): String = resolve(url).filePath

    // パスが有効かどうかを検証
    fun isValidPath(url: String): Boolean {
        return try {
            resolve(url)
            true
        } catch (e: Exception) {
            false
        }
    }
}
