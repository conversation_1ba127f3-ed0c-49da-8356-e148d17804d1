package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetUser @Inject constructor(private val userController: UserController) {

    data class Input(val id: Long)

    data class ConsoleGetUserResponse(val user: User, val role: String, val agencyId: Long?)

    fun execute(input: Input): Result<ConsoleGetUserResponse, ConsoleException> {

        val user = userController.getUserById(input.id)

        val userId = user.id ?: throw ConsoleResourceNotFoundException("User ID")

        val consoleUser =
            user.consoleUser
                ?: return Err(
                    ConsoleResourceNotFoundException(
                        "Console user with User id $userId was not found."
                    )
                )

        return Ok(
            ConsoleGetUserResponse(
                user = user,
                role = consoleUser.role,
                agencyId = consoleUser.agencyId,
            )
        )
    }
}
