package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.CreateOrUpdateItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.RequestConverter
import jp.co.torihada.fanme.exception.*
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.modules.shop.models.Item

@ApplicationScoped
class UpdateCreatorShopItem
@Inject
constructor(
    private val itemController: ItemController,
    private val userController: UserController,
    private val securityUtils: SecurityUtils,
    private val requestConverter: RequestConverter,
) {

    data class Input(
        val accountIdentity: String,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
        val itemId: Long,
        val requestBody: CreateOrUpdateItemRequest,
    )

    fun execute(params: Input): Result<Item, ConsoleException> {
        return try {
            val targetUser =
                userController.getUserByNotDeletedAccountIdentity(params.accountIdentity)
                    ?: throw ResourceNotFoundException("User")

            val targetConsoleUser =
                targetUser.consoleUser ?: throw ResourceNotFoundException("ConsoleUser")
            val targetAgencyId =
                targetConsoleUser.agencyId ?: throw ResourceNotFoundException("Agency")

            securityUtils.validateAgentAccess(
                targetAgencyId,
                params.currentUserUid,
                params.currentUserRole.value,
            )

            val updateRequest =
                requestConverter.requestToUpdateItem(
                    targetUser.uid!!,
                    params.itemId,
                    params.requestBody,
                )

            val updatedItem = itemController.updateItem(updateRequest)

            Ok(updatedItem)
        } catch (e: ConsoleException) {
            Err(e)
        } catch (e: Exception) {
            Err(ConsoleException(0, "${e::class.simpleName}: ${e.message}"))
        }
    }
}
