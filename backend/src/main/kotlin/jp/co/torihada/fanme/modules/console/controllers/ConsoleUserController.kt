package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.usecases.GetConsoleUsers
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class ConsoleUserController {

    @Inject private lateinit var getConsoleUsers: GetConsoleUsers

    fun getConsoleUsers(odata: OData?): List<ConsoleUser> {
        return getConsoleUsers.execute(GetConsoleUsers.Input(odata)).getOrElse { error ->
            throw error
        }
    }
}
