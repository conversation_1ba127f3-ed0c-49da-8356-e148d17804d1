package jp.co.torihada.fanme.modules.payment.utils

import java.time.Instant
import java.time.YearMonth as JavaYearMonth
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException

object YearMonth {
    private val formatter = DateTimeFormatter.ofPattern("yyyyMM")

    fun validate(yearMonth: String?) {
        if (yearMonth != null && !isValid(yearMonth)) {
            throw InvalidYearMonthFormatException(
                "Invalid format: $yearMonth. Expected format: YYYYMM"
            )
        }
    }

    fun toMonthPeriod(yearMonth: String): Pair<Instant, Instant> {
        validate(yearMonth)

        val yearMonthObj = JavaYearMonth.parse(yearMonth, formatter)
        val startDate = yearMonthObj.atDay(1).atStartOfDay(ZoneOffset.UTC).toInstant()
        val endDate = yearMonthObj.plusMonths(1).atDay(1).atStartOfDay(ZoneOffset.UTC).toInstant()

        return Pair(startDate, endDate)
    }

    fun isValid(yearMonth: String): Boolean {
        if (!yearMonth.matches(Regex("^\\d{6}$"))) {
            return false
        }

        return try {
            val parsed = JavaYearMonth.parse(yearMonth, formatter)
            parsed.year in 1900..2100
        } catch (_: DateTimeParseException) {
            false
        }
    }
}
