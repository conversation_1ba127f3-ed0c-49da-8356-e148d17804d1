package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.GET
import jakarta.ws.rs.PUT
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.CreateOrUpdateItemRequest
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.CreatorShopItemsController
import jp.co.torihada.fanme.modules.console.usecases.GetCreatorShopItems.ConsoleCreatorShopItemsResponse
import jp.co.torihada.fanme.modules.shop.models.Item
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/creators/{creator-account-identity}/shop/items")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class CreatorShopItemsEndpoint {

    @Inject lateinit var handler: CreatorShopItemsController

    @Inject lateinit var securityIdentity: SecurityIdentity

    @Inject lateinit var util: Util

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    fun getItems(
        @PathParam("creator-account-identity") accountIdentity: String
    ): BaseResponseBody<ConsoleCreatorShopItemsResponse> {
        val uid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val role = util.getCurrentUserRole(securityIdentity) ?: throw ForbiddenAccessException()

        val dto = handler.getItems(accountIdentity, uid, role)
        return BaseResponseBody(data = dto, errors = emptyList())
    }

    @PUT
    @Path("/{item-id}")
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateItem(
        @PathParam("creator-account-identity") accountIdentity: String,
        @PathParam("item-id") itemId: Long,
        requestBody: CreateOrUpdateItemRequest,
    ): BaseResponseBody<Item> {
        val uid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val role = util.getCurrentUserRole(securityIdentity) ?: throw ForbiddenAccessException()

        val updatedItem =
            handler.updateItem(
                accountIdentity = accountIdentity,
                currentUserUid = uid,
                currentUserRole = role,
                itemId = itemId,
                requestBody = requestBody,
            )
        return BaseResponseBody(data = updatedItem, errors = emptyList())
    }
}
