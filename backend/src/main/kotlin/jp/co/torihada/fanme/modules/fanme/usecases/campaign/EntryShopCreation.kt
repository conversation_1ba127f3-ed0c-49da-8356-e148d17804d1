package jp.co.torihada.fanme.modules.fanme.usecases.campaign

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.CampaignEntry
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class EntryShopCreation {
    fun execute(userUid: String, campaignIdentity: String): Result<Boolean, FanmeException> {
        val campaigns = Campaign.Companion.findValidCampaigns(Instant.now())
        val user =
            User.Companion.findByUuid(userUid) ?: return Err(ResourceNotFoundException("User"))

        // キャンペーンコードありのショップ作成によるエントリー
        campaigns
            .filter {
                it.entryType == Campaign.EntryType.SHOP_CREATION &&
                    it.campaignIdentity == campaignIdentity
            }
            .forEach { campaign -> CampaignEntry.Companion.create(campaign, user) }
        // キャンペーンコードなしのショップ作成によるエントリー
        campaigns
            .filter { it.entryType == Campaign.EntryType.SHOP_CREATION_ALL }
            .forEach { campaign -> CampaignEntry.Companion.create(campaign, user) }

        return Ok(true)
    }
}
