package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.console.UserContentBlockEndpoint
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.console.usecases.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail

@ApplicationScoped
class UpdateContentBlockDetail {

    @Inject lateinit var contentBlockController: ContentBlockController
    @Inject lateinit var userController: UserController
    @Inject lateinit var securityUtils: SecurityUtils

    fun execute(
        accountIdentity: String,
        request: UserContentBlockEndpoint.UpdateContentBlockDetailRequest,
        currentUserRole: String?,
        currentUserUid: String?,
    ): Result<ContentBlockDetail, Throwable> {
        return try {
            val targetUser =
                userController.getUserByAccountIdentity(accountIdentity)
                    ?: throw ConsoleResourceNotFoundException("User not found: $accountIdentity")

            securityUtils.validateAgentForUserAccess(
                targetUser = targetUser,
                currentUserRole = currentUserRole,
                currentUserUid = currentUserUid,
            )

            val updateRequest =
                ContentBlockController.UpdateContentBlockDetailRequest(
                    contentBlockDetailId = request.contentBlockDetailId,
                    title = request.title,
                    description = request.description,
                    appDescription = request.appDescription,
                    url = request.url,
                    iconUrl = request.iconUrl,
                    fileUpload = request.fileUpload,
                )

            val result = contentBlockController.updateContentBlockDetail(updateRequest)
            Ok(result)
        } catch (e: Exception) {
            Err(e)
        }
    }
}
