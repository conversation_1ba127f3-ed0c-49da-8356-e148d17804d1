package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class MoveContentBlock {
    class Input(val user: User, val contentBlockIds: List<Long>)

    fun execute(input: Input): Result<List<ContentBlock>, Exception> {
        val existingBlocks = input.user.contentBlocks
        val existingBlockMap = existingBlocks.associateBy { it.id }
        val existingIds = existingBlockMap.keys

        // IDの整合性チェック（順不同）
        if (existingIds != input.contentBlockIds.toSet()) {
            return Err(Exception("Invalid content block IDs"))
        }

        // 全てのContentBlockのdisplayOrderNumberを一時的な値に設定
        val tempOrderNum = 999_999
        existingBlocks.forEachIndexed { index, block ->
            ContentBlock.updateDisplayOrderNumbers(
                contentBlock = block,
                displayOrderNumber = tempOrderNum - index,
            )
        }

        val result =
            input.contentBlockIds.mapIndexed { index, id ->
                val block =
                    existingBlockMap[id] ?: return Err(ResourceNotFoundException("ContentBlock"))
                ContentBlock.updateDisplayOrderNumbers(
                    contentBlock = block,
                    displayOrderNumber = index + 1,
                )
            }

        return Ok(result)
    }
}
