package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.batch.usecases.SellerSalesExpire
import org.jboss.logging.Logger

class SellerSalesExpireMain : QuarkusApplication {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var sellerSalesExpireBatch: SellerSalesExpire

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("SellerSalesExpireMain start")
        try {
            sellerSalesExpireBatch.execute()
            logger.info("SellerSalesExpireMain success")
            return 0
        } catch (e: Exception) {
            logger.error("SellerSalesExpireMain error", e)
            return 1
        }
    }
}
