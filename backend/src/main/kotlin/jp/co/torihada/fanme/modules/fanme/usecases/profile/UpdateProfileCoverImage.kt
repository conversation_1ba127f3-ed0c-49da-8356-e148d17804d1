package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.DisplayableCoverImage
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCoverImage

@ApplicationScoped
class UpdateProfileCoverImage {

    class Input(val userId: Long, val coverImage: String, val resourceType: String)

    fun execute(input: Input): Result<ProfileCoverImage, Exception> {
        val profileCover =
            Profile.findByUserId(input.userId)?.cover
                ?: return Err(ResourceNotFoundException("Profile cover"))

        val profileCoverImage =
            ProfileCoverImage.create(
                profileCover = profileCover,
                resourceType = input.resourceType,
                resource = input.coverImage,
            )

        val displayableCoverImage = profileCover.displayableCoverImage
        val updatedDisplayableCoverImage =
            if (displayableCoverImage != null) {
                DisplayableCoverImage.update(
                    displayableCoverImage = displayableCoverImage,
                    profileCover = profileCover,
                    profileCoverImage = profileCoverImage,
                )
            } else {
                DisplayableCoverImage.create(
                    profileCover = profileCover,
                    profileCoverImage = profileCoverImage,
                )
            }

        profileCoverImage.displayableCoverImage = updatedDisplayableCoverImage

        return Ok(profileCoverImage)
    }
}
