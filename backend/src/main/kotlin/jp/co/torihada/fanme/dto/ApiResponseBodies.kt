package jp.co.torihada.fanme.dto

import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.usecases.GetAgencySales
import jp.co.torihada.fanme.modules.console.usecases.GetUser
import jp.co.torihada.fanme.modules.fanme.controllers.RankingEventController.RankingEventWithBoost
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.services.address.SuggestedAddressEntity
import jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer.FanmeCustomerEntity
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks
import jp.co.torihada.fanme.modules.fanme.usecases.userTutorial.GetUserTutorial
import jp.co.torihada.fanme.modules.shop.controllers.GachaController.GetGachaCompleteBadgeRankingResponse
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadge
import jp.co.torihada.fanme.modules.shop.usecases.cartItem.GetCartItems
import jp.co.torihada.fanme.modules.shop.usecases.file.GetDownloadUrls
import jp.co.torihada.fanme.modules.shop.usecases.gacha.GetPullableGachaCount
import jp.co.torihada.fanme.modules.shop.usecases.gacha.GetPurchasedItemReceivedFiles
import jp.co.torihada.fanme.modules.shop.usecases.gacha.PullGachaItems
import jp.co.torihada.fanme.modules.shop.usecases.item.GetItem
import jp.co.torihada.fanme.modules.shop.usecases.order.GetTipUpperLimit
import jp.co.torihada.fanme.modules.shop.usecases.purchasedItem.GetPurchasedItem
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop

data class ItemResponseBody(
    override val data: ItemData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ItemResponseBody.ItemData>(data, errors) {
    data class ItemData(val item: GetItem.ItemForGetItem)
}

data class ShopResponseBody(
    override val data: ShopData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ShopResponseBody.ShopData>(data, errors) {
    data class ShopData(val shop: GetShop.ShopForGetShop)
}

data class RankingEventInfoResponseBody(
    override val data: EventData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<RankingEventInfoResponseBody.EventData>(data, errors) {
    data class EventData(val event: RankingEventWithBoost)
}

data class TipLimitResponseBody(
    override val data: TipLimitData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<TipLimitResponseBody.TipLimitData>(data, errors) {
    data class TipLimitData(val tip_limit: GetTipUpperLimit.TipUpperLimit)
}

data class PurchaseItemResponseBody(
    override val data: PurchaseItemData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<PurchaseItemResponseBody.PurchaseItemData>(data, errors) {
    data class PurchaseItemData(val purchasedItem: GetPurchasedItem.PurchasedItemDetail)
}

data class DownloadUrlResponseBody(
    override val data: DownloadUrlData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<DownloadUrlResponseBody.DownloadUrlData>(data, errors) {
    data class DownloadUrlData(val downloadUrls: List<GetDownloadUrls.DownloadUrl>)
}

data class FanmeCustomerResponseBody(
    override val data: FanmeCustomerData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<FanmeCustomerResponseBody.FanmeCustomerData>(data, errors) {
    data class FanmeCustomerData(val fanmeCustomer: FanmeCustomerEntity)
}

data class SuggestAddressResponseBody(
    override val data: SuggestAddressData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<SuggestAddressResponseBody.SuggestAddressData>(data, errors) {
    data class SuggestAddressData(val suggestedAddress: SuggestedAddressEntity)
}

data class CartItemsResponseBody(
    val cart: GetCartItems.CartItemData,
    val errors: List<ErrorObject>? = null,
)

data class SingleOrderResponseBody(
    override val data: SingleOrderData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<SingleOrderResponseBody.SingleOrderData>(data, errors) {
    data class SingleOrderData(val order: OrderController.OrderResult)
}

data class AgenciesResponseBody(
    override val data: AgencyData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<AgenciesResponseBody.AgencyData>(data, errors) {
    data class AgencyData(val agencies: List<Agency>)
}

data class CompleteBadgeResponseBody(
    override val data: CompleteBadgeData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<CompleteBadgeResponseBody.CompleteBadgeData>(data, errors) {
    data class CompleteBadgeData(val badge: GetBadge.Output)
}

data class BadgeRankingResponseBody(
    override val data: BadgeRankingData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<BadgeRankingResponseBody.BadgeRankingData>(data, errors) {
    data class BadgeRankingData(val ranking: List<GetGachaCompleteBadgeRankingResponse>)
}

data class GachaPullResponseBody(
    override val data: GachaPullData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<GachaPullResponseBody.GachaPullData>(data, errors) {
    data class GachaPullData(val files: List<PullGachaItems.FileForPullGachaItems>)
}

data class GachaPullableCountResponseBody(
    override val data: GachaPullableCountData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<GachaPullableCountResponseBody.GachaPullableCountData>(data, errors) {
    data class GachaPullableCountData(val item: GetPullableGachaCount.Output)
}

data class ConsoleUsersResponseBody(
    override val data: ConsoleUsersData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ConsoleUsersResponseBody.ConsoleUsersData>(data, errors) {
    data class ConsoleUsersData(val consoleUsers: List<ConsoleUser>)
}

data class ConsoleUserResponseBody(
    override val data: ConsoleUserData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ConsoleUserResponseBody.ConsoleUserData>(data, errors) {
    data class ConsoleUserData(val user: GetUser.ConsoleGetUserResponse)
}

data class UsersResponseBody(
    override val data: UsersData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<UsersResponseBody.UsersData>(data, errors) {
    data class UsersData(val users: List<User>)
}

data class AgencySalesResponseBody(
    override val data: AgencySalesData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<AgencySalesResponseBody.AgencySalesData>(data, errors) {
    data class AgencySalesData(val agencySales: GetAgencySales.AgencySales)
}

data class AuditGroupsResponseBody(
    override val data: AuditGroupsData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<AuditGroupsResponseBody.AuditGroupsData>(data, errors) {
    data class AuditGroupsData(val auditGroups: List<AuditGroup>)
}

data class AuditStatusResponseBody(
    override val data: AuditStatusData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<AuditStatusResponseBody.AuditStatusData>(data, errors) {
    data class AuditStatusData(val result: Boolean)
}

data class ContentBlockResponseBody(
    override val data: ContentBlockData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ContentBlockResponseBody.ContentBlockData>(data, errors) {
    data class ContentBlockData(val contentBlocks: List<GetContentBlocks.ContentBlock>)
}

data class UserTutorialResponseBody(
    override val data: UserTutorialData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<UserTutorialResponseBody.UserTutorialData>(data, errors) {
    data class UserTutorialData(val userTutorial: GetUserTutorial.UserTutorial)
}

data class PurchasedItemReceivedFilesResponseBody(
    override val data: PurchasedItemReceivedFilesData,
    override val errors: List<ErrorObject>? = null,
) :
    BaseResponseBody<PurchasedItemReceivedFilesResponseBody.PurchasedItemReceivedFilesData>(
        data,
        errors,
    ) {
    data class PurchasedItemReceivedFilesData(
        val files: List<GetPurchasedItemReceivedFiles.PurchasedItemReceivedFile>
    )
}

data class UserCurrentEntryCampaignsResponseBody(
    override val data: UserCurrentEntryCampaignsData,
    override val errors: List<ErrorObject>? = null,
) :
    BaseResponseBody<UserCurrentEntryCampaignsResponseBody.UserCurrentEntryCampaignsData>(
        data,
        errors,
    ) {
    data class UserCurrentEntryCampaignsData(val campaigns: List<Campaign>)
}

data class ItemCostResponseBody(
    override val data: ItemCostData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody<ItemCostResponseBody.ItemCostData>(data, errors) {
    data class ItemCostData(val cost: Int)
}
