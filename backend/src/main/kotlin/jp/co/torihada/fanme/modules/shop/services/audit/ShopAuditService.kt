package jp.co.torihada.fanme.modules.shop.services.audit

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerAuditObject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerCreateInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.shop.models.*
import org.jboss.logging.Logger

@ApplicationScoped
class ShopAuditService {
    enum class OperationType(val value: String) {
        INSERT("INSERT"),
        UPDATE("UPDATE"),
    }

    @Inject private lateinit var logger: Logger

    @Inject private lateinit var auditGroupController: AuditGroupController

    @Inject private lateinit var bucketPathResolver: BucketPathResolver

    fun createAuditDataForShop(shop: Shop, creatorUid: String, operationType: OperationType) {
        try {
            if (
                shop.headerImageUri !== null &&
                    shop.headerImageUri!!.isNotEmpty() &&
                    !bucketPathResolver.isValidPath(shop.headerImageUri!!)
            ) {
                return
            }

            val auditObjects = mutableListOf<AuditGroupControllerAuditObject>()
            shop.headerImageUri?.let {
                auditObjects.add(
                    AuditGroupControllerAuditObject(
                        bucket = bucketPathResolver.getBucketName(it),
                        filePath = bucketPathResolver.getFilePath(it),
                        assetType = AssetType.IMAGE,
                    )
                )
            }

            auditGroupController.createAuditGroup(
                AuditGroupControllerCreateInput(
                    auditType = "shop",
                    userUid = creatorUid,
                    operationType = operationType.value,
                    metadata =
                        AuditGroupMetadata(
                            shopId = shop.id,
                            title = shop.name,
                            description = shop.description,
                        ),
                    auditObjects = auditObjects,
                )
            )
        } catch (e: Exception) {
            // 作成されなくても動作するリソースのため、エラーを無視する
            logger.error("Failed to create shop audit data", e)
            logger.error(e.stackTraceToString())
        }
    }

    fun createAuditDataForShopItem(
        creatorUid: String,
        operationType: OperationType,
        item: Item,
        files: List<ItemFile>?,
        samples: List<Sample>?,
        benefitFiles: List<BenefitFile>?,
    ) {
        try {
            val auditObjects = mutableListOf<AuditGroupControllerAuditObject>()
            // サムネイル
            auditObjects.add(
                AuditGroupControllerAuditObject(
                    bucket = bucketPathResolver.getBucketName(item.thumbnailUri),
                    filePath = bucketPathResolver.getFilePath(item.thumbnailUri),
                    assetType = exchangeAssetType("image"),
                )
            )
            // 商品ファイル
            files?.let {
                files.forEach {
                    if (it.objectUri.isNullOrEmpty()) return@forEach
                    auditObjects.add(
                        AuditGroupControllerAuditObject(
                            bucket = bucketPathResolver.getBucketName(it.objectUri!!),
                            filePath = bucketPathResolver.getFilePath(it.objectUri!!),
                            assetType = exchangeAssetType(it.fileType),
                        )
                    )
                }
            }
            // サンプルファイル
            samples?.let {
                samples.forEach {
                    auditObjects.add(
                        AuditGroupControllerAuditObject(
                            bucket = bucketPathResolver.getBucketName(it.objectUri),
                            filePath = bucketPathResolver.getFilePath(it.objectUri),
                            assetType = exchangeAssetType(it.fileType),
                        )
                    )
                }
            }

            // 特典ファイル
            benefitFiles?.let {
                it.forEach { file ->
                    auditObjects.add(
                        AuditGroupControllerAuditObject(
                            bucket = bucketPathResolver.getBucketName(file.objectUri),
                            filePath = bucketPathResolver.getFilePath(file.objectUri),
                            assetType = exchangeAssetType(file.fileType),
                        )
                    )
                }
            }

            // 上記を含む監査グループを作成
            auditGroupController.createAuditGroup(
                AuditGroupControllerCreateInput(
                    auditType = "shop_item",
                    userUid = creatorUid,
                    operationType = operationType.value,
                    metadata =
                        AuditGroupMetadata(
                            shopId = item.shop.id,
                            itemId = item.id.toString(),
                            title = item.name,
                            description = item.description,
                        ),
                    auditObjects = auditObjects,
                )
            )
        } catch (e: Exception) {
            // 作成されなくても動作するリソースのため、エラーを無視する
            logger.error("Failed to create shop item audit data", e)
            logger.error(e.stackTraceToString())
        }
    }

    private fun exchangeAssetType(fileType: String): AssetType {
        return when (fileType) {
            "image" -> AssetType.IMAGE
            "video" -> AssetType.MOVIE
            "audio" -> AssetType.VOICE
            else -> AssetType.ANY
        }
    }
}
