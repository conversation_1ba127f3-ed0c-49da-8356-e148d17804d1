package jp.co.torihada.fanme.lib

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import jakarta.enterprise.inject.spi.CDI

class MaskSerializer : JsonSerializer<String>() {

    companion object {
        private val sanitizer: NGWordSanitizer? by lazy {
            runCatching { CDI.current().select(NGWordSanitizer::class.java).get() }.getOrNull()
        }

        private val maskConfig: MaskConfig? by lazy {
            runCatching { CDI.current().select(MaskConfig::class.java).get() }.getOrNull()
        }
    }

    override fun serialize(value: String, gen: JsonGenerator, serializers: SerializerProvider) {
        val ngWordSanitizer = sanitizer
        val config = maskConfig

        if (config?.enabled == true && ngWordSanitizer != null) {
            gen.writeString(ngWordSanitizer.maskNGWords(value))
        } else {
            gen.writeString(value)
        }
    }
}
