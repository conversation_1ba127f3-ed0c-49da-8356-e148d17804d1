package jp.co.torihada.fanme.modules.fanme.services

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import jakarta.inject.Inject
import jakarta.json.JsonObject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.fanme.externals.client.salesforce.SalesforceClient
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.ExecuteQueryResponse
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.SalesforceRecord
import org.eclipse.microprofile.rest.client.inject.RestClient

abstract class SalesforceService {

    @Inject protected lateinit var config: Config
    @Inject @RestClient protected lateinit var salesforceClient: SalesforceClient

    protected fun getToken(): String {
        val response =
            salesforceClient.getToken(
                clientId = config.salesforceClientId(),
                clientSecret = config.salesforceClientSecret(),
                grantType = "client_credentials",
            )

        return response.accessToken
    }

    protected inline fun <reified T : SalesforceRecord> parseFirstRecord(json: JsonObject): T? {
        val mapper =
            ObjectMapper().registerKotlinModule().apply {
                configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            }

        val response = mapper.readValue<ExecuteQueryResponse>(json.toString())
        val firstRecordJson = response.records.firstOrNull()

        return mapper.convertValue(firstRecordJson, T::class.java)
    }

    protected inline fun <reified T : SalesforceRecord> parseRecords(json: JsonObject): List<T> {
        val mapper =
            ObjectMapper().registerKotlinModule().apply {
                configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            }

        val response = mapper.readValue<ExecuteQueryResponse>(json.toString())
        return response.records.map { mapper.convertValue(it, T::class.java) }
    }
}
