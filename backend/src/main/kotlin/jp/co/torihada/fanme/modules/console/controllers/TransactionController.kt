package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import io.quarkus.security.identity.SecurityIdentity
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.console.usecases.GetUserTransactions
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class TransactionController
@Inject
constructor(
    private val getUserTransactions: GetUserTransactions,
    private val securityIdentity: SecurityIdentity,
    private val util: Util,
) {

    fun getUserTransactions(userId: Long, odata: OData?): GetUserTransactions.Output {
        val currentUserUid =
            util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
        val currentUserRole =
            util.getCurrentUserRole(securityIdentity) ?: throw UnAuthorizedException()

        return getUserTransactions
            .execute(
                userId = userId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
                odata = odata,
            )
            .getOrThrow()
    }
}
