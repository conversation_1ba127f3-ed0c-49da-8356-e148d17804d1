-- Agency data
INSERT IGNORE INTO agencies (id, name, created_at, updated_at) VALUES 
(1, 'Agency A', NOW(), NOW());

-- Creator data (users table)
INSERT IGNORE INTO creators (id, uid, name, account_identity, gender, birthday, birthday_confirmed, purpose, is_public, allow_public_sharing, filled_profile, created_at, updated_at) VALUES 
(1, 'test-creator-1', 'Test Creator 1', 'test-creator-1', 'FEMALE', DATE_SUB(CURDATE(), INTERVAL 30 YEAR), false, 1, false, false, false, NOW(), NOW()),
(2, 'test-creator-2', 'Test Creator 2', 'test-creator-2', 'MALE', DATE_SUB(CURDATE(), INTERVAL 25 YEAR), false, 1, false, false, false, NOW(), NOW()),
(3, 'test-creator-3', 'Test Creator 3', 'test-creator-3', 'OTHER', DATE_SUB(CURDATE(), INTERVAL 22 YEAR), false, 2, true, false, false, NOW(), NOW()),
(4, 'test-creator-4', 'Test Creator 4', 'test-creator-4', 'FEMALE', DATE_SUB(CURDATE(), INTERVAL 28 YEAR), false, 3, true, true, false, NOW(), NOW()),
(5, 'test-creator-5', 'Test Creator 5', 'test-creator-5', 'MALE', DATE_SUB(CURDATE(), INTERVAL 35 YEAR), false, 2, true, false, true, NOW(), NOW());

-- Console users data
INSERT IGNORE INTO console_users (creator_id, agency_id, role, created_at, updated_at) VALUES 
(1, 1, 'SUPER', NOW(), NOW()),
(2, 1, 'AGENT', NOW(), NOW()),
(3, 1, 'CREATOR', NOW(), NOW()),
(4, 1, 'CREATOR', NOW(), NOW()),
(5, 1, 'CREATOR', NOW(), NOW());