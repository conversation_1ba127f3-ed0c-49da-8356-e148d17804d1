CREATE TABLE order_items (
                             id            bigint NOT NULL AUTO_INCREMENT,
                             order_id      bigint NOT NULL,
                             cart_id       bigint,
                             item_id       bigint NOT NULL,
                             quantity      int NOT NULL,
                             created_at    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             updated_at    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                             PRIMARY KEY (id),
                             FOREIGN KEY (order_id) REFERENCES orders(id),
                             FOREIGN KEY (cart_id) REFERENCES carts(id),
                             FOREIGN KEY (item_id) REFERENCES items(id)
);