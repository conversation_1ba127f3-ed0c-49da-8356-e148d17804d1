CREATE TABLE shop_item_type_margin_rates (
  id bigint NOT NULL AUTO_INCREMENT,
  shop_id bigint NOT NULL,
  item_type int NOT NULL COMMENT '0: DIGITAL_BUNDLE, 1: DIGITAL_GACHA, 2: CHEKI',
  margin_rate float NOT NULL CHECK (margin_rate >= 0.0 AND margin_rate <= 1.0),
  created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE (shop_id, item_type),
  FOREIGN KEY (shop_id) REFERENCES shops (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
