-- https://www.figma.com/board/dj1gl7uBFa45EjVIeHCOc4/FANME%E9%96%8B%E7%99%BA%E7%92%B0%E5%A2%83?node-id=106-243&t=909tM75MddD4Uklo-4
CREATE TABLE campaigns (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  campaign_identity VARCHAR(255) NOT NULL UNIQUE,
  title VARCHAR(255) NOT NULL,
  user_id BIGINT NULL,
  entry_type VARCHAR(50) NOT NULL,
  action_type VARCHAR(50) NOT NULL,
  action_duration_days INT NOT NULL DEFAULT 0,
  start_at DATETIME NOT NULL,
  end_at DATETIME NOT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
CREATE TABLE campaign_entries (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  campaign_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  entered_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_campaign_creator (campaign_id, user_id),
  KEY idx_campaign_id (campaign_id),
  KEY idx_user_id (user_id)
);

