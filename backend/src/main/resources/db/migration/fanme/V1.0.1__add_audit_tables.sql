CREATE TABLE `audit_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `audit_type` varchar(50) NOT NULL COMMENT '種別（SHOP,SHOP_ITEM,FANME_PROFILE,FANME_CONTENT,など）',
  `operation_type` varchar(50) NOT NULL COMMENT 'INSERT,UPDATE,DELETE',
  `metadata` text DEFAULT NULL COMMENT 'json形式（shop_id,item_id,title,description,など動的に入る）',
  `status` int NOT NULL COMMENT '0:未監査, -1:却下, 1:PENDING, 5:再提出, 9:承認',
  `comment` text COMMENT '監査コメント',
  `audited_at` datetime DEFAULT NULL COMMENT '監査実施日時',
  `audited_user_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY <PERSON>EY (`id`),
  KEY `idx_audit_groups_user_id` (`user_id`),
  KEY `idx_audit_groups_audit_type` (`audit_type`),
  KEY `idx_audit_groups_status` (`status`)
);

CREATE TABLE `audit_objects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `audit_group_id` bigint NOT NULL,
  `bucket` varchar(50) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `asset_type` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_audit_group_id` (`audit_group_id`)
);
