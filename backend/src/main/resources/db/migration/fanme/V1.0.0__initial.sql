CREATE TABLE IF NOT EXISTS `agencies` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `deleted_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_agencies_on_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ar_internal_metadata` (
    `key` varchar(255) NOT NULL,
    `value` varchar(255) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `creators` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `icon` varchar(255) DEFAULT NULL,
    `name` varchar(255) NOT NULL,
    `gender` varchar(255) NOT NULL DEFAULT 'BLANK',
    `birthday` date NOT NULL DEFAULT (curdate()),
    `birthday_confirmed` tinyint(1) NOT NULL DEFAULT '0',
    `is_birthday_week` int NOT NULL DEFAULT '0',
    `account_identity` varchar(255) NOT NULL,
    `is_public` tinyint(1) NOT NULL DEFAULT '1',
    `allow_public_sharing` tinyint(1) DEFAULT '0',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    `uid` varchar(255) DEFAULT NULL,
    `deleted_at` datetime(6) DEFAULT NULL,
    `filled_profile` tinyint(1) NOT NULL DEFAULT '1',
    `purpose` int NOT NULL DEFAULT '0',
    `last_accessed_at` datetime(6) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_creators_on_account_identity` (`account_identity`),
    UNIQUE KEY `index_creators_on_uid` (`uid`),
    KEY `index_creators_on_deleted_at` (`deleted_at`),
    KEY `index_creators_on_purpose` (`purpose`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `articles` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text,
    `image_url` varchar(255) DEFAULT NULL,
    `label` int NOT NULL DEFAULT '0',
    `priority` int NOT NULL DEFAULT '0',
    `content` text,
    `published_at` datetime(6) DEFAULT NULL,
    `expire_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_articles_on_label` (`label`),
    KEY `index_articles_on_published_at` (`published_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `article_reads` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `article_id` bigint NOT NULL,
    `creator_id` bigint NOT NULL,
    `viewed_at` datetime(6) NOT NULL DEFAULT (now()),
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_article_reads_on_article_id_and_creator_id` (`article_id`,`creator_id`),
    KEY `index_article_reads_on_article_id` (`article_id`),
    KEY `index_article_reads_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_b880fbc089` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`),
    CONSTRAINT `fk_rails_e10ed01b87` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `blogs` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `category` varchar(255) NOT NULL COMMENT '記事の種類（fanme_apps）',
    `image_url` text,
    `page_url` text,
    `description` text,
    `order` int NOT NULL COMMENT '表示順',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_type_order_uindex` (`category`,`order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `console_users` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `agency_id` bigint DEFAULT NULL,
    `role` varchar(255) NOT NULL,
    `deleted_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_console_users_on_creator_id` (`creator_id`),
    KEY `index_console_users_on_agency_id` (`agency_id`),
    KEY `index_console_users_on_deleted_at` (`deleted_at`),
    CONSTRAINT `fk_rails_8d8763ccde` FOREIGN KEY (`agency_id`) REFERENCES `agencies` (`id`),
    CONSTRAINT `fk_rails_adec9a2acd` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `content_block_details` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `icon` varchar(255) DEFAULT '',
    `title` varchar(255) NOT NULL DEFAULT '',
    `description` text,
    `app_description` text,
    `url` text,
    `style` json DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `content_block_types` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_content_block_types_on_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `content_blocks` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `content_block_type_id` bigint NOT NULL,
    `display_order_number` int NOT NULL,
    `displayable` tinyint(1) NOT NULL DEFAULT '1',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_content_blocks_on_creator_id_and_display_order_number` (`creator_id`,`display_order_number`),
    KEY `index_content_blocks_on_creator_id` (`creator_id`),
    KEY `index_content_blocks_on_content_block_type_id` (`content_block_type_id`),
    CONSTRAINT `fk_rails_0ae37fd669` FOREIGN KEY (`content_block_type_id`) REFERENCES `content_block_types` (`id`),
    CONSTRAINT `fk_rails_413be755a0` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `content_block_groups` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `content_block_id` bigint NOT NULL,
    `content_block_detail_id` bigint NOT NULL,
    `content_group_number` int NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `cb_groups_on_content_block_id_and_content_group_number` (`content_block_id`,`content_group_number`),
    UNIQUE KEY `content_block_groups_on_content_block_detail_id` (`content_block_detail_id`),
    KEY `index_content_block_groups_on_content_block_id` (`content_block_id`),
    KEY `index_content_block_groups_on_content_block_detail_id` (`content_block_detail_id`),
    CONSTRAINT `fk_rails_75473140b0` FOREIGN KEY (`content_block_id`) REFERENCES `content_blocks` (`id`),
    CONSTRAINT `fk_rails_d46fb853ac` FOREIGN KEY (`content_block_detail_id`) REFERENCES `content_block_details` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `creator_popups` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `enable` tinyint(1) NOT NULL DEFAULT '0',
    `title` varchar(255) DEFAULT NULL,
    `url` varchar(255) NOT NULL,
    `button_text` varchar(255) DEFAULT NULL,
    `image` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_creator_popups_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_fd0089b7c6` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `creator_states` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint unsigned DEFAULT NULL,
    `key` varchar(255) NOT NULL,
    `value` varchar(255) DEFAULT NULL,
    `data` text,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_creator_states_on_creator_id_and_key` (`creator_id`,`key`),
    KEY `index_creator_states_on_key_and_value` (`key`,`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `creator_tokens` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `id_token` text,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_creator_tokens_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_22ff32398c` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `creator_withdrawals` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `reason` int NOT NULL,
    `detail` text,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_creator_withdrawals_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_d21f82c7d1` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `docs` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(255) DEFAULT NULL,
    `slug` varchar(255) NOT NULL,
    `version` varchar(255) NOT NULL,
    `description` text,
    `content` longtext,
    `start_date` datetime(6) NOT NULL,
    `remind_before_date` int DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_docs_on_slug_and_version` (`slug`,`version`),
    KEY `index_docs_on_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `general_events` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `event_type` int NOT NULL DEFAULT '0',
    `prepare_at` datetime(6) NOT NULL,
    `start_at` datetime(6) NOT NULL,
    `end_at` datetime(6) NOT NULL,
    `archived_at` datetime(6) NOT NULL,
    `completed_at` datetime(6) DEFAULT NULL COMMENT '設定されたら開始済',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_general_events_on_creator_id` (`creator_id`),
    KEY `index_general_events_on_prepare_at` (`prepare_at`),
    KEY `index_general_events_on_start_at` (`start_at`),
    KEY `index_general_events_on_end_at` (`end_at`),
    KEY `index_general_events_on_archived_at` (`archived_at`),
    CONSTRAINT `fk_rails_8440e9306f` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `general_event_users` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `general_event_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `rank` int DEFAULT '0',
    `yell_count` int DEFAULT '0',
    `aggregation_at` datetime(6) DEFAULT NULL,
    `previous_rank` int DEFAULT '0',
    `previous_yell_count` int DEFAULT '0',
    `previous_aggregation_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_general_event_users_on_general_event_id` (`general_event_id`),
    KEY `index_general_event_users_on_user_id` (`user_id`),
    KEY `index_general_event_users_on_aggregation_at` (`aggregation_at`),
    KEY `index_general_event_users_on_previous_aggregation_at` (`previous_aggregation_at`),
    CONSTRAINT `fk_rails_bfc1605f71` FOREIGN KEY (`general_event_id`) REFERENCES `general_events` (`id`),
    CONSTRAINT `fk_rails_bfd67bdb17` FOREIGN KEY (`user_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `fcm_tokens` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `token` varchar(255) NOT NULL,
  `creator_uid` varchar(255) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_fcm_tokens_on_token` (`token`),
  KEY `index_fcm_tokens_on_creator_uid` (`creator_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `notification_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `enabled` tinyint(1) NOT NULL DEFAULT '0',
  `creator_uid` varchar(255) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `index_notification_settings_on_creator_uid` (`creator_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `lp_banners` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `navigation_url` text NOT NULL,
    `image_url` text NOT NULL,
    `order` int NOT NULL,
    `alt` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `lp_case_studies` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `navigation_url` text NOT NULL,
    `image_url` text NOT NULL,
    `order` int NOT NULL,
    `alt` varchar(255) NOT NULL,
    `name` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `notification_acceptances` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `category` varchar(255) NOT NULL,
    `accept_flg` tinyint(1) NOT NULL DEFAULT '1',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_notification_acceptances_on_creator_id_and_category` (`creator_id`,`category`),
    KEY `index_notification_acceptances_on_creator_id` (`creator_id`),
    KEY `index_notification_acceptances_on_category_and_accept_flg` (`category`,`accept_flg`),
    CONSTRAINT `fk_rails_f1083ffd9a` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `offline_events` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `description` text,
    `start_at` datetime(6) NOT NULL,
    `end_at` datetime(6) NOT NULL,
    `image_url` varchar(255) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `offline_event_creators` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `offline_event_id` bigint DEFAULT NULL,
    `creator_id` bigint DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_offline_event_creators_on_offline_event_id_and_creator_id` (`offline_event_id`,`creator_id`),
    KEY `index_offline_event_creators_on_offline_event_id` (`offline_event_id`),
    KEY `index_offline_event_creators_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_a7aa2b2e1b` FOREIGN KEY (`offline_event_id`) REFERENCES `offline_events` (`id`),
    CONSTRAINT `fk_rails_b34a47d817` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `profiles` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `bio` text NOT NULL,
    `header_image` varchar(255) DEFAULT NULL,
    `sns_link_color` varchar(255) NOT NULL DEFAULT 'ORIG',
    `official_flg` tinyint(1) NOT NULL DEFAULT '0',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_profiles_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_f21fafed25` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `profile_covers` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_id` bigint NOT NULL,
    `brightness` varchar(255) NOT NULL,
    `cover_visibility` tinyint(1) NOT NULL DEFAULT '1',
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_profile_covers_on_profile_id` (`profile_id`),
    CONSTRAINT `fk_rails_c89172d361` FOREIGN KEY (`profile_id`) REFERENCES `profiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `profile_cover_images` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_cover_id` bigint NOT NULL,
    `resource` varchar(255) NOT NULL,
    `resource_type` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_profile_cover_images_on_profile_cover_id` (`profile_cover_id`),
    CONSTRAINT `fk_rails_43510a5a4d` FOREIGN KEY (`profile_cover_id`) REFERENCES `profile_covers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `theme_colors` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `color` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `profile_theme_colors` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_id` bigint NOT NULL,
    `theme_color_id` bigint NOT NULL,
    `custom_color` varchar(255) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_profile_theme_colors_on_profile_id` (`profile_id`),
    KEY `index_profile_theme_colors_on_theme_color_id` (`theme_color_id`),
    CONSTRAINT `fk_rails_15bde89d69` FOREIGN KEY (`profile_id`) REFERENCES `profiles` (`id`),
    CONSTRAINT `fk_rails_bbb1c8da1b` FOREIGN KEY (`theme_color_id`) REFERENCES `theme_colors` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `displayable_cover_images` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_cover_id` bigint NOT NULL,
    `profile_cover_image_id` bigint NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_displayable_cover_images_on_profile_cover_id` (`profile_cover_id`),
    KEY `index_displayable_cover_images_on_profile_cover_image_id` (`profile_cover_image_id`),
    CONSTRAINT `fk_rails_70e8e6befd` FOREIGN KEY (`profile_cover_id`) REFERENCES `profile_covers` (`id`),
    CONSTRAINT `fk_rails_df18b30443` FOREIGN KEY (`profile_cover_image_id`) REFERENCES `profile_cover_images` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ranking_events` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `event_identity` varchar(255) NOT NULL,
    `name` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `image_url` varchar(255) NOT NULL,
    `base_color` varchar(255) NOT NULL DEFAULT '#B53599',
    `add_infos` json DEFAULT NULL COMMENT 'LPの追加情報群',
    `judge_x` varchar(255) DEFAULT NULL COMMENT '審査員のX URL',
    `judge_instagram` varchar(255) DEFAULT NULL COMMENT '審査員のInstagram URL',
    `share_hashtags` varchar(255) DEFAULT NULL COMMENT 'シェアハッシュタグ（カンマ区切り）',
    `results` varchar(255) DEFAULT NULL COMMENT '結果順位一覧',
    `apply_start_at` datetime(6) NOT NULL,
    `apply_end_at` datetime(6) NOT NULL,
    `start_at` datetime(6) NOT NULL,
    `end_at` datetime(6) NOT NULL,
    `calculated_at` datetime(6) NOT NULL,
    `archived_at` datetime(6) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_ranking_events_on_event_identity` (`event_identity`),
    KEY `index_ranking_events_on_name` (`name`),
    KEY `index_ranking_events_on_apply_start_at` (`apply_start_at`),
    KEY `index_ranking_events_on_apply_end_at` (`apply_end_at`),
    KEY `index_ranking_events_on_start_at` (`start_at`),
    KEY `index_ranking_events_on_end_at` (`end_at`),
    KEY `index_ranking_events_on_calculated_at` (`calculated_at`),
    KEY `index_ranking_events_on_archived_at` (`archived_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ranking_event_creators` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `ranking_event_id` bigint NOT NULL,
    `creator_id` bigint NOT NULL,
    `comment` text NOT NULL COMMENT '参加表明コメント',
    `status` int NOT NULL DEFAULT '0' COMMENT '0=参加申請, 9=参加中, -1=参加拒否',
    `is_first_participation` tinyint(1) NOT NULL DEFAULT '0',
    `rank` int DEFAULT NULL,
    `yell_count` int DEFAULT NULL,
    `aggregation_at` datetime(6) DEFAULT NULL,
    `previous_rank` int DEFAULT NULL,
    `previous_yell_count` int DEFAULT NULL,
    `previous_aggregation_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_ranking_creator` (`ranking_event_id`,`creator_id`),
    KEY `index_ranking_event_creators_on_ranking_event_id` (`ranking_event_id`),
    KEY `index_ranking_event_creators_on_creator_id` (`creator_id`),
    KEY `index_creator_status` (`creator_id`,`status`),
    CONSTRAINT `fk_rails_57a564ddbb` FOREIGN KEY (`ranking_event_id`) REFERENCES `ranking_events` (`id`),
    CONSTRAINT `fk_rails_f19238862a` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ranking_event_users` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `ranking_event_creator_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `rank` int DEFAULT '0',
    `yell_count` int DEFAULT '0',
    `aggregation_at` datetime(6) DEFAULT NULL,
    `previous_rank` int DEFAULT '0',
    `previous_yell_count` int DEFAULT '0',
    `previous_aggregation_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_ranking_event_users_on_ranking_event_creator_id` (`ranking_event_creator_id`),
    KEY `index_ranking_event_users_on_user_id` (`user_id`),
    KEY `index_ranking_event_users_on_aggregation_at` (`aggregation_at`),
    KEY `index_ranking_event_users_on_previous_aggregation_at` (`previous_aggregation_at`),
    CONSTRAINT `fk_rails_175a895521` FOREIGN KEY (`user_id`) REFERENCES `creators` (`id`),
    CONSTRAINT `fk_rails_6e0dbd1427` FOREIGN KEY (`ranking_event_creator_id`) REFERENCES `ranking_event_creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ranking_fan_badges` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `ranking_event_id` bigint NOT NULL,
    `rank` int NOT NULL DEFAULT '0',
    `badge_url` text NOT NULL,
    `badge_top_url` text,
    `image_url` text NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_ranking_fan_badges_on_ranking_event_id` (`ranking_event_id`),
    CONSTRAINT `fk_rails_bb4f3bfd5f` FOREIGN KEY (`ranking_event_id`) REFERENCES `ranking_events` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `ranking_yell_boosts` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `ranking_event_id` bigint DEFAULT NULL,
    `start_at` datetime(6) NOT NULL,
    `end_at` datetime(6) NOT NULL,
    `boost_ratio` decimal(10,0) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_ranking_yell_boosts_on_ranking_event_id` (`ranking_event_id`),
    KEY `index_ranking_yell_boosts_on_start_at` (`start_at`),
    KEY `index_ranking_yell_boosts_on_end_at` (`end_at`),
    CONSTRAINT `fk_rails_12bbf71e5f` FOREIGN KEY (`ranking_event_id`) REFERENCES `ranking_events` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `restricted_accounts` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `restrict_type` int NOT NULL DEFAULT '0',
    `start_at` datetime(6) DEFAULT NULL,
    `end_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_restricted_accounts_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_7fde6a39ac` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `shortened_urls` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `owner_id` bigint DEFAULT NULL,
    `owner_type` varchar(20) DEFAULT NULL,
    `url` text NOT NULL,
    `unique_key` varchar(10) NOT NULL,
    `use_count` int NOT NULL DEFAULT '0',
    `expires_at` datetime(6) DEFAULT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_shortened_urls_on_unique_key` (`unique_key`),
    KEY `index_shortened_urls_on_url` (`url`(767)),
    KEY `index_shortened_urls_on_owner_id_and_owner_type` (`owner_id`,`owner_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `sns_links` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_id` bigint NOT NULL,
    `type` varchar(255) NOT NULL,
    `account_identity` varchar(255) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_sns_links_on_profile_id_and_type` (`profile_id`,`type`),
    KEY `index_sns_links_on_profile_id` (`profile_id`),
    CONSTRAINT `fk_rails_93c38439da` FOREIGN KEY (`profile_id`) REFERENCES `profiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `sns_link_displays` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `profile_id` bigint NOT NULL,
    `sns_link_id` bigint NOT NULL,
    `display_order_number` int NOT NULL,
    `displayable` tinyint(1) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_sns_link_displays_on_profile_id_and_display_order_number` (`profile_id`,`display_order_number`),
    KEY `index_sns_link_displays_on_profile_id` (`profile_id`),
    KEY `index_sns_link_displays_on_sns_link_id` (`sns_link_id`),
    CONSTRAINT `fk_rails_636709bd7a` FOREIGN KEY (`profile_id`) REFERENCES `profiles` (`id`),
    CONSTRAINT `fk_rails_9704190f9f` FOREIGN KEY (`sns_link_id`) REFERENCES `sns_links` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_general_event_fan_badges` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `general_event_id` bigint NOT NULL,
    `rank` int NOT NULL DEFAULT '0',
    `user_id` bigint NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_user_general_event_fan_badges_on_general_event_id` (`general_event_id`),
    KEY `index_user_general_event_fan_badges_on_user_id` (`user_id`),
    CONSTRAINT `fk_rails_5b36cc4d46` FOREIGN KEY (`user_id`) REFERENCES `creators` (`id`),
    CONSTRAINT `fk_rails_ffc2b40ac6` FOREIGN KEY (`general_event_id`) REFERENCES `general_events` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_general_event_yell_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_id` bigint NOT NULL,
    `target_creator_id` bigint NOT NULL,
    `log_type` int NOT NULL DEFAULT '0' COMMENT '0=クリック, 1=購入, 2=シェア',
    `yell_count` int NOT NULL DEFAULT '1',
    `transaction_id` bigint DEFAULT NULL,
    `action_at` datetime(6) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_user_general_event_yell_logs_on_user_id` (`user_id`),
    KEY `index_user_general_event_yell_logs_on_target_creator_id` (`target_creator_id`),
    KEY `index_user_general_event_yell_logs_on_log_type` (`log_type`),
    KEY `index_user_general_event_yell_logs_on_transaction_id` (`transaction_id`),
    KEY `index_user_action_at` (`target_creator_id`,`action_at`),
    KEY `index_creator_action_at` (`user_id`,`action_at`),
    CONSTRAINT `fk_rails_54c1b97cab` FOREIGN KEY (`target_creator_id`) REFERENCES `creators` (`id`),
    CONSTRAINT `fk_rails_7ac9e4cb22` FOREIGN KEY (`user_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_ranking_fan_badges` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `ranking_fan_badge_id` bigint NOT NULL,
    `creator_id` bigint NOT NULL,
    `user_id` bigint NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_user_ranking_fan_badges_on_ranking_fan_badge_id` (`ranking_fan_badge_id`),
    KEY `index_user_ranking_fan_badges_on_creator_id` (`creator_id`),
    KEY `index_user_ranking_fan_badges_on_user_id` (`user_id`),
    CONSTRAINT `fk_rails_a92ca2b221` FOREIGN KEY (`ranking_fan_badge_id`) REFERENCES `ranking_fan_badges` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_tutorials` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `creator_id` bigint NOT NULL,
    `name` varchar(255) NOT NULL,
    `display_flg` tinyint(1) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_user_tutorials_on_creator_id_and_name` (`creator_id`,`name`),
    KEY `index_user_tutorials_on_creator_id` (`creator_id`),
    CONSTRAINT `fk_rails_f73783962f` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `user_yell_logs` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_id` bigint NOT NULL,
    `target_creator_id` bigint NOT NULL,
    `log_type` int NOT NULL DEFAULT '0' COMMENT '0=クリック, 1=購入, 2=シェア',
    `yell_count` int NOT NULL DEFAULT '1',
    `transaction_id` bigint DEFAULT NULL,
    `action_at` datetime(6) NOT NULL,
    `created_at` datetime(6) NOT NULL,
    `updated_at` datetime(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_user_yell_logs_on_user_id` (`user_id`),
    KEY `index_user_yell_logs_on_target_creator_id` (`target_creator_id`),
    KEY `index_user_yell_logs_on_log_type` (`log_type`),
    KEY `index_user_yell_logs_on_transaction_id` (`transaction_id`),
    KEY `index_user_action_at` (`target_creator_id`,`action_at`),
    KEY `index_creator_action_at` (`user_id`,`action_at`),
    KEY `index_creator_user_action_at` (`target_creator_id`,`user_id`,`action_at`),
    CONSTRAINT `fk_rails_87e4cbf767` FOREIGN KEY (`user_id`) REFERENCES `creators` (`id`),
    CONSTRAINT `fk_rails_b33b4f4450` FOREIGN KEY (`target_creator_id`) REFERENCES `creators` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

