package jp.co.torihada.fanme

import com.tngtech.archunit.core.domain.JavaClass
import com.tngtech.archunit.core.domain.JavaMethod
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import java.io.File
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class ParameterOrderingArchTest {
    private val basePackage = "jp.co.torihada.fanme"

    @Test
    fun `メソッドパラメータは非null→nullableの順序で配置されていること`() {
        val importedClasses =
            ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .withImportOption { location ->
                    // テスト関連のクラスを除外
                    !location.contains("/test/") &&
                        !location.contains("/tests/") &&
                        !location.contains("/mock/") &&
                        !location.contains("/mocks/") &&
                        !location.contains("/factories/") &&
                        !location.contains("/factory/")
                }
                .importPackages(basePackage)

        val violations = mutableListOf<ParameterOrderViolation>()
        var checkedMethods = 0

        importedClasses.forEach { javaClass ->
            javaClass.methods
                .filter { method ->
                    !method.isConstructor &&
                        !method.name.startsWith("get") && // getterを除外
                        !method.name.startsWith("set") && // setterを除外
                        method.parameters.size >= 2
                }
                .forEach { method ->
                    checkedMethods++
                    checkMethodParameterOrder(method, javaClass)?.let { violation ->
                        violations.add(violation)
                    }
                }
        }

        println("チェック完了: ${importedClasses.size}クラス, ${checkedMethods}メソッド")

        if (violations.isNotEmpty()) {
            val message = buildString {
                appendLine("\n❌ パラメータ順序の違反が検出されました (${violations.size}件):")
                appendLine("規則: 非nullパラメータ → nullableパラメータの順に配置してください\n")

                // 違反をクラスごとにグループ化
                violations
                    .groupBy { it.className }
                    .forEach { (className, classViolations) ->
                        appendLine("${className}:")
                        classViolations.forEach { violation ->
                            appendLine("  - ${violation.methodName}: ${violation.description}")
                        }
                    }

                appendLine("\n修正例:")
                appendLine("  誤: fun example(name: String?, id: Long, active: Boolean)")
                appendLine("  正: fun example(id: Long, active: Boolean, name: String?)")
            }
            fail(message)
        } else {
            println("✓ すべてのメソッドでパラメータ順序が正しく配置されています")
        }
    }

    private fun checkMethodParameterOrder(
        method: JavaMethod,
        javaClass: JavaClass,
    ): ParameterOrderViolation? {
        // Kotlinソースファイルを読み込んでnullability情報を取得
        val sourceFile = findKotlinSourceFile(javaClass)
        if (sourceFile == null || !sourceFile.exists()) {
            return null
        }

        val methodSignature = extractMethodSignature(sourceFile, method.name)
        if (methodSignature == null) {
            return null
        }

        val parameterTypes = parseParameterTypes(methodSignature)
        if (parameterTypes.size < 2) {
            return null
        }

        // nullable/non-nullableの順序をチェック
        var firstNullableIndex = -1
        val violations = mutableListOf<String>()

        parameterTypes.forEachIndexed { index, (paramName, isNullable) ->
            if (isNullable && firstNullableIndex == -1) {
                firstNullableIndex = index
            } else if (!isNullable && firstNullableIndex != -1) {
                violations.add(
                    "'$paramName' (非null) が '${parameterTypes[firstNullableIndex].first}' (nullable) の後に配置"
                )
            }
        }

        return if (violations.isNotEmpty()) {
            ParameterOrderViolation(
                className = javaClass.simpleName,
                methodName = method.name,
                description = violations.joinToString(", "),
            )
        } else {
            null
        }
    }

    private fun findKotlinSourceFile(javaClass: JavaClass): File? {
        val packagePath = javaClass.packageName.replace('.', '/')
        val fileName = javaClass.simpleName + ".kt"
        val possiblePaths =
            listOf("src/main/kotlin/$packagePath/$fileName", "src/main/java/$packagePath/$fileName")

        for (path in possiblePaths) {
            val file = File(path)
            if (file.exists()) {
                return file
            }
        }
        return null
    }

    private fun extractMethodSignature(sourceFile: File, methodName: String): String? {
        val content = sourceFile.readText()
        val regex = """fun\s+$methodName\s*\([^)]*\)""".toRegex(RegexOption.MULTILINE)
        return regex.find(content)?.value
    }

    private fun parseParameterTypes(methodSignature: String): List<Pair<String, Boolean>> {
        val parameterSection = methodSignature.substringAfter('(').substringBefore(')')
        if (parameterSection.isBlank()) return emptyList()

        return parameterSection.split(',').mapNotNull { param ->
            val trimmed = param.trim()
            if (trimmed.isBlank()) return@mapNotNull null

            val parts = trimmed.split(':')
            if (parts.size >= 2) {
                val paramName = parts[0].trim()
                val paramTypeAndDefault = parts[1].trim()
                val paramType = paramTypeAndDefault.substringBefore('=').trim()
                val isNullable = paramType.endsWith('?')
                paramName to isNullable
            } else {
                null
            }
        }
    }

    data class ParameterOrderViolation(
        val className: String,
        val methodName: String,
        val description: String,
    )
}
