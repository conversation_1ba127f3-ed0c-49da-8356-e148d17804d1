package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Benefit
import jp.co.torihada.fanme.modules.shop.models.BenefitCondition
import jp.co.torihada.fanme.modules.shop.models.Item

object BenefitFactory {
    fun new(
        itemId: Long,
        description: String = "特典の説明",
        conditionType: BenefitCondition = BenefitCondition.PURCHASED,
    ): Benefit {
        return Benefit().apply {
            this.item = Item.findById(itemId)!!
            this.description = description
            this.conditionType = conditionType
        }
    }
}
