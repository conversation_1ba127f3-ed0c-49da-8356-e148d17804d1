package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup

object ContentBlockGroupFactory {
    fun new(
        contentBlock: ContentBlock,
        contentBlockDetail: ContentBlockDetail,
        contentGroupNumber: Int = 1,
    ): ContentBlockGroup {
        return ContentBlockGroup().apply {
            this.contentBlock = contentBlock
            this.contentBlockDetail = contentBlockDetail
            this.contentGroupNumber = contentGroupNumber
        }
    }
}
