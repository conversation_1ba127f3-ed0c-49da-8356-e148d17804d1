package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.auth.MockExtAuthClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetTransferAmountTest {

    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig
    @Inject lateinit var config: Config
    @Inject lateinit var mockExtGmoTransferClient: MockExtGmoTransferClient
    @InjectMock @RestClient lateinit var extGmoTransferClient: ExtGmoTransferClient
    @InjectMock @RestClient lateinit var extAuthClient: ExtAuthClient
    @Inject lateinit var mockExtAuthClient: MockExtAuthClient
    @Inject lateinit var getTransferAmount: GetTransferAmount

    @BeforeEach
    fun setup() {
        mockExtGmoTransferClient.setUpMocks(extGmoTransferClient, config)
        mockExtAuthClient.setupMocks(extAuthClient, config)
    }

    @Test
    @TestTransaction
    fun `test get transfer amount success`() {
        val sellerUserId = "sellerUserId"

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = "sellerUserId",
                amount = 1000,
                accumulatedSales = 1000,
            )
            .apply { persist() }

        val response =
            getTransferAmount
                .execute(
                    GetTransferAmount.Input(userId = sellerUserId, idToken = "sampleAuthorization")
                )
                .getOrElse { throw Exception("Failed to get transfer amount") }

        assertEquals(response.identityVerified, true)
        assertEquals(response.balance, 1000)
        assertEquals(response.transferAmount, 560)
        assertEquals(response.totalAmount, 1000)
        assertEquals(response.transferFee, 440)
        assertEquals(response.checkBankId, true)
    }
}
