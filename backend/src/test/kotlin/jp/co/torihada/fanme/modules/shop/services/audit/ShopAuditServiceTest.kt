package jp.co.torihada.fanme.modules.shop.services.audit

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerAuditObject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerCreateInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.shop.factories.*
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService.OperationType
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.MockitoAnnotations

@QuarkusTest
@QuarkusTestResource(TestResourceLifecycleManager::class)
class ShopAuditServiceTest {
    @InjectMocks private lateinit var shopAuditService: ShopAuditService

    @Mock private lateinit var auditGroupController: AuditGroupController

    @Mock private lateinit var bucketPathResolver: BucketPathResolver

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    @TestTransaction
    fun `ヘッダー画像なしのショップの監査データが正しく作成されること`() {
        // Given
        val shop = ShopFactory.new()
        shop.persist()

        val input =
            AuditGroupControllerCreateInput(
                auditType = "shop",
                userUid = shop.creatorUid!!,
                operationType = OperationType.INSERT.value,
                metadata =
                    AuditGroupMetadata(
                        shopId = shop.id,
                        title = shop.name,
                        description = shop.description,
                    ),
                auditObjects = emptyList(),
            )
        Mockito.`when`(auditGroupController.createAuditGroup(input)).thenReturn(1L)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.INSERT)

        // Then
        Mockito.verify(auditGroupController, times(1)).createAuditGroup(input)
    }

    @Test
    @TestTransaction
    fun `ヘッダー画像ありのショップの監査データが正しく作成されること`() {
        // Given
        val url = "https://s3-domain.com/test-bucket/test-path/image.jpg"
        val shop = ShopFactory.new(headerImageUri = url)
        shop.persist()

        Mockito.`when`(bucketPathResolver.isValidPath(url)).thenReturn(true)
        Mockito.`when`(bucketPathResolver.getBucketName(url)).thenReturn("test-bucket")
        Mockito.`when`(bucketPathResolver.getFilePath(url)).thenReturn("test-path/image.jpg")

        val input =
            AuditGroupControllerCreateInput(
                auditType = "shop",
                userUid = shop.creatorUid!!,
                operationType = OperationType.UPDATE.value,
                metadata =
                    AuditGroupMetadata(
                        shopId = shop.id,
                        title = shop.name,
                        description = shop.description,
                    ),
                auditObjects =
                    listOf(
                        AuditGroupControllerAuditObject(
                            bucket = "test-bucket",
                            filePath = "test-path/image.jpg",
                            assetType = AssetType.IMAGE,
                        )
                    ),
            )
        Mockito.`when`(auditGroupController.createAuditGroup(input)).thenReturn(1L)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.UPDATE)

        // Then
        Mockito.verify(auditGroupController, times(1)).createAuditGroup(input)
    }

    @Test
    @TestTransaction
    fun `ヘッダー画像のURIが不正な場合でもエラーをスローせずに処理を継続すること`() {
        // Given
        val shop = ShopFactory.new(headerImageUri = "invalid-uri")
        shop.persist()

        Mockito.`when`(bucketPathResolver.isValidPath("invalid-uri")).thenReturn(false)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.INSERT)

        // Then
        // エラー時はskipするので、処理は通ってokとする
    }

    @Test
    @TestTransaction
    fun `基本的な商品の監査データが正しく作成されること`() {
        // Given
        val thumbnailUrl = "https://s3-domain.com/test-bucket/test-path/thumbnail.jpg"
        val fileUrlImage = "https://s3-domain.com/test-bucket/test-path/file1.png"
        val fileUrlVideo = "https://s3-domain.com/test-bucket/test-path/file2.mp3"
        val fileUrlMovie = "https://s3-domain.com/test-bucket/test-path/file3.mp4"
        val sampleUrl = "https://s3-domain.com/test-bucket/test-path/sample.mp4"
        val benefitFileUrl = "https://s3-domain.com/test-bucket/benefits/special.mp3"

        // S3パスのモック設定
        mockS3Paths(thumbnailUrl, "test-bucket", "test-path/thumbnail.jpg")
        mockS3Paths(fileUrlImage, "test-bucket", "test-path/file1.png")
        mockS3Paths(fileUrlVideo, "test-bucket", "test-path/file2.mp3")
        mockS3Paths(fileUrlMovie, "test-bucket", "test-path/file3.mp4")
        mockS3Paths(sampleUrl, "test-bucket", "test-path/sample.mp4")
        mockS3Paths(benefitFileUrl, "test-bucket", "benefits/special.mp3")

        val shop = ShopFactory.new()
        shop.persist()
        val item = ItemFactory.new(shopId = shop.id!!, thumbnailUri = thumbnailUrl)
        item.persist()
        val itemFiles =
            mutableSetOf(
                ItemFileFactory.new(
                    itemId = item.id!!,
                    objectUri = fileUrlImage,
                    fileType = "image",
                ),
                ItemFileFactory.new(
                    itemId = item.id!!,
                    objectUri = fileUrlVideo,
                    fileType = "audio",
                ),
                ItemFileFactory.new(
                    itemId = item.id!!,
                    objectUri = fileUrlMovie,
                    fileType = "video",
                ),
            )
        val sample =
            SampleFactory.new(itemId = item.id!!, objectUri = sampleUrl, fileType = "video")
        sample.persist()
        val benefit = BenefitFactory.new(itemId = item.id!!)
        benefit.persist()
        val benefitFile =
            BenefitFileFactory.new(
                benefitId = benefit.id!!,
                objectUri = benefitFileUrl,
                fileType = "audio",
            )
        shop.apply {
            item.files = itemFiles
            item.samples = mutableSetOf(sample)
            item.benefits = mutableSetOf(benefit.apply { files = mutableSetOf(benefitFile) })
        }
        shop.persist()

        val expectedAuditObjects =
            listOf(
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "test-path/thumbnail.jpg",
                    assetType = AssetType.IMAGE,
                ),
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "test-path/file1.png",
                    assetType = AssetType.IMAGE,
                ),
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "test-path/file2.mp3",
                    assetType = AssetType.VOICE,
                ),
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "test-path/file3.mp4",
                    assetType = AssetType.MOVIE,
                ),
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "test-path/sample.mp4",
                    assetType = AssetType.MOVIE,
                ),
                AuditGroupControllerAuditObject(
                    bucket = "test-bucket",
                    filePath = "benefits/special.mp3",
                    assetType = AssetType.VOICE,
                ),
            )

        val expectedInput =
            AuditGroupControllerCreateInput(
                auditType = "shop_item",
                userUid = shop.creatorUid!!,
                operationType = OperationType.INSERT.value,
                metadata =
                    AuditGroupMetadata(
                        shopId = shop.id!!,
                        itemId = item.id.toString(),
                        title = item.name,
                        description = item.description,
                    ),
                auditObjects = expectedAuditObjects,
            )

        Mockito.`when`(auditGroupController.createAuditGroup(expectedInput)).thenReturn(1L)

        // When
        shopAuditService.createAuditDataForShopItem(
            shop.creatorUid!!,
            OperationType.INSERT,
            item,
            item.files.toList(),
            item.samples.toList(),
            item.benefits.flatMap { it.files },
        )

        // Then
        Mockito.verify(auditGroupController, times(1)).createAuditGroup(expectedInput)
    }

    private fun mockS3Paths(url: String, bucket: String, filePath: String) {
        Mockito.`when`(bucketPathResolver.isValidPath(url)).thenReturn(true)
        Mockito.`when`(bucketPathResolver.getBucketName(url)).thenReturn(bucket)
        Mockito.`when`(bucketPathResolver.getFilePath(url)).thenReturn(filePath)
    }
}
