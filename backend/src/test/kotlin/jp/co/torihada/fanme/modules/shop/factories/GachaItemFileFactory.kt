package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.AwardType
import jp.co.torihada.fanme.modules.shop.models.GachaItemFile
import jp.co.torihada.fanme.modules.shop.models.ItemFile

object GachaItemFileFactory {
    fun new(
        itemFileId: Long,
        awardType: Int = AwardType.C.value,
        isSecret: Boolean = false,
    ): GachaItemFile {
        return GachaItemFile().apply {
            this.itemFile = ItemFile.findById(itemFileId)!!
            this.awardType = AwardType.fromValue(awardType)
            this.isSecret = isSecret
        }
    }
}
