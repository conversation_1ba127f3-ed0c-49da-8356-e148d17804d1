package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail

object ContentBlockDetailFactory {
    fun new(
        icon: String? = null,
        title: String = "Content Block Title",
        description: String? = "Content Block Description",
        appDescription: String? = "App Description",
        url: String? = "https://example.com",
        style: MutableMap<String, Any>? = mutableMapOf("color" to "blue"),
    ): ContentBlockDetail {
        return ContentBlockDetail().apply {
            this.icon = icon
            this.title = title
            this.description = description
            this.appDescription = appDescription
            this.url = url
            this.style = style
        }
    }
}
