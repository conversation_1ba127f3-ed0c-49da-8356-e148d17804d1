package jp.co.torihada.fanme.modules.shop.usecases.shop

import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class CreateShopTest {
    // CreateShopのテスト
    @Inject lateinit var controller: ShopController

    val creatorUid = "1234567890"
    val name = "test shop"
    val description = "test description"
    val headerImageUri = "https://example.com"
    val message = "test message"

    //    @Test
    //    @TestTransaction
    //    fun `test create shop`() {
    //        // 正常系
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = name,
    //                    description = description,
    //                    headerImageUri = headerImageUri,
    //                    message = message,
    //                )
    //            )
    //        } catch (e: Exception) {
    //            throw e
    //        }
    //
    //        val shop = Shop.findByCreatorUid(creatorUid)
    //        shop?.let {
    //            assert(it.name == name)
    //            assert(it.description == description)
    //            assert(it.headerImageUri == headerImageUri)
    //            assert(it.message == message)
    //            assert(it.marginRate == 0.265f)
    //            assert(it.isOpen)
    //        }
    //    }
    //
    //    @Test
    //    @TestTransaction
    //    fun `test create shop with exist creatorUid`() {
    //        val input =
    //            ShopRequest.CreateShop(
    //                creatorUid = creatorUid,
    //                name = name,
    //                description = description,
    //                headerImageUri = headerImageUri,
    //                message = message,
    //            )
    //        try {
    //            controller.createShop(input)
    //            controller.createShop(input)
    //        } catch (e: Exception) {
    //            assert(e is ResourceAlreadyExistsException)
    //            assert(e.message == "Shop already exists.")
    //        }
    //    }

    //    @Test
    //    @TestTransaction
    //    fun `test create shop invalid inputs`() {
    //        Shop.deleteAll()
    //        val validator = Validation.buildDefaultValidatorFactory().validator
    //        // creatorUid is empty
    //        try {
    //            val res: Set<ConstraintViolation<ShopRequest.CreateShop>> =
    //                validator.validate<ShopRequest.CreateShop>(
    //                    ShopRequest.CreateShop(
    //                        creatorUid = "",
    //                        name = name,
    //                        description = description,
    //                        headerImageUrl = headerImageUrl,
    //                        message = message,
    //                    )
    //                )
    //            assert(res.size == 1)
    //                controller.createShop(
    //                    ShopRequest.CreateShop(
    //                        creatorUid = "",
    //                        name = name,
    //                        description = description,
    //                        headerImageUrl = headerImageUrl,
    //                        message = message,
    //                    )
    //                )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == CREATOR_UID_IS_REQUIRED)
    //        }
    //
    //        // name is empty
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = "",
    //                    description = description,
    //                    headerImageUrl = headerImageUrl,
    //                    message = message,
    //                )
    //            )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == NAME_IS_REQUIRED)
    //        }
    //
    //        // name is over 30 characters
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = "a".repeat(31),
    //                    description = description,
    //                    headerImageUrl = headerImageUrl,
    //                    message = message,
    //                )
    //            )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == NAME_TOO_MANY_LENGTH_30)
    //        }
    //
    //        // description is over 500 characters
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = name,
    //                    description = "a".repeat(501),
    //                    headerImageUrl = headerImageUrl,
    //                    message = message,
    //                )
    //            )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == DESCRIPTION_TOO_MANY_LENGTH_500)
    //        }
    //
    //        // headerImageUrl is invalid
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = name,
    //                    description = description,
    //                    headerImageUrl = "invalid url",
    //                    message = message,
    //                )
    //            )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == HEADER_IMAGE_URL_IS_INVALID_URL)
    //        }
    //
    //        // message is over 100 characters
    //        try {
    //            controller.createShop(
    //                ShopRequest.CreateShop(
    //                    creatorUid = creatorUid,
    //                    name = name,
    //                    description = description,
    //                    headerImageUrl = headerImageUrl,
    //                    message = "a".repeat(101),
    //                )
    //            )
    //        } catch (e: Exception) {
    //            assert(e is ConstraintViolationException)
    //            assert(e.message == MESSAGE_TOO_MANY_LENGTH)
    //        }
    //    }
}
