package jp.co.torihada.fanme.utils

import kotlin.math.abs
import org.hamcrest.Description
import org.hamcrest.TypeSafeMatcher

class NumberCloseTo(private val expected: Double, private val error: Double) :
    TypeSafeMatcher<Number>() {
    override fun describeTo(description: Description) {
        description.appendText("a numeric value within <$error> of <$expected>")
    }

    override fun matchesSafely(item: Number): Boolean {
        val diff = abs(item.toDouble() - expected)
        return diff <= error
    }
}
