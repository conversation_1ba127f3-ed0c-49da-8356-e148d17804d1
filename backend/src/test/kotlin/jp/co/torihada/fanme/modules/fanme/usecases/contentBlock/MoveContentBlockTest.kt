package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

@QuarkusTest
class MoveContentBlockTest {

    @Inject lateinit var moveContentBlock: MoveContentBlock

    @Test
    @TestTransaction
    fun `test reorder content blocks by ids`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock1 =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 1,
                displayable = true,
            )
        contentBlock1.persistAndFlush()
        user.contentBlocks.add(contentBlock1)

        val contentBlock2 =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 2,
                displayable = true,
            )
        contentBlock2.persistAndFlush()
        user.contentBlocks.add(contentBlock2)

        val contentBlock3 =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 3,
                displayable = true,
            )
        contentBlock3.persistAndFlush()
        user.contentBlocks.add(contentBlock3)

        user.persistAndFlush()

        val input =
            MoveContentBlock.Input(
                user = user,
                contentBlockIds = listOf(contentBlock3.id!!, contentBlock1.id!!, contentBlock2.id!!),
            )
        moveContentBlock.execute(input).unwrap()

        val refreshedBlock1 = ContentBlock.findById(contentBlock1.id!!)
        val refreshedBlock2 = ContentBlock.findById(contentBlock2.id!!)
        val refreshedBlock3 = ContentBlock.findById(contentBlock3.id!!)

        assertEquals(2, refreshedBlock1?.displayOrderNumber)
        assertEquals(3, refreshedBlock2?.displayOrderNumber)
        assertEquals(1, refreshedBlock3?.displayOrderNumber)
    }
}
