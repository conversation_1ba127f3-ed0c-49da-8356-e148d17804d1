package jp.co.torihada.fanme.modules.console.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.models.User

object ConsoleUserFactory {
    fun new(
        creatorId: Long,
        agencyId: Long? = null,
        role: String = UserRole.CREATOR_VALUE,
        deletedAt: Instant? = null,
    ): ConsoleUser {
        return ConsoleUser().apply {
            this.user = User.findById(creatorId)!!
            this.agencyId = agencyId
            this.role = role
            this.deletedAt = deletedAt
        }
    }

    fun createTestUser(uid: String, name: String, email: String): User? {
        val count = User.count("uid = ?1", uid)
        if (count == 0L) {
            val user =
                User().apply {
                    this.uid = uid
                    this.name = name
                    this.accountIdentity = email
                    this.gender = "MALE"
                    this.birthday = Instant.now()
                    this.birthdayConfirmed = false
                    this.isPublic = true
                    this.filledProfile = true
                    this.purpose = 0
                }
            user.persistAndFlush()
            return user
        }

        return User.find("uid", uid).firstResult()
    }
}
