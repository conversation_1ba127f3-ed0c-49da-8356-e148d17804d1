package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetTransferAccountTest {

    @Inject lateinit var config: Config

    @InjectMock @RestClient lateinit var extGmoTransferClient: ExtGmoTransferClient

    @Inject lateinit var mockExtGmoTransferClient: MockExtGmoTransferClient

    @Inject lateinit var getTransferAccount: GetTransferAccount

    @BeforeEach
    fun setup() {
        mockExtGmoTransferClient.setUpMocks(extGmoTransferClient, config)
    }

    @Test
    @TestTransaction
    fun `test get transfer account success`() {
        val userId = "sellerUserId"

        val response =
            getTransferAccount.execute(GetTransferAccount.Input(userId = userId)).getOrElse {
                throw Exception("Failed to get transfer account")
            }

        assertEquals(response.deleteFlag, "0")
        assertEquals(response.bankName, "三菱東京UFJ銀行")
        assertEquals(response.bankCode, "001")
        assertEquals(response.branchName, "新宿支店")
        assertEquals(response.accountName, "トリハダジロウ")
        assertEquals(response.accountNumber, "1234567")
        assertEquals(response.branchCodeJpbank, "001")
        assertEquals(response.accountNumberJpbank, "1234567")
    }
}
