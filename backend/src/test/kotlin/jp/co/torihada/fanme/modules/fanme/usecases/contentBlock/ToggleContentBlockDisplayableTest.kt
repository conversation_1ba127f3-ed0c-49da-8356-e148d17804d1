package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

@QuarkusTest
class ToggleContentBlockDisplayableTest {

    @Inject lateinit var toggleContentBlockDisplayable: ToggleContentBlockDisplayable

    @Test
    @TestTransaction
    fun `test toggle content block displayable from true to false`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 1,
                displayable = true,
            )
        contentBlock.persistAndFlush()

        val input =
            ToggleContentBlockDisplayable.Input(
                contentBlockId = contentBlock.id!!,
                displayable = false,
            )

        val result = toggleContentBlockDisplayable.execute(input).unwrap()

        assertEquals(false, result.displayable)
    }
}
