package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class CreateContentBlockTest {

    @Inject private lateinit var createContentBlock: CreateContentBlock

    @Test
    @TestTransaction
    fun `test create content block`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlockType = Const.ContentBlockType.TWO_BLOCKS
        val input = CreateContentBlock.Input(contentBlockType = contentBlockType, user = user)

        val result = createContentBlock.execute(input)

        assertTrue(result.isOk)
        val contentBlock = result.unwrap()
        assertNotNull(contentBlock)
        assertEquals(user.id, contentBlock.user?.id)
        assertEquals(contentBlockType.id, contentBlock.contentBlockTypeId)
        assertEquals(1, contentBlock.displayOrderNumber)
        assertEquals(true, contentBlock.displayable)
        assertEquals(contentBlock.contentBlockGroups.size, 2)
        contentBlock.contentBlockGroups.forEach {
            assertNotNull(it)
            assertEquals(contentBlock, it.contentBlock)
            assertNotNull(it.contentBlockDetail)
            assertEquals(it.contentBlockDetail?.title, "")
            assertEquals(it.contentBlockDetail?.description, null)
            assertEquals(it.contentBlockDetail?.appDescription, null)
            assertEquals(it.contentBlockDetail?.url, null)
            assertEquals(it.contentBlockDetail?.icon, null)
        }
    }
}
