package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemFile

object ItemFileFactory {
    fun new(
        itemId: Long,
        name: String = "商品名",
        objectUri: String = "https://example.com/object.jpg",
        thumbnailUri: String = "https://example.com/thumbnail.jpg",
        price: Int? = null,
        fileType: String = "image",
        size: Float = 1.0f,
        duration: Int = 0,
        itemThumbnailSelected: Boolean = false,
        sortOrder: Int = 0,
    ): ItemFile {
        return ItemFile().apply {
            this.item = Item.findById(itemId)!!
            this.name = name
            this.objectUri = objectUri
            this.thumbnailUri = thumbnailUri
            this.price = price
            this.fileType = fileType
            this.size = size
            this.duration = duration
            this.itemThumbnailSelected = itemThumbnailSelected
            this.sortOrder = sortOrder
        }
    }
}
