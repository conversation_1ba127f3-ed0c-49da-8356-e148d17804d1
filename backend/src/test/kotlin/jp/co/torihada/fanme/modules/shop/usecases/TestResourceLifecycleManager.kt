package jp.co.torihada.fanme.modules.shop.usecases

import io.quarkus.test.common.QuarkusTestResourceLifecycleManager
import org.flywaydb.core.Flyway
import org.testcontainers.containers.MySQLContainer

// テスト全体のライフサイクルを記載してください
open class TestResourceLifecycleManager : QuarkusTestResourceLifecycleManager {
    private var mysqlContainer: MySQLContainer<Nothing>? = null

    override fun start(): MutableMap<String, String> {
        mysqlContainer = MySQLContainer<Nothing>("mysql:8.0").apply { startupAttempts = 1 }
        mysqlContainer!!.withDatabaseName("shop")
        mysqlContainer!!.start()

        // Flywayを使用してマイグレーションを実行
        val flyway =
            Flyway.configure()
                .dataSource(
                    mysqlContainer!!.jdbcUrl,
                    mysqlContainer!!.username,
                    mysqlContainer!!.password,
                )
                .locations("filesystem:src/main/resources/db/migration/shop") // マイグレーションファイルの場所を指定
                .load()

        // マイグレーションを実行
        flyway.migrate()

        return mutableMapOf(
            "quarkus.datasource.shop.jdbc.url" to mysqlContainer!!.jdbcUrl,
            "quarkus.datasource.shop.username" to mysqlContainer!!.username,
            "quarkus.datasource.shop.password" to mysqlContainer!!.password,
            "quarkus.hibernate-orm.shop.datasource" to "shop",
        )
    }

    override fun stop() {
        mysqlContainer!!.stop()
    }
}
