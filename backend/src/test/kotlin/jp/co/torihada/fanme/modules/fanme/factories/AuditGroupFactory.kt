package jp.co.torihada.fanme.modules.fanme.factories

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata

@ApplicationScoped
class AuditGroupFactory @Inject constructor() {
    companion object {
        fun new(
            userUid: String,
            auditType: AuditType,
            operationType: OperationType,
            metadata: AuditGroupMetadata,
            itemId: Long? = null,
            status: AuditStatus = AuditStatus.UNAUDITED,
        ): AuditGroup {
            return AuditGroup().apply {
                this.userUid = userUid
                this.auditType = auditType
                this.operationType = operationType
                this.metadata = jacksonObjectMapper().writeValueAsString(metadata)
                this.itemId = itemId
                this.status = status
            }
        }
    }
}
