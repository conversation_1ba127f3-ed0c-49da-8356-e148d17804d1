package jp.co.torihada.fanme

import java.io.File
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class ParameterOrderingSourceTest {
    private val baseDir = File("src/main/kotlin")

    @Test
    fun `Kotlinソースコードでパラメータ順序をチェック - 非null→nullableの順`() {
        val violations = mutableListOf<Violation>()
        var checkedFiles = 0
        var checkedMethods = 0

        baseDir
            .walkTopDown()
            .filter { file ->
                file.extension == "kt" &&
                    !file.path.contains("/test/") &&
                    !file.path.contains("/tests/") &&
                    !file.path.contains("/mock/") &&
                    !file.path.contains("/mocks/") &&
                    !file.path.contains("/factories/") &&
                    !file.path.contains("/factory/")
            }
            .forEach { file ->
                checkedFiles++
                val fileViolations = checkFileForParameterOrdering(file)
                checkedMethods += fileViolations.methodsChecked
                violations.addAll(fileViolations.violations)
            }

        println("チェック完了: ${checkedFiles}ファイル, ${checkedMethods}メソッド")

        if (violations.isNotEmpty()) {
            val message = buildString {
                appendLine("\n❌ パラメータ順序の違反が検出されました (${violations.size}件):")
                appendLine("規則: 非nullパラメータ → nullableパラメータの順に配置してください\n")

                violations
                    .groupBy { it.fileName }
                    .forEach { (fileName, fileViolations) ->
                        appendLine("$fileName:")
                        fileViolations.forEach { violation ->
                            appendLine("  ${violation.lineNumber}: ${violation.methodName}")
                            appendLine("    現在: ${violation.currentOrder}")
                            appendLine("    推奨: ${violation.suggestedOrder}")
                        }
                        appendLine()
                    }

                appendLine("修正方法:")
                appendLine("  誤: fun example(name: String?, id: Long)")
                appendLine("  正: fun example(id: Long, name: String?)")
            }
            fail(message)
        } else {
            println("✓ すべてのメソッドでパラメータ順序が正しく配置されています")
        }
    }

    private fun checkFileForParameterOrdering(file: File): FileCheckResult {
        val violations = mutableListOf<Violation>()
        val lines = file.readLines()
        var methodsChecked = 0

        // メソッド定義を探す正規表現
        val methodRegex =
            """^\s*(override\s+|open\s+|private\s+|protected\s+|internal\s+|public\s+)*fun\s+(\w+)\s*\(([^)]*)\)"""
                .toRegex()

        lines.forEachIndexed { index, line ->
            val match = methodRegex.find(line)
            if (match != null) {
                val isOverride = match.groups[1]?.value?.contains("override") == true
                val methodName = match.groups[2]?.value ?: ""
                val parametersSection = match.groups[3]?.value ?: ""

                // フレームワークのインターフェースをオーバーライドするメソッドは除外
                if (isOverride && methodName == "isValid") {
                    return@forEachIndexed
                }

                // 複数行にまたがるパラメータを処理
                val fullParameters =
                    if (line.contains(")")) {
                        parametersSection
                    } else {
                        // 次の行を読んで完全なパラメータリストを取得
                        val paramLines = mutableListOf(parametersSection)
                        var nextIndex = index + 1
                        while (nextIndex < lines.size && !lines[nextIndex].contains(")")) {
                            paramLines.add(lines[nextIndex].trim())
                            nextIndex++
                        }
                        if (nextIndex < lines.size) {
                            paramLines.add(lines[nextIndex].substringBefore(")").trim())
                        }
                        paramLines.joinToString(" ")
                    }

                val parameters = parseParameters(fullParameters)
                if (parameters.size >= 2) {
                    methodsChecked++
                    val violation =
                        checkParameterOrder(parameters, methodName, file.name, index + 1)
                    if (violation != null) {
                        violations.add(violation)
                    }
                }
            }
        }

        return FileCheckResult(violations, methodsChecked)
    }

    private fun parseParameters(parametersString: String): List<Parameter> {
        if (parametersString.isBlank()) return emptyList()

        return parametersString.split(',').mapNotNull { param ->
            val trimmed = param.trim()
            if (trimmed.isBlank()) return@mapNotNull null

            val colonIndex = trimmed.indexOf(':')
            if (colonIndex > 0) {
                val name = trimmed.substring(0, colonIndex).trim()
                val typeAndDefault = trimmed.substring(colonIndex + 1).trim()
                val type = typeAndDefault.substringBefore('=').trim()
                val isNullable = type.endsWith('?')
                Parameter(name, type, isNullable)
            } else {
                null
            }
        }
    }

    private fun checkParameterOrder(
        parameters: List<Parameter>,
        methodName: String,
        fileName: String,
        lineNumber: Int,
    ): Violation? {
        var firstNullableIndex = -1
        val violations = mutableListOf<String>()

        parameters.forEachIndexed { index, param ->
            if (param.isNullable && firstNullableIndex == -1) {
                firstNullableIndex = index
            } else if (!param.isNullable && firstNullableIndex != -1) {
                violations.add("'${param.name}' (非null) が nullable パラメータの後に配置")
            }
        }

        return if (violations.isNotEmpty()) {
            val currentOrder = parameters.joinToString(", ") { "${it.name}: ${it.type}" }
            val sortedParams = parameters.sortedBy { it.isNullable }
            val suggestedOrder = sortedParams.joinToString(", ") { "${it.name}: ${it.type}" }

            Violation(
                fileName = fileName,
                lineNumber = lineNumber,
                methodName = methodName,
                currentOrder = currentOrder,
                suggestedOrder = suggestedOrder,
                description = violations.joinToString(", "),
            )
        } else {
            null
        }
    }

    data class Parameter(val name: String, val type: String, val isNullable: Boolean)

    data class Violation(
        val fileName: String,
        val lineNumber: Int,
        val methodName: String,
        val currentOrder: String,
        val suggestedOrder: String,
        val description: String,
    )

    data class FileCheckResult(val violations: List<Violation>, val methodsChecked: Int)
}
