package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserToken

object UserTokenFactory {
    fun new(user: User, idToken: String): UserToken {
        return UserToken().apply {
            this.user = user
            this.idToken = idToken
        }
    }

    fun createTestUserToken(
        user: User,
        idToken: String = "test-id-token-${System.currentTimeMillis()}",
    ): UserToken {
        val userToken = new(user = user, idToken = idToken)
        user.token = userToken
        userToken.persistAndFlush()
        return userToken
    }
}
