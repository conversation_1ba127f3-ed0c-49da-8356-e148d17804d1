package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockDetailFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class DeleteContentBlockTest {

    @Inject private lateinit var deleteContentBlock: DeleteContentBlock

    @Test
    @TestTransaction
    fun `test delete content block successfully`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 1,
                displayable = true,
            )
        contentBlock.persistAndFlush()

        val contentBlockDetail1 =
            ContentBlockDetailFactory.new(
                title = "Test Content Block 1",
                description = "Test Description 1",
            )
        contentBlockDetail1.persistAndFlush()

        val contentBlockGroup1 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail1,
                contentGroupNumber = 1,
            )
        contentBlockGroup1.persistAndFlush()

        val contentBlockDetail2 =
            ContentBlockDetailFactory.new(
                title = "Test Content Block 2",
                description = "Test Description 2",
            )
        contentBlockDetail2.persistAndFlush()

        val contentBlockGroup2 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail2,
                contentGroupNumber = 2,
            )
        contentBlockGroup2.persistAndFlush()

        contentBlock.contentBlockGroups.add(contentBlockGroup1)
        contentBlock.contentBlockGroups.add(contentBlockGroup2)

        val foundContentBlock = ContentBlock.findById(contentBlock.id!!)
        assertNotNull(foundContentBlock)
        assertEquals(2, foundContentBlock!!.contentBlockGroups.size)

        val result =
            deleteContentBlock.execute(DeleteContentBlock.Input(contentBlockId = contentBlock.id!!))

        assertTrue(result.isOk)

        val deletedContentBlock = ContentBlock.findById(contentBlock.id!!)
        assertNull(deletedContentBlock)
    }
}
