package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.DisplayableCoverImagesFactory
import jp.co.torihada.fanme.modules.fanme.factories.ProfileCoverFactory
import jp.co.torihada.fanme.modules.fanme.factories.ProfileCoverImageFactory
import jp.co.torihada.fanme.modules.fanme.factories.ProfileFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class UpdateProfileCoverImageTest {

    @Inject private lateinit var updateProfileCoverImage: UpdateProfileCoverImage

    @Test
    @TestTransaction
    fun `execute updates profile cover image when user exists`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val profile =
            ProfileFactory.new(
                user = user,
                bio = "Test bio",
                headerImage = "header.jpg",
                snsLinkColor = "ORIG",
                officialFlg = false,
            )
        profile.persistAndFlush()

        val profileCover =
            ProfileCoverFactory.new(
                profile = profile,
                brightness = Const.Brightness.NORMAL.value,
                coverVisibility = true,
            )
        profileCover.persistAndFlush()

        val coverImage =
            ProfileCoverImageFactory.new(
                profileCover = profileCover,
                resource = "cover.jpg",
                resourceType = Const.CoverResourceType.PHOTO.value,
            )
        coverImage.persistAndFlush()

        val displayableCoverImage =
            DisplayableCoverImagesFactory.new(
                profileCover = profileCover,
                profileCoverImage = coverImage,
            )
        displayableCoverImage.persistAndFlush()
        profileCover.coverImage.add(coverImage)
        profile.cover = profileCover
        profile.cover?.displayableCoverImage = displayableCoverImage

        val newCoverImage = "new-cover.jpg"
        val newResourceType = Const.CoverResourceType.VIDEO.value

        val input =
            UpdateProfileCoverImage.Input(
                userId = user.id!!,
                coverImage = newCoverImage,
                resourceType = newResourceType,
            )
        val result = updateProfileCoverImage.execute(input)

        assertTrue(result.isOk)
        val updatedProfileCoverImage = result.unwrap()
        assertNotNull(updatedProfileCoverImage)
        assertEquals(profile.cover?.id, updatedProfileCoverImage.profileCover?.id)
        assertEquals(newCoverImage, updatedProfileCoverImage.resource)
        assertEquals(newResourceType, updatedProfileCoverImage.resourceType)

        assertNotNull(updatedProfileCoverImage.displayableCoverImage)
        assertEquals(
            profile.cover?.id,
            updatedProfileCoverImage.displayableCoverImage?.profileCover?.id,
        )
        assertEquals(
            updatedProfileCoverImage.id,
            updatedProfileCoverImage.displayableCoverImage?.profileCoverImage?.id,
        )
    }
}
