package jp.co.torihada.fanme.exception

import java.io.File
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ExceptionDefinitionRuleTest {

    @Test
    fun `ResourceNotFoundException以外でnot foundメッセージを持つ例外クラスが存在しないこと`() {
        val exceptionFile = File("src/main/kotlin/jp/co/torihada/fanme/exception/Exception.kt")
        val lines = exceptionFile.readLines()

        val violatingExceptions = mutableListOf<String>()
        var currentClassName: String? = null

        lines.forEach { line ->
            val classMatch = Regex("""class\s+(\w+)""").find(line)
            if (classMatch != null) {
                currentClassName = classMatch.groupValues[1]
            }

            if (
                currentClassName != null &&
                    currentClassName != "ResourceNotFoundException" &&
                    currentClassName != "ConsoleResourceNotFoundException"
            ) {
                if (line.contains("not found", ignoreCase = true)) {
                    violatingExceptions.add("$currentClassName: $line.trim()")
                }
            }
        }

        if (violatingExceptions.isNotEmpty()) {
            println("以下の例外クラスでnot foundメッセージが使用されています:")
            violatingExceptions.forEach { println("  - $it") }
            println("\nResourceNotFoundExceptionまたはConsoleResourceNotFoundExceptionを使用してください。")
        }

        assertEquals(0, violatingExceptions.size)
    }

    @Test
    fun `ResourceNotFoundExceptionが定義されていること`() {
        val exceptionFile = File("src/main/kotlin/jp/co/torihada/fanme/exception/Exception.kt")
        val content = exceptionFile.readText()

        assertTrue(content.contains("class ResourceNotFoundException"))
    }
}
