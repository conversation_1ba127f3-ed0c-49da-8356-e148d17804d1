package jp.co.torihada.fanme.modules.payment.mock.shop

import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.client.shop.ExtShopClient
import jp.co.torihada.fanme.modules.payment.externals.entity.shop.UpdateOrder
import org.mockito.Mockito

@ApplicationScoped
class MockExtShopClient {
    fun setupMocks(extShopClient: ExtShopClient) {
        Mockito.`when`(
                extShopClient.updateOrder(
                    authorization =
                        "Basic ZmFubWUuc2hvcC5hcGk6aHVpZVJFMTAyNDh7LWpkc0pERkxld2VmanNnYWRmYXNHZmRoamtsZmpkc2Zqa2w=",
                    UpdateOrder.Request(
                        transactionId = 1,
                        checkoutId = 1,
                        status = Const.TransactionStatus.Success.value,
                    ),
                )
            )
            .thenReturn(Response.ok().build())
    }
}
