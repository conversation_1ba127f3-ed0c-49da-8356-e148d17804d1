package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer

object SellerGmoTransferFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        sellerUserId: String? = null,
        depositId: String? = null,
        amount: Int = 0,
        fee: Int = 0,
        gmoFee: Int = 0,
        gmoUrl: String? = null,
        authCode: String? = null,
        status: String? = null,
        isForce: Boolean = false,
        detailCode: String? = null,
    ): SellerGmoTransfer {
        return SellerGmoTransfer().apply {
            this.id = id
            this.tenant = tenant
            this.sellerUserId = sellerUserId
            this.depositId = depositId
            this.amount = amount
            this.fee = fee
            this.gmoFee = gmoFee
            this.gmoUrl = gmoUrl
            this.authCode = authCode
            this.status = status
            this.isForce = isForce
            this.detailCode = detailCode
        }
    }
}
