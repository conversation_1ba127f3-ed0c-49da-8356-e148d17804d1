package jp.co.torihada.fanme.modules.fanme.usecases.audit

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.util.stream.Stream
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@QuarkusTest
class GetItemPublishLockedTest {
    @Inject lateinit var getItemPublishLocked: GetItemPublishLocked

    companion object {
        @JvmStatic
        fun testCases(): Stream<Arguments> =
            Stream.of(
                Arguments.of("却下ステータス", AuditStatus.REJECTED, true),
                Arguments.of("未監査ステータス", AuditStatus.UNAUDITED, false),
                Arguments.of("承認ステータス", AuditStatus.APPROVED, false),
                Arguments.of("PENDINGステータス", AuditStatus.PENDING, false),
                Arguments.of("再提出ステータス", AuditStatus.RESEND, false),
                Arguments.of("監査グループが存在しない", null, false),
            )
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @Transactional
    @DisplayName("監査ステータスに応じて正しい公開ロック状態を返すこと")
    fun testGetItemPublishLocked(
        testName: String,
        status: AuditStatus?,
        expectedIsPublishLocked: Boolean,
    ) {
        // Given
        val itemId = System.currentTimeMillis() // ユニークなIDを生成

        if (status != null) {
            val metadata = AuditGroupMetadata(itemId = itemId.toString(), title = "Test Item")
            val auditGroup =
                AuditGroupFactory.new(
                    userUid = "test-user",
                    auditType = AuditType.SHOP_ITEM,
                    operationType = OperationType.INSERT,
                    metadata = metadata,
                    itemId = itemId,
                    status = status,
                )
            auditGroup.persist()
        }

        // When
        val result = getItemPublishLocked.execute(itemId)

        // Then
        assertTrue(result.isOk)
        assertEquals(expectedIsPublishLocked, result.value)
    }

    @Test
    @Transactional
    @DisplayName("複数レコードのうち最新が却下ステータスの場合は公開不可（true）を返すこと")
    fun testLatestRejectedStatusReturnsPublishLockedTrue() {
        // Given
        val itemId = System.currentTimeMillis()
        val metadata1 = AuditGroupMetadata(itemId = itemId.toString(), title = "Test Item 1")
        val metadata2 = AuditGroupMetadata(itemId = itemId.toString(), title = "Test Item 2")
        val metadata3 = AuditGroupMetadata(itemId = itemId.toString(), title = "Test Item 3")

        // 1つ目: 承認済み
        val auditGroup1 =
            AuditGroupFactory.new(
                userUid = "test-user",
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.INSERT,
                metadata = metadata1,
                itemId = itemId,
                status = AuditStatus.APPROVED,
            )
        auditGroup1.persist()

        // 2つ目: 未監査
        val auditGroup2 =
            AuditGroupFactory.new(
                userUid = "test-user",
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.UPDATE,
                metadata = metadata2,
                itemId = itemId,
                status = AuditStatus.UNAUDITED,
            )
        auditGroup2.persist()

        // 3つ目: 最新・却下
        val auditGroup3 =
            AuditGroupFactory.new(
                userUid = "test-user",
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.UPDATE,
                metadata = metadata3,
                itemId = itemId,
                status = AuditStatus.REJECTED,
            )
        auditGroup3.persist()

        // When
        val result = getItemPublishLocked.execute(itemId)

        // Then
        assertTrue(result.isOk)
        assertTrue(result.value)
    }
}
