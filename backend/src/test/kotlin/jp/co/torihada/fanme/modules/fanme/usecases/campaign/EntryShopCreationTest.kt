package jp.co.torihada.fanme.modules.fanme.usecases.campaign

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.factories.CampaignFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.CampaignEntry
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

@QuarkusTest
class EntryShopCreationTest {
    @Inject lateinit var entryShopCreation: EntryShopCreation

    @Test
    @TestTransaction
    fun `SHOP_CREATION ショップ作成でキャンペーンIDが一致する場合はレコードが作成される`() {
        // Given
        val user = UserFactory.new(uid = "test-user-uuid")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "test-campaign-1",
                entryType = Campaign.EntryType.SHOP_CREATION,
                startAt = Instant.now().minusSeconds(60),
                endAt = Instant.now().plusSeconds(60),
            )
        campaign.persist()

        val before = CampaignEntry.Companion.listAll().size

        // When
        user.uid?.let { entryShopCreation.execute(it, campaign.campaignIdentity) }

        // Then
        val after = CampaignEntry.Companion.listAll().size
        Assertions.assertEquals(before + 1, after)
    }

    @Test
    @TestTransaction
    fun `SHOP_CREATION ショップ作成でキャンペーンIDが一致しない場合はレコードが作成されない`() {
        // Given
        val user = UserFactory.new(uid = "test-user-uuid")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "test-campaign-2",
                entryType = Campaign.EntryType.SHOP_CREATION,
                startAt = Instant.now().minusSeconds(60),
                endAt = Instant.now().plusSeconds(60),
            )
        campaign.persist()

        val before = CampaignEntry.Companion.listAll().size

        // When
        user.uid?.let { entryShopCreation.execute(it, "xxxxx") }

        // Then
        val after = CampaignEntry.Companion.listAll().size
        Assertions.assertEquals(before, after)
    }

    @Test
    @TestTransaction
    fun `SHOP_CREATION_ALL ショップ作成でキャンペーンIDに関係なくレコードが作成される`() {
        // Given
        val user = UserFactory.new(uid = "test-user-uuid")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "test-campaign-all",
                entryType = Campaign.EntryType.SHOP_CREATION_ALL,
                startAt = Instant.now().minusSeconds(60),
                endAt = Instant.now().plusSeconds(60),
            )
        campaign.persist()

        val before = CampaignEntry.Companion.listAll().size

        // When
        user.uid?.let { entryShopCreation.execute(it, "xxxxx") }

        // Then
        val after = CampaignEntry.Companion.listAll().size
        Assertions.assertEquals(before + 1, after)
    }

    @Test
    @TestTransaction
    fun `SHOP_CREATION_ALL 期間外のものはエントリーされない(過去)`() {
        // Given
        val user = UserFactory.new(uid = "test-user-uuid")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "test-campaign-all",
                entryType = Campaign.EntryType.SHOP_CREATION_ALL,
                startAt = Instant.now().minusSeconds(60),
                endAt = Instant.now().minusSeconds(20),
            )
        campaign.persist()

        val before = CampaignEntry.Companion.listAll().size

        // When
        user.uid?.let { entryShopCreation.execute(it, "xxxxx") }

        // Then
        val after = CampaignEntry.Companion.listAll().size
        Assertions.assertEquals(before, after)
    }

    @Test
    @TestTransaction
    fun `SHOP_CREATION_ALL 期間外のものはエントリーされない(未来)`() {
        // Given
        val user = UserFactory.new(uid = "test-user-uuid")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "test-campaign-all",
                entryType = Campaign.EntryType.SHOP_CREATION_ALL,
                startAt = Instant.now().plusSeconds(20),
                endAt = Instant.now().plusSeconds(60),
            )
        campaign.persist()

        val before = CampaignEntry.Companion.listAll().size

        // When
        user.uid?.let { entryShopCreation.execute(it, "xxxxx") }

        // Then
        val after = CampaignEntry.Companion.listAll().size
        Assertions.assertEquals(before, after)
    }
}
