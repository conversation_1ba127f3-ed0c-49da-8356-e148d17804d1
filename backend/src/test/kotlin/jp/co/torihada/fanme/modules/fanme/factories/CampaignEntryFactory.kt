package jp.co.torihada.fanme.modules.fanme.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import jp.co.torihada.fanme.modules.fanme.models.CampaignEntry
import jp.co.torihada.fanme.modules.fanme.models.User

object CampaignEntryFactory {
    fun new(campaign: Campaign, user: User, enteredAt: Instant = Instant.now()): CampaignEntry {
        val entry = CampaignEntry()
        entry.campaign = campaign
        entry.user = user
        entry.enteredAt = enteredAt
        return entry
    }
}
