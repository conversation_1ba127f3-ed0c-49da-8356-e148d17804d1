package jp.co.torihada.fanme.modules.fanme.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.models.Campaign

object CampaignFactory {
    fun new(
        campaignIdentity: String = "test-campaign",
        entryType: Campaign.EntryType = Campaign.EntryType.SHOP_CREATION,
        actionType: Campaign.ActionType = Campaign.ActionType.NONE,
        actionDurationDays: Int = 0,
        startAt: Instant = Instant.now().minusSeconds(60),
        endAt: Instant = Instant.now().plusSeconds(60),
    ): Campaign {
        return Campaign().apply {
            this.campaignIdentity = campaignIdentity
            this.entryType = entryType
            this.actionType = actionType
            this.actionDurationDays = actionDurationDays
            this.startAt = startAt
            this.endAt = endAt
        }
    }
}
