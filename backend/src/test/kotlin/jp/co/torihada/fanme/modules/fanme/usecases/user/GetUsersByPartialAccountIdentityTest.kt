package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.getOrThrow
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetPartialUsersByAccountIdentityTest {

    @Inject lateinit var getPartialUsersByAccountIdentity: GetUsersByPartialAccountIdentity

    private var testUser1Id: Long = 0
    private var testUser2Id: Long = 0
    private var testUser3Id: Long = 0
    private var deletedUserId: Long = 0

    @BeforeAll
    @Transactional
    fun setupAll() {
        val testUser1 =
            UserFactory.createTestUser(
                "partial-search-test-1",
                "Test User 1",
                "<EMAIL>",
            )!!
        testUser1Id = testUser1.id!!

        val testUser2 =
            UserFactory.createTestUser(
                "partial-search-test-2",
                "Test User 2",
                "<EMAIL>",
            )!!
        testUser2Id = testUser2.id!!

        val testUser3 =
            UserFactory.createTestUser("another-user-test", "Test User 3", "<EMAIL>")!!
        testUser3Id = testUser3.id!!

        val deletedUser =
            UserFactory.createTestUser(
                "partial-search-deleted",
                "Deleted User",
                "<EMAIL>",
            )!!
        deletedUserId = deletedUser.id!!
        deletedUser.deletedAt = java.time.Instant.now()
        deletedUser.persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        User.delete("uid LIKE ?1", "partial-search-%")
        User.delete("uid LIKE ?1", "another-user-%")
    }

    @Test
    @DisplayName("部分一致で複数のユーザーを検索できる")
    fun testPartialMatchReturnsMultipleUsers() {
        val result =
            getPartialUsersByAccountIdentity.execute(
                GetUsersByPartialAccountIdentity.Input(partialAccountIdentity = "partial-test")
            )

        val users = result.getOrThrow()
        assertEquals(2, users.size)
        assertTrue(users.any { it.id == testUser1Id })
        assertTrue(users.any { it.id == testUser2Id })
        assertFalse(users.any { it.id == testUser3Id })
    }

    @Test
    @DisplayName("完全一致でも検索できる")
    fun testExactMatchReturnsSingleUser() {
        val result =
            getPartialUsersByAccountIdentity.execute(
                GetUsersByPartialAccountIdentity.Input(
                    partialAccountIdentity = "<EMAIL>"
                )
            )

        val users = result.getOrThrow()
        assertEquals(1, users.size)
        assertEquals(testUser3Id, users.first().id)
    }

    @Test
    @DisplayName("一致するものがない場合は空のリストを返す")
    fun testNoMatchReturnsEmptyList() {
        val result =
            getPartialUsersByAccountIdentity.execute(
                GetUsersByPartialAccountIdentity.Input(
                    partialAccountIdentity = "non-existent-account"
                )
            )

        val users = result.getOrThrow()
        assertTrue(users.isEmpty())
    }

    @Test
    @DisplayName("削除されたユーザーは検索結果に含まれない")
    fun testDeletedUsersAreExcluded() {
        val result =
            getPartialUsersByAccountIdentity.execute(
                GetUsersByPartialAccountIdentity.Input(
                    partialAccountIdentity = "<EMAIL>"
                )
            )

        val users = result.getOrThrow()
        assertEquals(0, users.size)
        assertFalse(users.any { it.id == deletedUserId })
    }

    @Test
    @DisplayName("空文字列で検索すると全ての未削除ユーザーを返す")
    fun testEmptyStringReturnsAllUsers() {
        val result =
            getPartialUsersByAccountIdentity.execute(
                GetUsersByPartialAccountIdentity.Input(partialAccountIdentity = "")
            )

        val users = result.getOrThrow()
        assertTrue(users.size >= 3)
        assertTrue(users.any { it.id == testUser1Id })
        assertTrue(users.any { it.id == testUser2Id })
        assertTrue(users.any { it.id == testUser3Id })
        assertFalse(users.any { it.id == deletedUserId })
    }
}
