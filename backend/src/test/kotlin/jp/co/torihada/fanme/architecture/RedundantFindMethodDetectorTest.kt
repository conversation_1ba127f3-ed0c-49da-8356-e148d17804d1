package jp.co.torihada.fanme.architecture

import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToMany
import java.io.File
import kotlin.reflect.KClass
import kotlin.reflect.full.companionObject
import kotlin.reflect.full.declaredMemberFunctions
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.javaField
import kotlin.reflect.jvm.jvmErasure
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test

class RedundantFindMethodDetectorTest {

    private data class ManyToOneRelation(
        val fieldName: String,
        val parentClass: Class<*>,
        val mappedBy: String?,
    )

    private data class RedundantMethod(
        val module: String,
        val entityClass: String,
        val methodName: String,
        val parentClass: String,
        val mappedBy: String,
    )

    @Test
    fun `detects redundant find methods that can be replaced by JPA relations`() {
        val entityClasses = findEntityClassesFromSource()
        println("\n=== Scanning ${entityClasses.size} entity classes for redundant methods ===")

        val redundantMethods = entityClasses.flatMap(::findRedundantMethodsInEntity)

        if (redundantMethods.isNotEmpty()) {
            val report = buildFailureReport(redundantMethods)
            fail(report)
        } else {
            println("\nNo redundant find methods detected!")
        }
    }

    private fun findRedundantMethodsInEntity(entityClass: KClass<*>): List<RedundantMethod> {
        val redundantMethods = mutableListOf<RedundantMethod>()
        val manyToOneRelations = findManyToOneRelations(entityClass)

        for (relation in manyToOneRelations) {
            val findMethodName = "findBy${relation.parentClass.simpleName}Id"
            val companion = entityClass.companionObject ?: continue

            val hasRedundantMethod =
                companion.declaredMemberFunctions.any { it.name == findMethodName }
            if (!hasRedundantMethod) {
                continue
            }

            if (isMethodIgnored(entityClass, findMethodName)) {
                println(
                    "  Skipping ${entityClass.simpleName}.$findMethodName() - explicitly marked with '${Companion.IGNORE_COMMENT}'"
                )
                continue
            }

            val childPersistenceUnit = entityClass.java.getPersistenceUnit()
            val parentPersistenceUnit = relation.parentClass.getPersistenceUnit()
            if (childPersistenceUnit != parentPersistenceUnit) {
                println(
                    "  Skipping ${entityClass.simpleName}.$findMethodName() - entities are in different persistence units (Child: $childPersistenceUnit, Parent: $parentPersistenceUnit)"
                )
                continue
            }

            if (parentHasEagerlyFetchedOneToManyFor(relation.parentClass.kotlin, entityClass)) {
                redundantMethods.add(
                    RedundantMethod(
                        module = entityClass.java.getModuleName(),
                        entityClass = entityClass.simpleName ?: "Unknown",
                        methodName = findMethodName,
                        parentClass = relation.parentClass.simpleName,
                        mappedBy =
                            relation.mappedBy
                                ?: entityClass.simpleName?.replaceFirstChar { it.lowercase() } + "s",
                    )
                )
            }
        }
        return redundantMethods
    }

    private fun findManyToOneRelations(entityClass: KClass<*>): List<ManyToOneRelation> {
        return entityClass.memberProperties.mapNotNull { property ->
            property.javaField
                ?.takeIf { it.isAnnotationPresent(ManyToOne::class.java) }
                ?.let { field ->
                    ManyToOneRelation(
                        fieldName = property.name,
                        parentClass = field.type,
                        mappedBy = findMappedByInParent(field.type.kotlin, entityClass),
                    )
                }
        }
    }

    private fun findMappedByInParent(parentClass: KClass<*>, childClass: KClass<*>): String? {
        return parentClass.memberProperties
            .find { property ->
                val oneToMany = property.javaField?.getAnnotation(OneToMany::class.java)
                oneToMany != null &&
                    property.returnType.arguments.firstOrNull()?.type?.jvmErasure == childClass
            }
            ?.name
    }

    private fun parentHasEagerlyFetchedOneToManyFor(
        parentClass: KClass<*>,
        childClass: KClass<*>,
    ): Boolean {
        return parentClass.memberProperties.any { property ->
            property.javaField?.getAnnotation(OneToMany::class.java)?.let { oneToMany ->
                val isCorrectChildType =
                    property.returnType.arguments.firstOrNull()?.type?.jvmErasure == childClass
                isCorrectChildType && oneToMany.fetch == FetchType.EAGER
            } ?: false
        }
    }

    private fun findEntityClassesFromSource(): List<KClass<*>> {
        val entityClasses = mutableListOf<KClass<*>>()

        SOURCE_PATHS.map { File(it) }
            .filter { it.exists() }
            .forEach { baseDir ->
                println("Scanning for entities in: ${baseDir.absolutePath}")
                scanDirectoryForEntities(baseDir, entityClasses)
            }

        if (entityClasses.isEmpty()) {
            println("Warning: No entity classes found. Searched directories: $SOURCE_PATHS")
        }
        return entityClasses
    }

    private fun scanDirectoryForEntities(directory: File, entityClasses: MutableList<KClass<*>>) {
        directory
            .walkTopDown()
            .filter { it.isFile && it.extension == "kt" }
            .forEach { file ->
                val content = file.readText()
                if (content.contains("@Entity")) {
                    parseEntityClassFromFile(content)?.let { entityClasses.add(it) }
                }
            }
    }

    private fun parseEntityClassFromFile(content: String): KClass<*>? {
        val packageMatch = PACKAGE_REGEX.find(content)
        val classMatch = ENTITY_CLASS_REGEX.find(content)

        if (packageMatch != null && classMatch != null) {
            val packageName = packageMatch.groupValues[1]
            val className = classMatch.groupValues[1]
            val fullClassName = "$packageName.$className"

            return try {
                Class.forName(fullClassName).kotlin.takeIf {
                    it.java.isAnnotationPresent(Entity::class.java)
                }
            } catch (e: ClassNotFoundException) {
                println(
                    "Warning: Could not load class '$fullClassName'. It may have been moved or deleted."
                )
                null
            }
        }
        return null
    }

    private fun isMethodIgnored(entityClass: KClass<*>, methodName: String): Boolean {
        val packagePath = entityClass.java.packageName.replace('.', '/')
        val className = entityClass.simpleName
        val ignorePattern =
            ".*${Regex.escape(IGNORE_COMMENT)}.*\\s+fun\\s+$methodName.*"
                .toRegex(RegexOption.DOT_MATCHES_ALL)

        return SOURCE_PATHS.map { File("$it/$packagePath/$className.kt") }
            .firstOrNull { it.exists() }
            ?.let { ignorePattern.matches(it.readText()) } ?: false
    }

    private fun buildFailureReport(methods: List<RedundantMethod>): String {
        return buildString {
            appendLine("\n\n=== Redundant Find Methods Detected ===")
            appendLine(
                "The following static find methods can be removed and replaced with direct access to JPA relations."
            )

            methods
                .groupBy { it.module }
                .forEach { (module, moduleMethods) ->
                    appendLine("\n--- Module: $module ---")
                    moduleMethods.forEach { method ->
                        appendLine(
                            "In class `${method.entityClass}`, remove: `${method.methodName}()`"
                        )
                        appendLine(
                            "   Replace with: `${method.parentClass.replaceFirstChar { it.lowercase() }}.${method.mappedBy}`"
                        )
                    }
                }

            appendLine("\n\nTotal: ${methods.size} redundant methods found.")
            appendLine(
                "Recommendation: Removing these methods improves code clarity and leverages the persistence framework more effectively."
            )
        }
    }

    private fun Class<*>.getModuleName(): String {
        return when {
            packageName.contains(".modules.shop.") -> "shop"
            packageName.contains(".modules.fanme.") -> "fanme"
            packageName.contains(".modules.payment.") -> "payment"
            packageName.contains(".modules.console.") -> "console"
            else ->
                throw IllegalStateException(
                    "Unknown module for package '$packageName'. " +
                        "Please update `getModuleName()` in RedundantFindMethodDetectorTest to include it."
                )
        }
    }

    private fun Class<*>.getPersistenceUnit(): String {
        return when {
            packageName.contains(".modules.shop.") -> "shop"
            packageName.contains(".modules.fanme.") -> "fanme"
            packageName.contains(".modules.console.") -> "fanme"
            packageName.contains(".modules.payment.") -> "payment"
            else ->
                throw IllegalStateException(
                    "Unknown persistence unit for package '$packageName'. " +
                        "Please update `getPersistenceUnit()` in RedundantFindMethodDetectorTest to include it."
                )
        }
    }

    companion object {
        private const val IGNORE_COMMENT = "DISABLE RedundantFindMethodDetectorTest"

        private val SOURCE_PATHS =
            listOf(
                "src/main/kotlin/jp/co/torihada/fanme/modules",
                "backend/src/main/kotlin/jp/co/torihada/fanme/modules",
            )

        private val PACKAGE_REGEX = Regex("package ([\\w.]+)")
        private val ENTITY_CLASS_REGEX =
            Regex("@Entity\\s*(?:\\([^)]*\\))?[\\s\\S]*?class\\s+(\\w+)")
    }
}
