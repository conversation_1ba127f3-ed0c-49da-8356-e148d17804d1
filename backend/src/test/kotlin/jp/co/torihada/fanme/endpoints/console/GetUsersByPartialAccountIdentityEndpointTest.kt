package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.*
import org.hamcrest.Matchers.oneOf
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetUsersByPartialAccountIdentityEndpointTest {

    private var testUserId: Long = 0
    private var testUserAccountIdentity: String = ""
    private var testUser2Id: Long = 0
    private var testUser2AccountIdentity: String = ""
    private var deletedUserId: Long = 0
    private var deletedUserAccountIdentity: String = ""
    private var nonConsoleUserId: Long = 0
    private var nonConsoleUserAccountIdentity: String = ""

    @BeforeAll
    @Transactional
    fun setupAll() {
        val testUser =
            UserFactory.createTestUser(
                "search-test-user-1",
                "Search Test User",
                "<EMAIL>",
            )!!
        testUserId = testUser.id!!
        testUserAccountIdentity = testUser.accountIdentity!!

        val testUser2 =
            UserFactory.createTestUser(
                "search-test-user-2",
                "Search Test User 2",
                "<EMAIL>",
            )!!
        testUser2Id = testUser2.id!!
        testUser2AccountIdentity = testUser2.accountIdentity!!

        // Create deleted user
        val deletedUser =
            UserFactory.createTestUser(
                "search-deleted-user",
                "Deleted User",
                "<EMAIL>",
            )!!
        deletedUserId = deletedUser.id!!
        deletedUserAccountIdentity = deletedUser.accountIdentity!!
        deletedUser.deletedAt = java.time.Instant.now()
        deletedUser.persistAndFlush()

        // Create another user for testing
        val nonConsoleUser =
            UserFactory.createTestUser("another-user-test", "Another User", "<EMAIL>")!!
        nonConsoleUserId = nonConsoleUser.id!!
        nonConsoleUserAccountIdentity = nonConsoleUser.accountIdentity!!
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        User.delete("uid LIKE ?1", "search-%")
        User.delete("uid LIKE ?1", "another-user-%")
    }

    @Test
    @DisplayName("AGENTロールはアクセスできない")
    @TestSecurity(
        user = "test-agent",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent")],
    )
    fun agentCannotAccessEndpoint() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users?partial-account-identity=$testUserAccountIdentity")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("存在しないaccountIdentityで検索すると空のリストを返す")
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    fun getPartialNonExistentUsersReturnsEmptyList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users?partial-account-identity=non-existent-account-identity")
            .then()
            .statusCode(200)
            .body("data.usersByPartialAccountIdentityResponse.size()", equalTo(0))
    }

    @Test
    @DisplayName("部分一致で複数のユーザーを検索できる")
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    fun canGetPartialMatch() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users?partial-account-identity=search-test")
            .then()
            .statusCode(200)
            .body("data.usersByPartialAccountIdentityResponse.size()", equalTo(2))
            .body("data.usersByPartialAccountIdentityResponse[0]", notNullValue())
            .body("data.usersByPartialAccountIdentityResponse[1]", notNullValue())
            .body(
                "data.usersByPartialAccountIdentityResponse[0].id",
                oneOf(testUserId.toInt(), testUser2Id.toInt()),
            )
            .body(
                "data.usersByPartialAccountIdentityResponse[1].id",
                oneOf(testUserId.toInt(), testUser2Id.toInt()),
            )
    }

    @Test
    @DisplayName("空のpartial-account-identityパラメータで検索すると空のリストを返す")
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    fun getPartialWithEmptyParameterReturnsEmptyList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users?partial-account-identity=")
            .then()
            .statusCode(200)
            .body("data.usersByPartialAccountIdentityResponse.size()", equalTo(0))
    }
}
