package jp.co.torihada.fanme.endpoints.console

import io.quarkus.redis.datasource.RedisDataSource
import io.quarkus.redis.datasource.hash.HashCommands
import io.quarkus.redis.datasource.keys.KeyCommands
import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserToken
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProxyAccessTokenEndpointTest {

    @InjectMock lateinit var redisDataSource: RedisDataSource

    private var testUserId: Long = 0
    private var testAccountIdentity: String = "test-account-identity"

    private var nonExistentAccountIdentity: String = "non-existent-account"
    private var proxyUserId: Long = 0
    private var proxyUserAccountIdentity: String = "proxy-user-account-identity"
    private var bizUserId: Long = 0
    private var bizUserAccountIdentity: String = "biz-user-account-identity"
    private var otherCreatorAccountIdentity: String = "other-creator-account-identity"

    @BeforeAll
    @Transactional
    fun setupAll() {
        val testUser = UserFactory.createTestUser("target-user", "Target User", testAccountIdentity)
        testUserId = testUser?.id ?: throw RuntimeException("Failed to create test user")

        val userToken =
            UserToken().apply {
                user = testUser
                idToken = "test-token-for-target-user"
            }
        userToken.persist()

        val proxyUser =
            UserFactory.createTestUser("proxy-user-uid", "Proxy User", proxyUserAccountIdentity)
        proxyUserId = proxyUser?.id ?: throw RuntimeException("Failed to create proxy user")

        val bizUser = UserFactory.createTestUser("biz-user-uid", "Biz User", bizUserAccountIdentity)
        bizUserId = bizUser?.id ?: throw RuntimeException("Failed to create biz user")

        val otherCreator =
            UserFactory.createTestUser(
                "other-creator-uid",
                "Other Creator",
                otherCreatorAccountIdentity,
            )

        val otherCreatorToken =
            UserToken().apply {
                user = otherCreator
                idToken = "test-token-for-other-creator"
            }
        otherCreatorToken.persist()

        val testAgency = AgencyFactory.new(name = "Test Agency")
        testAgency.persist()

        val agentUserId =
            User.find("uid", "agent-user-uid").firstResult()?.id
                ?: UserFactory.createTestUser(
                        "agent-user-uid",
                        "Agent User",
                        "agent-user-account-identity",
                    )
                    ?.id
                ?: throw RuntimeException("Failed to create agent user")

        val agentConsoleUser =
            ConsoleUserFactory.new(
                creatorId = agentUserId,
                agencyId = testAgency.id,
                role = UserRole.AGENT_VALUE,
            )
        agentConsoleUser.persist()

        val targetConsoleUser =
            ConsoleUserFactory.new(
                creatorId = testUserId,
                agencyId = testAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
        targetConsoleUser.persist()
    }

    @AfterAll
    @Transactional
    fun tearDownAll() {
        UserToken.deleteAll()
        ConsoleUser.deleteAll()
        User.deleteAll()
        Agency.deleteAll()
    }

    private val savedRedisData = mutableMapOf<String, Map<String, String>>()

    @BeforeEach
    fun setUp() {
        savedRedisData.clear()

        val hashCommands = mock(HashCommands::class.java) as HashCommands<String, String, String>
        val keyCommands = mock(KeyCommands::class.java) as KeyCommands<String>

        `when`(redisDataSource.hash(String::class.java)).thenReturn(hashCommands)
        `when`(redisDataSource.key()).thenReturn(keyCommands)
        `when`(hashCommands.hset(anyString(), anyMap())).thenAnswer { invocation ->
            val key = invocation.getArgument<String>(0)
            val map = invocation.getArgument<Map<String, String>>(1)
            savedRedisData[key] = map
            null
        }
        `when`(hashCommands.hgetall(anyString())).thenAnswer { invocation ->
            val key = invocation.getArgument<String>(0)
            savedRedisData[key] ?: emptyMap<String, String>()
        }
        `when`(keyCommands.expire(anyString(), anyLong())).thenReturn(true)
        `when`(keyCommands.keys(anyString())).thenReturn(savedRedisData.keys.toList())
        `when`(keyCommands.del(anyString())).thenAnswer { invocation ->
            val key = invocation.getArgument<String>(0)
            savedRedisData.remove(key)
            1L
        }
    }

    @AfterEach
    fun tearDown() {
        savedRedisData.clear()
    }

    @Test
    @DisplayName("POST proxy-access-token - AGENT権限で自分の事務所に所属したクリエイターにアクセスできる")
    @TestSecurity(
        user = "agent-user",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-user-uid")],
    )
    fun postProxyAccessTokenWithAgentRoleForOwnCreator() {
        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .post("/console/users/$testAccountIdentity/proxy-access-token")
                .then()
                .statusCode(200)
                .extract()
                .response()

        val proxyAccessToken = response.jsonPath().getString("data.proxyAccessToken")

        assertNotNull(proxyAccessToken)

        val redisKey = "proxy:$proxyAccessToken"
        val hashCommands: HashCommands<String, String, String> =
            redisDataSource.hash(String::class.java)
        val savedData = hashCommands.hgetall(redisKey)

        assertNotNull(savedData)
        assertEquals(3, savedData.size)
        assertEquals("agent-user-account-identity", savedData["proxy_user_account_identity"])
        assertEquals(testAccountIdentity, savedData["target_user_account_identity"])
        assertNotNull(savedData["target_user_fanme_token"])
    }

    @Test
    @DisplayName("POST proxy-access-token - AGENT権限で所属していないクリエイターにはアクセスできない")
    @TestSecurity(
        user = "agent-user",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-user-uid")],
    )
    fun postProxyAccessTokenWithAgentRoleForOtherCreatorForbidden() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .post("/console/users/$otherCreatorAccountIdentity/proxy-access-token")
            .then()
            .statusCode(403)
    }
}
