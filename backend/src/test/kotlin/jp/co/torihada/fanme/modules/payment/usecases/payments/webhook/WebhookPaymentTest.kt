package jp.co.torihada.fanme.modules.payment.usecases.payments.webhook

import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.client.payment.ExtPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.client.shop.ExtShopClient
import jp.co.torihada.fanme.modules.payment.factories.CheckoutFactory
import jp.co.torihada.fanme.modules.payment.factories.TipFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.auth.MockExtAuthClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.mock.payment.MockExtPaymentClient
import jp.co.torihada.fanme.modules.payment.mock.shop.MockExtShopClient
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.SellerSale
import jp.co.torihada.fanme.modules.payment.models.TenantSale
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class WebhookPaymentTest {

    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig
    @Inject lateinit var mockExtGmoPaymentClient: MockExtGmoPaymentClient
    @Inject lateinit var webhookPayment: WebhookPayment
    @Inject lateinit var mockExtShopClient: MockExtShopClient
    @Inject lateinit var mockExtAuthClient: MockExtAuthClient
    @Inject lateinit var mockExtPaymentClient: MockExtPaymentClient

    @InjectMock @RestClient lateinit var extGmoPaymentClient: ExtGmoPaymentClient
    @InjectMock @RestClient lateinit var extShopClient: ExtShopClient
    @InjectMock @RestClient lateinit var extAuthClient: ExtAuthClient
    @InjectMock @RestClient lateinit var extPaymentClient: ExtPaymentClient

    @BeforeEach
    fun setUp() {
        mockExtGmoPaymentClient.setupMocks(
            extGmoPaymentClient,
            commonConfig,
            paymentConfig,
            "webhook",
        )
        mockExtShopClient.setupMocks(extShopClient)
        mockExtAuthClient.setupMocks(extAuthClient, paymentConfig)
        mockExtPaymentClient.setupMocks(extPaymentClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test webhook payment success`() {
        val paymentTermStr = "20241028235959" // 日付文字列（yyyyMMddHHmmss形式）
        val formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")

        // LocalDateTimeに変換し、Instantに変換する
        val paymentTermInstant =
            LocalDateTime.parse(paymentTermStr, formatter).toInstant(ZoneOffset.UTC)

        // setupデータ作成
        val tip =
            TipFactory.new(
                amount = 100,
                sellerUserId = "sellerUserId",
                purchaserUserId = "purchaserUserId",
            )
        tip.apply { persist() }

        CheckoutFactory.new(
                type = PaymentConst.PaymentType.CONVENIENCE.value,
                orderId = "sampleOrderId",
                accessId = "sampleAccessId",
                providerShopId = "testProviderShopId",
                tip = tip,
                accessPass = "sample",
                status = Const.CheckoutStatus.REQSUCCESS.value,
                amount = 1000,
                total = 1265,
                convenience = "セブンイレブン",
                confNo = "sampleConfNo",
                paymentTerm = paymentTermInstant,
                receiptUrl = "sampleReceiptUrl",
                gmoRequest = "sampleGmoRequest",
                cvsFee = 165,
                sellerUserId = "sellerUserId",
                purchaserUserId = "purchaserUserId",
                sellerSalesAmount = 720,
                tenantSalesAmount = 445,
                isShop = true,
            )
            .persist()

        webhookPayment.execute(
            WebhookPayment.Input(
                shopId = paymentConfig.gmoShopId(),
                shopPass = paymentConfig.gmoShopPass(),
                accessId = "sampleAccessId",
                accessPass = "sample",
                orderId = "sampleOrderId",
                status = "PAYSUCCESS",
                amount = "1100",
                tax = "0",
                payType = "1",
            )
        )

        val checkout = Checkout.findLast()
        val transaction = Transaction.findLast()
        val sellerSale = SellerSale.findLast()
        val tenantSale = TenantSale.findLast()

        checkout?.let { assertEquals(it.status, Const.CheckoutStatus.PAYSUCCESS.value) }
        transaction?.let {
            assertEquals(it.orderId, "sampleOrderId")
            assertEquals(it.amount, 1000)
            assertEquals(it.tip, checkout!!.tip)
            assertEquals(it.checkout, checkout)
            assertEquals(it.totalAmount, 1265)
            assertEquals(it.status, PaymentConst.TransactionStatus.Success.value)
        }

        sellerSale?.let {
            assertEquals(it.amount, 720)
            assertEquals(it.transaction, transaction)
        }

        tenantSale?.let {
            assertEquals(it.amount, 445)
            assertEquals(it.transaction, transaction)
        }
    }
}
