package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Benefit
import jp.co.torihada.fanme.modules.shop.models.BenefitFile

object BenefitFileFactory {
    fun new(
        benefitId: Long,
        name: String = "特典ファイル",
        objectUri: String = "https://example.com/benefit-file.pdf",
        thumbnailUri: String? = "https://example.com/benefit-file.webp",
        fileType: String = "document", // image, audio, video, document
        size: Float = 512.0f,
        duration: Int = 0,
    ): BenefitFile {
        return BenefitFile().apply {
            this.benefit = Benefit.findById(benefitId)!!
            this.name = name
            this.objectUri = objectUri
            this.thumbnailUri = thumbnailUri
            this.fileType = fileType
            this.size = size
            this.duration = duration
        }
    }
}
