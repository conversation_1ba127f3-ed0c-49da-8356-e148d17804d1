package jp.co.torihada.fanme.modules.fanme.usecases.campaign

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.factories.CampaignEntryFactory
import jp.co.torihada.fanme.modules.fanme.factories.CampaignFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.Campaign
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class HasActiveCampaignEntryTest {
    @Inject lateinit var hasActiveCampaignEntry: HasActiveCampaignEntry

    @Test
    @Transactional
    fun `エントリーが存在しない場合はfalseを返す`() {
        val user = UserFactory.new(uid = "campaign-test-user-uuid1")
        user.persist()

        val result = hasActiveCampaignEntry.execute(user.uid!!, Campaign.ActionType.MARGIN_TYPE1)
        assertTrue(result.isOk)
        assertEquals(false, result.value)
    }

    @Test
    @Transactional
    fun `エントリーが期間内に存在する場合はtrueを返す`() {
        val user = UserFactory.new(uid = "campaign-test-user-uuid2")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "campaign-identity1",
                entryType = Campaign.EntryType.SHOP_CREATION_ALL,
                actionType = Campaign.ActionType.MARGIN_TYPE1,
                actionDurationDays = 10,
            )
        campaign.persist()
        val entry =
            CampaignEntryFactory.new(
                user = user,
                campaign = campaign,
                enteredAt = Instant.now().minusSeconds(60 * 60 * 24 * 5),
            ) // 5日前
        entry.persist()

        val result = hasActiveCampaignEntry.execute(user.uid!!, Campaign.ActionType.MARGIN_TYPE1)
        assertTrue(result.isOk)
        assertEquals(true, result.value)
    }

    @Test
    @Transactional
    fun `エントリーが期間外の場合はfalseを返す`() {
        val user = UserFactory.new(uid = "campaign-test-user-uuid3")
        user.persist()
        val campaign =
            CampaignFactory.new(
                campaignIdentity = "campaign-identity2",
                entryType = Campaign.EntryType.SHOP_CREATION_ALL,
                actionType = Campaign.ActionType.MARGIN_TYPE1,
                actionDurationDays = 3,
            )
        campaign.persist()
        val entry =
            CampaignEntryFactory.new(
                user = user,
                campaign = campaign,
                enteredAt = Instant.now().minusSeconds(60 * 60 * 24 * 10),
            ) // 10日前
        entry.persist()

        val result = hasActiveCampaignEntry.execute(user.uid!!, Campaign.ActionType.MARGIN_TYPE1)
        assertTrue(result.isOk)
        assertEquals(false, result.value)
    }
}
