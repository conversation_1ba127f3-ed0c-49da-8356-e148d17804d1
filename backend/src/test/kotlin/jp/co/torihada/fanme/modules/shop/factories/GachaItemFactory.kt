package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.GachaItem
import jp.co.torihada.fanme.modules.shop.models.Item

object GachaItemFactory {
    fun new(itemId: Long, isDuplicated: Boolean = false): GachaItem {
        return GachaItem().apply {
            this.item = Item.findById(itemId)!!
            this.isDuplicated = isDuplicated
        }
    }
}
