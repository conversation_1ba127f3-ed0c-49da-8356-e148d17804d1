package jp.co.torihada.fanme.endpoints.console.shop

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.*
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.CreatorShopItemsController
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CreatorShopItemsUpdateEndpointTest {

    /** このテストでは DB に依存しないため、ユーザ / Shop / Item は単なる POJO として用意する。 */
    private lateinit var testAgency: Agency
    private lateinit var creator: User
    private lateinit var agentSameAgency: User
    private lateinit var agentDifferentAgency: User
    private lateinit var creatorAccountIdentity: String

    private val itemId: Long = 1L

    @InjectMock lateinit var controller: CreatorShopItemsController

    @BeforeAll
    fun setupAll() {
        initFanmeData()
    }

    @Transactional
    fun initFanmeData() {
        testAgency = AgencyFactory.new("Agency A").apply { persistAndFlush() }

        val superUser =
            UserFactory.createTestUser("test-super", "Super User", "<EMAIL>")!!
        ConsoleUserFactory.new(
                creatorId = superUser.id!!,
                agencyId = testAgency.id,
                role = UserRole.SUPER_VALUE,
            )
            .persistAndFlush()

        creator =
            UserFactory.createTestUser(
                "creator-update-test",
                "Creator User",
                "<EMAIL>",
            )!!
        creatorAccountIdentity = creator.accountIdentity!!
        ConsoleUserFactory.new(
                creatorId = creator.id!!,
                agencyId = testAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
            .persistAndFlush()

        agentSameAgency =
            UserFactory.createTestUser(
                "agent-same-update",
                "Agent Same",
                "<EMAIL>",
            )!!
        ConsoleUserFactory.new(
                creatorId = agentSameAgency.id!!,
                agencyId = testAgency.id,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()

        val differentAgency = AgencyFactory.new("Agency B").apply { persistAndFlush() }
        agentDifferentAgency =
            UserFactory.createTestUser(
                "agent-diff-update",
                "Agent Diff",
                "<EMAIL>",
            )!!
        ConsoleUserFactory.new(
                creatorId = agentDifferentAgency.id!!,
                agencyId = differentAgency.id,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()
    }

    @AfterAll
    fun cleanupAll() {
        cleanupFanmeData()
    }

    @Transactional
    fun cleanupFanmeData() {
        ConsoleUser.deleteAll()
        Agency.deleteAll()
        User.delete("uid LIKE ?1", "%update-test%")
        User.delete("uid LIKE ?1", "agent-same-update%")
        User.delete("uid LIKE ?1", "agent-diff-update%")
    }

    private fun requestBodyJson(updatedName: String): String =
        """
        {
            "name": "$updatedName",
            "description": "Updated Description",
            "thumbnailUri": "https://example.com/thumbnail-new.jpg",
            "thumbnailFrom": 0,
            "thumbnailBlurLevel": 0,
            "thumbnailWatermarkLevel": 1,
            "price": 200,
            "available": true,
            "itemFiles": [
                {
                    "name": "file1",
                    "objectUri": "https://example.com/file1.png",
                    "thumbnailUri": "https://example.com/thumb1.png",
                    "price": 200,
                    "fileType": "image",
                    "size": 1.0,
                    "duration": 0,
                    "itemThumbnailSelected": true,
                    "sortOrder": 0
                }
            ],
            "samples": null,
            "benefits": null,
            "tags": ["tag1"],
            "itemOption": {
                "isSingleSales": false,
                "qtyTotal": null,
                "qtyPerUser": null,
                "forSale": null,
                "password": null,
                "onSale": null
            },
            "itemType": 0
        }
        """
            .trimIndent()

    private fun buildRequest(name: String): CreateOrUpdateItemRequest =
        CreateOrUpdateItemRequest(
            name = name,
            description = "Updated Description",
            thumbnailUri = "https://example.com/thumbnail-new.jpg",
            thumbnailFrom = 0,
            thumbnailBlurLevel = 0,
            thumbnailWatermarkLevel = 1,
            price = 200,
            available = true,
            itemFiles =
                listOf(
                    DigitalBundleFile(
                        name = "file1",
                        objectUri = "https://example.com/file1.png",
                        thumbnailUri = "https://example.com/thumb1.png",
                        price = 200,
                        fileType = "image",
                        size = 1.0f,
                        duration = 0,
                        itemThumbnailSelected = true,
                        sortOrder = 0,
                    )
                ),
            samples = null,
            benefits = null,
            tags = listOf("tag1"),
            itemOption =
                ItemOption(
                    isSingleSales = false,
                    qtyTotal = null,
                    qtyPerUser = null,
                    forSale = null,
                    password = null,
                    onSale = null,
                ),
            itemType = 0,
        )

    @Test
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    fun superCanUpdateItem() {
        val updatedName = "SUPERが更新した商品名"

        val mockedShop =
            Shop().apply {
                id = 100L
                name = "ショップ名"
                creatorUid = creator.uid
            }

        val updatedItem =
            Item().apply {
                id = itemId
                shop = mockedShop
                name = updatedName
                description = "Updated Description"
                thumbnailUri = "https://example.com/thumbnail-new.jpg"
                thumbnailFrom = 0
                thumbnailBlurLevel = 0
                thumbnailWatermarkLevel = 1
                price = 200
                fileType = 0
                available = true
                itemType = ItemType.DIGITAL_BUNDLE
            }

        val requestObj = buildRequest(updatedName)

        `when`(
                controller.updateItem(
                    creatorAccountIdentity,
                    "test-super",
                    ConsoleUserRole.SUPER,
                    itemId,
                    requestObj,
                )
            )
            .thenReturn(updatedItem)

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBodyJson(updatedName))
            .`when`()
            .put("/console/creators/$creatorAccountIdentity/shop/items/$itemId")
            .then()
            .statusCode(200)
            .body("data.id", notNullValue())
            .body("data.name", equalTo(updatedName))
            .body("data.thumbnail_uri", equalTo("https://example.com/thumbnail-new.jpg"))
            .body("data.price", equalTo(200))

        verify(controller)
            .updateItem(
                creatorAccountIdentity,
                "test-super",
                ConsoleUserRole.SUPER,
                itemId,
                requestObj,
            )
    }

    @Test
    @TestSecurity(
        user = "agent-same-update",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-same-update")],
    )
    fun agentInSameAgencyCanUpdateItem() {
        val updatedName = "AGENT更新商品名"

        val mockedShop =
            Shop().apply {
                id = 100L
                name = "ショップ名"
                creatorUid = creator.uid
            }

        val updatedItem =
            Item().apply {
                id = itemId
                shop = mockedShop
                name = updatedName
                description = "Updated Description"
                thumbnailUri = "https://example.com/thumbnail-new.jpg"
                thumbnailFrom = 0
                thumbnailBlurLevel = 0
                thumbnailWatermarkLevel = 1
                price = 200
                fileType = 0
                available = true
                itemType = ItemType.DIGITAL_BUNDLE
            }

        val requestObj2 = buildRequest(updatedName)

        `when`(
                controller.updateItem(
                    creatorAccountIdentity,
                    "agent-same-update",
                    ConsoleUserRole.AGENT,
                    itemId,
                    requestObj2,
                )
            )
            .thenReturn(updatedItem)

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBodyJson(updatedName))
            .`when`()
            .put("/console/creators/$creatorAccountIdentity/shop/items/$itemId")
            .then()
            .statusCode(200)
            .body("data.name", equalTo(updatedName))

        verify(controller)
            .updateItem(
                creatorAccountIdentity,
                "agent-same-update",
                ConsoleUserRole.AGENT,
                itemId,
                requestObj2,
            )
    }

    @Test
    @TestSecurity(
        user = "agent-diff-update",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-diff-update")],
    )
    fun agentInDifferentAgencyCannotUpdateItem() {
        val updatedName = "SHOULD_FAIL"

        val requestObj3 = buildRequest(updatedName)

        `when`(
                controller.updateItem(
                    creatorAccountIdentity,
                    "agent-diff-update",
                    ConsoleUserRole.AGENT,
                    itemId,
                    requestObj3,
                )
            )
            .thenAnswer { throw ForbiddenAccessException() }

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBodyJson(updatedName))
            .`when`()
            .put("/console/creators/$creatorAccountIdentity/shop/items/$itemId")
            .then()
            .statusCode(403)
    }
}
