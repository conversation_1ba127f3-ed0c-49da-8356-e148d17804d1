package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.SellerAccountActivity
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer

object SellerAccountActivityFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        sellerUserId: String? = null,
        activityType: String? = null,
        activityCode: String? = null,
        yearMonth: String? = null,
        sellerGmoTransfer: SellerGmoTransfer? = null,
        amount: Int = 0,
        balance: Int = 0,
    ): SellerAccountActivity {
        return SellerAccountActivity().apply {
            this.id = id
            this.tenant = tenant
            this.sellerUserId = sellerUserId
            this.activityType = activityType
            this.activityCode = activityCode
            this.yearMonth = yearMonth
            this.sellerGmoTransfer = sellerGmoTransfer
            this.amount = amount
            this.balance = balance
        }
    }
}
