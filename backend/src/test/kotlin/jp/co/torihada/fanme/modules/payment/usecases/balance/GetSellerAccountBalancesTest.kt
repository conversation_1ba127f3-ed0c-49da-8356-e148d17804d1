package jp.co.torihada.fanme.modules.payment.usecases.balance

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetSellerAccountBalancesTest {

    @Inject lateinit var commonConfig: CommonConfig

    @Inject lateinit var config: Config

    @Inject lateinit var getSellerAccountBalances: GetSellerAccountBalances

    @Test
    @TestTransaction
    fun `test get balances for multiple seller IDs successfully`() {
        val sellerUserId1 = "sellerUserId1"
        val sellerUserId2 = "sellerUserId2"
        val sellerUserId3 = "sellerUserId3"

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                amount = 1000,
                accumulatedSales = 2000,
            )
            .apply { persist() }

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId2,
                amount = 500,
                accumulatedSales = 1500,
            )
            .apply { persist() }

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId3,
                amount = 300,
                accumulatedSales = 800,
            )
            .apply { persist() }

        val input =
            GetSellerAccountBalances.Input(
                sellerUserIds = listOf(sellerUserId1, sellerUserId2, sellerUserId3)
            )
        val result =
            getSellerAccountBalances.execute(input).getOrElse {
                throw Exception("Failed to get seller account balances")
            }

        assertEquals(3, result.balances.size)

        val seller1Balance = result.balances.find { it.sellerUserId == sellerUserId1 }
        val seller2Balance = result.balances.find { it.sellerUserId == sellerUserId2 }
        val seller3Balance = result.balances.find { it.sellerUserId == sellerUserId3 }

        assertEquals(sellerUserId1, seller1Balance?.sellerUserId)
        assertEquals(2000, seller1Balance?.accumulatedSales)
        assertEquals(1000, seller1Balance?.amount)

        assertEquals(sellerUserId2, seller2Balance?.sellerUserId)
        assertEquals(1500, seller2Balance?.accumulatedSales)
        assertEquals(500, seller2Balance?.amount)

        assertEquals(sellerUserId3, seller3Balance?.sellerUserId)
        assertEquals(800, seller3Balance?.accumulatedSales)
        assertEquals(300, seller3Balance?.amount)
    }

    @Test
    @TestTransaction
    fun `test get balances with empty seller IDs list returns empty result`() {
        val input = GetSellerAccountBalances.Input(sellerUserIds = emptyList())
        val result =
            getSellerAccountBalances.execute(input).getOrElse {
                throw Exception("Failed to get seller account balances")
            }

        assertEquals(0, result.balances.size)
    }

    @Test
    @TestTransaction
    fun `test get balances with non-existent seller IDs returns empty result for those IDs`() {
        val existingSellerUserId = "existingSellerUserId"
        val nonExistingSellerUserId = "nonExistingSellerUserId"

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = existingSellerUserId,
                amount = 1000,
                accumulatedSales = 2000,
            )
            .apply { persist() }

        val input =
            GetSellerAccountBalances.Input(
                sellerUserIds = listOf(existingSellerUserId, nonExistingSellerUserId)
            )
        val result =
            getSellerAccountBalances.execute(input).getOrElse {
                throw Exception("Failed to get seller account balances")
            }

        assertEquals(1, result.balances.size)
        assertEquals(existingSellerUserId, result.balances[0].sellerUserId)
        assertEquals(2000, result.balances[0].accumulatedSales)
        assertEquals(1000, result.balances[0].amount)
    }

    @Test
    @TestTransaction
    fun `test get balances handles zero accumulatedSales correctly`() {
        val sellerUserId = "sellerWithZeroSales"

        val sellerBalance =
            SellerAccountBalanceFactory.new(
                    tenant = commonConfig.tenant(),
                    sellerUserId = sellerUserId,
                    amount = 500,
                    accumulatedSales = 0,
                )
                .apply { persist() }

        val input = GetSellerAccountBalances.Input(sellerUserIds = listOf(sellerUserId))
        val result =
            getSellerAccountBalances.execute(input).getOrElse {
                throw Exception("Failed to get seller account balances")
            }

        assertEquals(1, result.balances.size)
        assertEquals(sellerUserId, result.balances[0].sellerUserId)
        assertEquals(0, result.balances[0].accumulatedSales)
        assertEquals(500, result.balances[0].amount)
    }
}
