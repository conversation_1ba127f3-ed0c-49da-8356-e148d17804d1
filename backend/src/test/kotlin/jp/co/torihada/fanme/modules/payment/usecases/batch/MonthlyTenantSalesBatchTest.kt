package jp.co.torihada.fanme.modules.payment.usecases.batch

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.batch.usecases.MonthlyTenantSalesBatch
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.factories.SellerSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.TenantSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.TransactionFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.models.MonthlyTenantSale
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class MonthlyTenantSalesBatchTest {

    @Inject lateinit var config: Config
    @Inject lateinit var monthlyTenantSalesBatch: MonthlyTenantSalesBatch

    @Test
    @TestTransaction
    fun `test monthly tenant sales batch`() {

        val yearMonth = "202401"

        // DBの準備
        val transaction =
            TransactionFactory.new(
                tenant = config.tenant(),
                sellerUserId = "sellerUserId",
                status = Const.TransactionStatus.Success.value,
                amount = 1000,
                totalAmount = 1000,
                purchaserUserId = "purchaserUserId",
                orderedAt = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )

        transaction.createdAt = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC)

        transaction.persist()

        TenantSaleFactory.new(tenant = config.tenant(), amount = 265, transaction = transaction)
            .persist()

        SellerSaleFactory.new(
                tenant = config.tenant(),
                sellerUserId = "sellerUserId",
                amount = 735,
                transaction = transaction,
            )
            .persist()

        monthlyTenantSalesBatch.execute(yearMonth)

        // DBの確認
        val monthlySellerSale = MonthlyTenantSale.findByTenantAndYearMonth("fanme", yearMonth)
        monthlySellerSale?.let {
            assertEquals(it.tenant, config.tenant())
            assertEquals(it.yearMonth, yearMonth)
            assertEquals(it.transactionAmount, 1000)
            assertEquals(it.miniappSalesAmount, 265)
        }
    }
}
