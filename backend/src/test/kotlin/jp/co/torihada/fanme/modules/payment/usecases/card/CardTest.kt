package jp.co.torihada.fanme.modules.payment.usecases.card

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoCardClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoMemberClient
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoCardClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtMemberClient
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class CardTest {
    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig

    @InjectMock @RestClient lateinit var extGmoCardClient: ExtGmoCardClient

    @InjectMock @RestClient lateinit var extGmoMemberClient: ExtGmoMemberClient

    @Inject lateinit var mockExtGmoCardClient: MockExtGmoCardClient

    @Inject lateinit var mockExtGmoMemberClient: MockExtMemberClient

    @Inject lateinit var deleteCard: DeleteCard

    @Inject lateinit var fetchCard: FetchCard

    @Inject lateinit var registerCard: RegisterCard

    @Inject lateinit var updateCard: UpdateCard

    @BeforeEach
    fun setUp() {
        mockExtGmoCardClient.setupMocks(extGmoCardClient, commonConfig, paymentConfig)
        mockExtGmoMemberClient.setupMocks(extGmoMemberClient, paymentConfig)
    }

    @Test
    fun `test saved card success`() {
        val response =
            registerCard
                .execute(
                    RegisterCard.Input(
                        userId = "sampleUserId",
                        cardName = "sampleCardName",
                        token = "sampleToken",
                    )
                )
                .getOrElse { throw Exception("Failed to register card") }

        assertEquals(RegisterCard.Card(cardNo = "sampleCardNo", cardSeq = 1), response)
    }

    @Test
    fun `test delete card success`() {
        val response =
            deleteCard
                .execute(DeleteCard.Input(userId = "sampleUserId", cardSequence = 0))
                .getOrElse { throw Exception("Failed to delete card") }

        assertEquals(true, response.success)
    }

    @Test
    fun `test fetch card success`() {
        val response =
            fetchCard.execute(userId = "sampleUserId").getOrElse {
                throw Exception("Failed to fetch card")
            }

        assertEquals(
            listOf(
                FetchCard.Card(
                    cardSequence = 0,
                    defaultFlag = "0",
                    name = "sampleCardName",
                    number = "sampleCardNo",
                    expiresAt = "2300-01-01",
                    holderName = "sampleCardHolderName",
                    brand = "sampleCardBrand",
                    lastUsed = false,
                )
            ),
            response,
        )
    }

    @Test
    fun `test update card success`() {
        val response =
            updateCard
                .execute(
                    UpdateCard.Input(
                        userId = "sampleUserId",
                        cardSequence = 0,
                        cardName = "sampleCardName",
                        expire = "2400-01-01",
                        cardHolderName = "sampleCardHolderName",
                    )
                )
                .getOrElse { throw Exception("Failed to update card") }

        assertEquals(true, response.success)
    }
}
