package jp.co.torihada.fanme

import java.io.File
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CodeStyleTest {

    @Test
    fun `プロジェクト全体でフルパッケージ名を使用していないこと`() {
        val sourceDir = File("src/main/kotlin")
        val violations = mutableListOf<String>()

        fun checkFile(file: File) {
            if (file.extension != "kt") return

            val content = file.readText()
            val lines = file.readLines()

            // ファイル全体から完全修飾名を検出
            val fullQualifiedPattern =
                Regex(
                    """(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]+\.[A-Z][a-zA-Z0-9]+)"""
                )

            fullQualifiedPattern.findAll(content).forEach { match ->
                val fullName = match.value
                val startIndex = match.range.first

                // 行番号を計算
                var lineNumber = 1
                var charCount = 0
                for (i in lines.indices) {
                    charCount += lines[i].length + 1 // +1 for newline
                    if (charCount > startIndex) {
                        lineNumber = i + 1
                        break
                    }
                }

                // import文やpackage文の中でないことを確認
                val lineContent = lines.getOrNull(lineNumber - 1) ?: ""
                if (
                    !lineContent.trimStart().startsWith("import ") &&
                        !lineContent.trimStart().startsWith("package ")
                ) {
                    // 修正方法を提案
                    val parts = fullName.split(".")
                    val className = parts.takeLast(2).joinToString(".")
                    val packagePath = parts.dropLast(2).joinToString(".")

                    violations.add(
                        "${file.relativeTo(sourceDir).path}:$lineNumber: フルパッケージ名 '$fullName' が使用されています\n" +
                            "  修正方法: \n" +
                            "    1. import文に追加: import $fullName\n" +
                            "    2. コード内で使用: $className"
                    )
                }
            }

            // 既存のパターンマッチングも残す
            lines.forEachIndexed { index, line ->
                // import文とpackage文はスキップ
                if (
                    line.trimStart().startsWith("import ") ||
                        line.trimStart().startsWith("package ")
                ) {
                    return@forEachIndexed
                }

                // コメント行はスキップ
                if (
                    line.trimStart().startsWith("//") ||
                        line.trimStart().startsWith("*") ||
                        line.trimStart().startsWith("/*")
                ) {
                    return@forEachIndexed
                }

                // フルパッケージ名のパターンをチェック
                // 変数宣言、パラメータ、戻り値の型などで使用されているケースを検出
                val patterns =
                    listOf(
                        // 変数宣言: var/val name: package.Class
                        Regex(
                            """:\s*(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""
                        ),
                        // パラメータ型: (param: package.Class)
                        Regex(
                            """\(\s*\w+\s*:\s*(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""
                        ),
                        // 戻り値型: ): package.Class
                        Regex(
                            """\)\s*:\s*(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""
                        ),
                        // ジェネリクス: <package.Class>
                        Regex(
                            """<\s*(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\s*>"""
                        ),
                        // is/as演算子: is package.Class, as package.Class
                        Regex(
                            """\b(is|as)\s+(jp\.co\.torihada\.fanme\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""
                        ),
                        // その他の一般的なパターン (Quarkusの外部パッケージも含む)
                        Regex(""":\s*(io\.quarkus\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""),
                        Regex(""":\s*(jakarta\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""),
                        Regex(""":\s*(javax\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""),
                        Regex(""":\s*(org\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""),
                        Regex(""":\s*(com\.[a-zA-Z0-9_.]+\.[A-Z][a-zA-Z0-9]*)\b"""),
                    )

                patterns.forEach { pattern ->
                    val matches = pattern.findAll(line)
                    matches.forEach { match ->
                        val fullName = match.groupValues.last()
                        // 一部の除外パターン
                        if (
                            !line.contains("@$fullName") &&
                                !line.contains("\"$fullName\"") &&
                                !line.contains("'$fullName'") &&
                                !line.contains("$fullName::class") &&
                                !line.contains("$fullName.kt")
                        ) {

                            violations.add(
                                "${file.relativeTo(sourceDir).path}:${index + 1}: フルパッケージ名 '$fullName' が使用されています"
                            )
                        }
                    }
                }
            }
        }

        fun checkDirectory(dir: File) {
            dir.walkTopDown().forEach { file ->
                if (file.isFile && file.extension == "kt") {
                    checkFile(file)
                }
            }
        }

        // ソースディレクトリをチェック
        if (sourceDir.exists()) {
            checkDirectory(sourceDir)
        }

        // 違反があれば詳細を表示
        if (violations.isNotEmpty()) {
            println("\n=== フルパッケージ名使用の違反 ===")
            violations.forEach {
                println(it)
                println() // 各違反の間に空行を追加
            }
            println("\n合計: ${violations.size} 件の違反が見つかりました")
        }

        // テストの成功/失敗
        assertTrue(violations.isEmpty(), "フルパッケージ名の使用が ${violations.size} 件見つかりました")
    }
}
