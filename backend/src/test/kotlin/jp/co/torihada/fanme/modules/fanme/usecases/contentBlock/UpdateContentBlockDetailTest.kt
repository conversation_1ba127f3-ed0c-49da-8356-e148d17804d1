package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockDetailFactory
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

@QuarkusTest
class UpdateContentBlockDetailTest {

    @Inject private lateinit var updateContentBlockDetail: UpdateContentBlockDetail

    @Test
    @TestTransaction
    fun `execute returns updated content block detail when content block exists`() {
        val contentBlockDetail = ContentBlockDetailFactory.new()
        contentBlockDetail.persistAndFlush()

        val newTitle = "Updated Title"
        val newDescription = "Updated Description"
        val newAppDescription = "Updated App Description"
        val newUrl = "https://updated-example.com"
        val newIconUrl = "updated-icon.png"

        val input =
            UpdateContentBlockDetail.Input(
                contentBlockId = contentBlockDetail.id!!,
                title = newTitle,
                description = newDescription,
                appDescription = newAppDescription,
                url = newUrl,
                iconUrl = newIconUrl,
            )

        val result = updateContentBlockDetail.execute(input)

        assertTrue(result.isOk)
        val updatedContentBlockDetail = result.unwrap()
        assertNotNull(updatedContentBlockDetail)
        assertEquals(contentBlockDetail.id, updatedContentBlockDetail.id)
        assertEquals(newTitle, updatedContentBlockDetail.title)
        assertEquals(newDescription, updatedContentBlockDetail.description)
        assertEquals(newAppDescription, updatedContentBlockDetail.appDescription)
        assertEquals(newUrl, updatedContentBlockDetail.url)
        assertEquals(newIconUrl, updatedContentBlockDetail.icon)
    }

    @Test
    @TestTransaction
    fun `execute updates only provided fields`() {
        val contentBlockDetail = ContentBlockDetailFactory.new()
        contentBlockDetail.persistAndFlush()

        val originalTitle = contentBlockDetail.title
        val originalAppDescription = contentBlockDetail.appDescription
        val originalIcon = contentBlockDetail.icon

        val newDescription = "Only Description Updated"

        val input =
            UpdateContentBlockDetail.Input(
                contentBlockId = contentBlockDetail.id!!,
                title = null,
                description = newDescription,
                appDescription = null,
                url = null,
                iconUrl = null,
            )

        // When
        val result = updateContentBlockDetail.execute(input)

        // Then
        assertTrue(result.isOk)
        val updatedContentBlockDetail = result.unwrap()
        assertNotNull(updatedContentBlockDetail)
        assertEquals(contentBlockDetail.id, updatedContentBlockDetail.id)
        assertEquals(contentBlockDetail.title, updatedContentBlockDetail.title)
        assertEquals(newDescription, updatedContentBlockDetail.description)
        assertEquals(originalAppDescription, updatedContentBlockDetail.appDescription)
        assertEquals(contentBlockDetail.url, updatedContentBlockDetail.url)
        assertEquals(originalIcon, updatedContentBlockDetail.icon)
    }
}
