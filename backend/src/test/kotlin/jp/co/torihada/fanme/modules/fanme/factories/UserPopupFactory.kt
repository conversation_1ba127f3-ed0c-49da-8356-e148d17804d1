package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserPopup

object UserPopupFactory {
    fun new(
        user: User,
        enable: Boolean = true,
        title: String? = "Test User Popup",
        url: String = "https://example.com",
        buttonText: String? = "Click Here",
        image: String? = "test-image.jpg",
    ): UserPopup {
        return UserPopup().apply {
            this.user = user
            this.enable = enable
            this.title = title
            this.url = url
            this.buttonText = buttonText
            this.image = image
        }
    }
}
