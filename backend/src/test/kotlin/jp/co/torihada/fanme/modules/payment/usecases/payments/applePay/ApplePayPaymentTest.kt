package jp.co.torihada.fanme.modules.payment.usecases.payments.applePay

import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.models.*
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import jp.co.torihada.fanme.modules.payment.usecases.payments.apple.EntryTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.apple.ExecTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseEntryTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseExecTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.CreateCheckout
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class ApplePayPaymentTest {
    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig
    @Inject lateinit var mockExtGmoPaymentClient: MockExtGmoPaymentClient
    @Inject lateinit var createCheckout: CreateCheckout
    @Inject lateinit var entryTransaction: EntryTransaction
    @Inject lateinit var execTransaction: ExecTransaction

    @InjectMock @RestClient lateinit var extGmoPaymentClient: ExtGmoPaymentClient

    @BeforeEach
    fun setUp() {
        mockExtGmoPaymentClient.setupMocks(
            extGmoPaymentClient,
            commonConfig,
            paymentConfig,
            "savedCard",
        )
    }

    @Test
    @TestTransaction
    fun `test apple pay payment success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout.execute(
            CreateCheckout.Input(
                sellerUserId = sellerUserId,
                purchaserUserId = purchaserUserId,
                totalAmount = 1100,
                itemsAmount = 1000,
                profit = 820,
                fee = 280,
                isDigital = true,
                deliveryFee = 0,
                tipAmount = 100,
                paymentType = PaymentConst.PaymentType.APPLE_PAY,
                convenience = null,
            )
        )
        val checkout = Checkout.findLast()

        entryTransaction.execute(
            BaseEntryTransaction.Input(
                checkoutId = checkout?.id!!,
                orderId = checkout.orderId!!,
                totalAmount = 1100,
            )
        )

        execTransaction.execute(
            BaseExecTransaction.Input(
                checkoutId = checkout.id!!,
                orderId = checkout.orderId!!,
                sellerUserUid = sellerUserId,
                purchaserUserUid = purchaserUserId,
                cardSequence = 0,
                token = "sampleToken",
            )
        )

        // DB確認
        val transaction = Transaction.findLast()
        val tip = Tip.findLast()
        val sellerSale = SellerSale.findLast()
        val tenantSale = TenantSale.findLast()

        checkout?.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            assertEquals(it.providerShopId, paymentConfig.gmoShopId())
            assertEquals(it.total, 1100)
            assertEquals(it.amount, 1000)
            assertEquals(it.status, Const.CheckoutStatus.PAYSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }

        transaction?.let {
            assertEquals(it.totalAmount, 1100)
            assertEquals(it.status, PaymentConst.TransactionStatus.Success.value)
            assertEquals(it.checkout?.id, checkout.id)
            assertEquals(it.checkout, checkout)
        }

        sellerSale?.let {
            assertEquals(it.amount, 820)
            assertEquals(it.transaction, transaction)
        }

        tenantSale?.let {
            assertEquals(it.amount, 280)
            assertEquals(it.transaction, transaction)
        }

        // 各モデルのデータを削除
        Checkout.deleteAll()
        Transaction.deleteAll()
        Tip.deleteAll()
        SellerSale.deleteAll()
        TenantSale.deleteAll()
    }

    @Test
    @TestTransaction
    fun `test apple pay payment for physical item success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout.execute(
            CreateCheckout.Input(
                sellerUserId = sellerUserId,
                purchaserUserId = purchaserUserId,
                // コンビニ手数料を抜いた決済の合計
                totalAmount = 1500,
                itemsAmount = 1000,
                profit = 820,
                fee = 680,
                isDigital = false,
                deliveryFee = 400,
                tipAmount = 100,
                paymentType = PaymentConst.PaymentType.APPLE_PAY,
                convenience = null,
            )
        )
        val checkout = Checkout.findLast()

        entryTransaction.execute(
            BaseEntryTransaction.Input(
                checkoutId = checkout?.id!!,
                orderId = checkout.orderId!!,
                totalAmount = 1500,
            )
        )

        execTransaction.execute(
            BaseExecTransaction.Input(
                checkoutId = checkout.id!!,
                orderId = checkout.orderId!!,
                sellerUserUid = sellerUserId,
                purchaserUserUid = purchaserUserId,
                cardSequence = 0,
                token = "sampleToken",
            )
        )

        // DB確認
        val transaction = Transaction.findLast()
        val tip = Tip.findLast()
        val sellerSale = SellerSale.findLast()
        val tenantSale = TenantSale.findLast()

        checkout?.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            // TODO 物販の審査が通ったら、gmoProductShopIdに変更する
            assertEquals(it.providerShopId, paymentConfig.gmoShopId())
            assertEquals(it.total, 1500)
            assertEquals(it.amount, 1000)
            assertEquals(it.deliveryFee, 400)
            assertEquals(it.status, Const.CheckoutStatus.PAYSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }

        transaction?.let {
            assertEquals(it.totalAmount, 1500)
            assertEquals(it.status, PaymentConst.TransactionStatus.Success.value)
            assertEquals(it.checkout?.id, checkout.id)
            assertEquals(it.checkout, checkout)
        }

        sellerSale?.let {
            assertEquals(it.amount, 820)
            assertEquals(it.transaction, transaction)
        }

        tenantSale?.let {
            assertEquals(it.amount, 680)
            assertEquals(it.transaction, transaction)
        }

        // 各モデルのデータを削除
        Checkout.deleteAll()
        Transaction.deleteAll()
        Tip.deleteAll()
        SellerSale.deleteAll()
        TenantSale.deleteAll()
    }
}
