package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Ok
import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.OperationType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupService
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupServiceAuditObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations

@QuarkusTest
class CreateAuditGroupTest {

    @InjectMocks private lateinit var createAuditGroup: CreateAuditGroup

    @Mock private lateinit var auditGroupService: AuditGroupService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `ショップ監査グループが正常に作成されること`() {
        // Given
        val userUid = "test-user"
        val auditType = AuditType.SHOP
        val operationType = OperationType.INSERT
        val metadata =
            AuditGroupMetadata(shopId = 1L, title = "Test Shop", description = "Test Description")
        val auditObjects =
            listOf(
                AuditGroupServiceAuditObject(
                    bucket = "test-bucket",
                    filePath = "test/path",
                    assetType = AssetType.IMAGE,
                )
            )
        val expectedAuditGroupId = 1L

        Mockito.`when`(
                auditGroupService.createAuditGroup(
                    auditType = auditType,
                    userUid = userUid,
                    operationType = operationType,
                    metadata = metadata,
                    auditObjects = auditObjects,
                )
            )
            .thenReturn(expectedAuditGroupId)

        // When
        val result =
            createAuditGroup.execute(
                userUid = userUid,
                auditType = auditType,
                operationType = operationType,
                metadata = metadata,
                auditObjects = auditObjects,
            )

        // Then
        assertEquals(Ok(expectedAuditGroupId), result)
    }
}
