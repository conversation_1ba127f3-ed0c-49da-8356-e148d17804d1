package jp.co.torihada.fanme.modules.payment.controllers

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class SellerAccountBalanceControllerBalanceTest {

    @Inject lateinit var commonConfig: CommonConfig

    @Inject lateinit var config: Config

    @Inject lateinit var sellerAccountBalanceController: SellerAccountBalanceController

    @Test
    @TestTransaction
    fun `test getSellerAccountBalances returns correct balances`() {
        val sellerUserId1 = "testSeller1"
        val sellerUserId2 = "testSeller2"

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                amount = 1000,
                accumulatedSales = 2000,
            )
            .apply { persist() }

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId2,
                amount = 500,
                accumulatedSales = 1500,
            )
            .apply { persist() }

        val result =
            sellerAccountBalanceController.getSellerAccountBalances(
                listOf(sellerUserId1, sellerUserId2)
            )

        assertNotNull(result.balances)
        assertEquals(2, result.balances.size)

        val balance1 = result.balances.find { it.sellerUserId == sellerUserId1 }
        assertNotNull(balance1)
        assertEquals(2000, balance1!!.accumulatedSales)
        assertEquals(1000, balance1.withdrawableAmount)

        val balance2 = result.balances!!.find { it.sellerUserId == sellerUserId2 }
        assertNotNull(balance2)
        assertEquals(1500, balance2!!.accumulatedSales)
        assertEquals(500, balance2.withdrawableAmount)
    }

    @Test
    @TestTransaction
    fun `test getSellerAccountBalances with empty seller IDs list returns empty result`() {
        val result = sellerAccountBalanceController.getSellerAccountBalances(emptyList())

        assertNotNull(result.balances)
        assertEquals(0, result.balances.size)
    }

    @Test
    @TestTransaction
    fun `test getSellerAccountBalances with non-existent seller IDs returns empty result for those IDs`() {
        val existingSellerUserId = "existingSeller"
        val nonExistingSellerUserId = "nonExistingSeller"

        SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = existingSellerUserId,
                amount = 1000,
                accumulatedSales = 2000,
            )
            .apply { persist() }

        val result =
            sellerAccountBalanceController.getSellerAccountBalances(
                listOf(existingSellerUserId, nonExistingSellerUserId)
            )

        assertNotNull(result.balances)
        assertEquals(1, result.balances.size)
        assertEquals(existingSellerUserId, result.balances[0].sellerUserId)
    }
}
