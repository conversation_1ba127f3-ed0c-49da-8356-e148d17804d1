package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConsoleUserEndpointTest {

    @BeforeAll
    @Transactional
    fun setupAll() {

        val consoleSuperUser =
            UserFactory.createTestUser("test-user-1", "Super User", "<EMAIL>")
        val consoleBizUser =
            UserFactory.createTestUser("test-user-2", "Biz User", "<EMAIL>")
        val consoleCreatorUser =
            UserFactory.createTestUser("test-user-3", "Normal User", "<EMAIL>")

        val consoleUsers =
            listOf(
                    consoleSuperUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.SUPER_VALUE)
                    },
                    consoleBizUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.BIZ_VALUE)
                    },
                    consoleCreatorUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.CREATOR_VALUE)
                    },
                )
                .filterNotNull()

        consoleUsers.forEach { it.persistAndFlush() }
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ConsoleUser.deleteAll()
        User.delete("uid LIKE ?1", "test-user-%")
    }

    @Test
    @DisplayName("SUPERロールでユーザー一覧を取得できること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun superRoleCanGetUserList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(200)
            .body(notNullValue())
    }

    @Test
    @DisplayName("BIZロールでユーザー一覧を取得できること")
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun bizRoleCanGetUserList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(200)
            .body(notNullValue())
    }

    @Test
    @DisplayName("AGENTロールではユーザー一覧にアクセスできないこと")
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun agentRoleCannotAccessUserList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("CREATORロールではユーザー一覧にアクセスできないこと")
    @TestSecurity(user = "test-creator", roles = [UserRole.CREATOR_VALUE])
    fun creatorRoleCannotAccessUserList() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(403)
    }

    @Test
    @DisplayName("認証無しの場合は401エラーが返されること")
    fun unauthorizedRequestReturns401() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(401)
    }

    @Test
    @DisplayName("セットアップしたデータが正しく返されること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun setupDataIsReturnedCorrectly() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/console-users")
            .then()
            .statusCode(200)
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.SUPER_VALUE}' }.role",
                equalTo(UserRole.SUPER_VALUE),
            )
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.BIZ_VALUE}' }.role",
                equalTo(UserRole.BIZ_VALUE),
            )
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.CREATOR_VALUE}' }.role",
                equalTo(UserRole.CREATOR_VALUE),
            )
            .body("data.consoleUsers.size()", equalTo(3))
    }
}
