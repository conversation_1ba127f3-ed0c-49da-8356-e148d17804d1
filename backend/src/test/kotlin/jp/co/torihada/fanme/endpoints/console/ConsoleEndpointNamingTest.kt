package jp.co.torihada.fanme.endpoints.console

import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import java.lang.reflect.Method
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.reflections.Reflections
import org.reflections.scanners.Scanners
import org.reflections.util.ClasspathHelper
import org.reflections.util.ConfigurationBuilder

class ConsoleEndpointNamingTest {

    @Test
    @DisplayName("console endpoints should use kebab-case for paths")
    fun consoleEndpointsShouldUseKebabCaseForPaths() {
        val reflections =
            Reflections(
                ConfigurationBuilder()
                    .setUrls(ClasspathHelper.forPackage("jp.co.torihada.fanme.endpoints.console"))
                    .setScanners(Scanners.TypesAnnotated, Scanners.MethodsAnnotated)
            )

        val controllers =
            reflections.getTypesAnnotatedWith(Path::class.java).filter {
                it.name.startsWith("jp.co.torihada.fanme.endpoints.console")
            }

        assertTrue(controllers.isNotEmpty(), "Console endpoints not found")

        val violations = mutableListOf<String>()

        controllers.forEach { controller ->
            // Check class-level @Path annotation
            val classPathAnnotation = controller.getAnnotation(Path::class.java)
            if (classPathAnnotation != null) {
                val path = classPathAnnotation.value
                val pathViolations =
                    validatePath(path, "${controller.simpleName} class-level @Path")
                violations.addAll(pathViolations)
            }

            // Check method-level @Path annotations and @PathParam parameters
            controller.declaredMethods.forEach { method ->
                checkMethodAnnotations(method, violations)
            }
        }

        assertTrue(
            violations.isEmpty(),
            "Console endpoints should use kebab-case naming. Violations found:\n${violations.joinToString("\n")}",
        )
    }

    private fun checkMethodAnnotations(method: Method, violations: MutableList<String>) {
        // Check method-level @Path annotation
        val methodPathAnnotation = method.getAnnotation(Path::class.java)
        if (methodPathAnnotation != null) {
            val path = methodPathAnnotation.value
            val pathViolations =
                validatePath(path, "${method.declaringClass.simpleName}.${method.name} @Path")
            violations.addAll(pathViolations)
        }

        // Check @PathParam annotations
        method.parameterAnnotations.forEach { paramAnnotations ->
            paramAnnotations.forEach { annotation ->
                if (annotation is PathParam) {
                    val paramName = annotation.value
                    if (!isKebabCase(paramName)) {
                        violations.add(
                            "@PathParam(\"$paramName\") in ${method.declaringClass.simpleName}.${method.name} should use kebab-case"
                        )
                    }
                }
            }
        }
    }

    private fun validatePath(path: String, context: String): List<String> {
        val violations = mutableListOf<String>()

        // Split path into segments, ignoring empty segments
        val segments = path.split("/").filter { it.isNotEmpty() }

        segments.forEach { segment ->
            if (segment.contains("{") && segment.contains("}")) {
                // This is a path parameter like {agency_id}
                val paramName = segment.removePrefix("{").removeSuffix("}")
                if (!isKebabCase(paramName)) {
                    violations.add(
                        "Path parameter '{$paramName}' in $context should use kebab-case"
                    )
                }
            } else {
                // This is a regular path segment
                if (!isKebabCase(segment)) {
                    violations.add("Path segment '$segment' in $context should use kebab-case")
                }
            }
        }

        return violations
    }

    private fun isKebabCase(str: String): Boolean {
        // kebab-case: lowercase letters, numbers, and hyphens only
        // Must not start or end with hyphen, no consecutive hyphens
        if (str.isEmpty()) return true
        if (str.startsWith("-") || str.endsWith("-")) return false
        if (str.contains("--")) return false

        return str.matches(Regex("^[a-z0-9]+(-[a-z0-9]+)*$"))
    }
}
