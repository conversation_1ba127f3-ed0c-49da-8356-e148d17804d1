package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ProfileFactory
import jp.co.torihada.fanme.modules.fanme.factories.SnsLinkDisplayFactory
import jp.co.torihada.fanme.modules.fanme.factories.SnsLinkFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.Profile
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class MoveSnsLinksTest {

    @Inject private lateinit var moveSnsLinks: MoveSnsLinks

    @Test
    @TestTransaction
    fun `should swap sns links display order successfully`() {
        val user = UserFactory.createTestUser()!!
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val snsLinks =
            SnsLinkFactory.createMultipleTestSnsLinks(
                profile = profile,
                snsLinksData =
                    listOf(
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.TWITTER.dbStr,
                            "@twitter",
                            1,
                            true,
                        ),
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.INSTAGRAM.dbStr,
                            "instagram",
                            2,
                            true,
                        ),
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.YOUTUBE.dbStr,
                            "youtube",
                            3,
                            true,
                        ),
                    ),
            )

        snsLinks.forEachIndexed { index, snsLink ->
            SnsLinkDisplayFactory.createTestSnsLinkDisplay(
                profile = profile,
                snsLink = snsLink,
                displayOrderNumber = index + 1,
                displayable = true,
            )
        }

        user.persistAndFlush()

        val input =
            MoveSnsLinks.Input(
                userId = user.id!!,
                snsLinks = listOf("youtube", "twitter", "instagram"),
            )

        val result = moveSnsLinks.execute(input)

        assertTrue(result.isOk)
        val response = result.unwrap()
        assertTrue(response.success)

        // データベースの表示順序が入れ替わっていることを確認
        // プロフィールを再取得してSNSリンクの最新状態を確認
        val updatedProfile = Profile.findById(profile.id!!)
        val sortedDisplays =
            updatedProfile!!
                .snsLinkDisplays
                .filter { it.displayable == true }
                .sortedBy { it.displayOrderNumber }

        // YouTube が 1番目
        assertEquals(Const.SnsLinkType.YOUTUBE.dbStr, sortedDisplays[0].snsLink?.type)
        assertEquals(1, sortedDisplays[0].displayOrderNumber)

        // Twitter が 2番目
        assertEquals(Const.SnsLinkType.TWITTER.dbStr, sortedDisplays[1].snsLink?.type)
        assertEquals(2, sortedDisplays[1].displayOrderNumber)

        // Instagram が 3番目
        assertEquals(Const.SnsLinkType.INSTAGRAM.dbStr, sortedDisplays[2].snsLink?.type)
        assertEquals(3, sortedDisplays[2].displayOrderNumber)
    }

    @Test
    @TestTransaction
    fun `should exclude non displayable sns links from move operation`() {
        val user = UserFactory.createTestUser()!!
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val snsLinks =
            SnsLinkFactory.createMultipleTestSnsLinks(
                profile = profile,
                snsLinksData =
                    listOf(
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.TWITTER.dbStr,
                            "@twitter",
                            1,
                            true,
                        ),
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.INSTAGRAM.dbStr,
                            "instagram",
                            2,
                            false,
                        ),
                    ),
            )

        SnsLinkDisplayFactory.createTestSnsLinkDisplay(
            profile = profile,
            snsLink = snsLinks[0], // Twitter
            displayOrderNumber = 1,
            displayable = true,
        )
        SnsLinkDisplayFactory.createTestSnsLinkDisplay(
            profile = profile,
            snsLink = snsLinks[1], // Instagram
            displayOrderNumber = 2,
            displayable = false, // 非表示
        )

        user.persistAndFlush()

        val input = MoveSnsLinks.Input(userId = user.id!!, snsLinks = listOf("twitter"))

        val result = moveSnsLinks.execute(input)

        assertTrue(result.isOk)
        val response = result.unwrap()

        assertTrue(response.success)
        val updatedProfile = Profile.findById(profile.id!!)
        val twitterDisplay =
            updatedProfile!!.snsLinkDisplays.find {
                it.snsLink?.type == Const.SnsLinkType.TWITTER.dbStr
            }
        assertEquals(1, twitterDisplay?.displayOrderNumber)
    }

    @Test
    @TestTransaction
    fun `should return error when link count mismatch`() {
        val user = UserFactory.createTestUser()!!
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val snsLinks =
            SnsLinkFactory.createMultipleTestSnsLinks(
                profile = profile,
                snsLinksData =
                    listOf(
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.TWITTER.dbStr,
                            "@twitter",
                            1,
                            true,
                        ),
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.INSTAGRAM.dbStr,
                            "instagram",
                            2,
                            true,
                        ),
                    ),
            )

        snsLinks.forEachIndexed { index, snsLink ->
            SnsLinkDisplayFactory.createTestSnsLinkDisplay(
                profile = profile,
                snsLink = snsLink,
                displayOrderNumber = index + 1,
                displayable = true,
            )
        }

        user.persistAndFlush()

        // 3つのリンクを指定（実際は2つしかない）
        val input =
            MoveSnsLinks.Input(
                userId = user.id!!,
                snsLinks = listOf("twitter", "instagram", "youtube"),
            )

        val result = moveSnsLinks.execute(input)

        assertTrue(result.isErr)
        val error = result.component2()
        assertTrue(error is IllegalArgumentException)
        assertTrue(error?.message?.contains("Request is invalid: link count mismatch") == true)
    }

    @Test
    @TestTransaction
    fun `should return error for invalid sns type`() {
        val user = UserFactory.createTestUser()!!
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val snsLinks =
            SnsLinkFactory.createMultipleTestSnsLinks(
                profile = profile,
                snsLinksData =
                    listOf(
                        SnsLinkFactory.SnsLinkData(
                            Const.SnsLinkType.TWITTER.dbStr,
                            "@twitter",
                            1,
                            true,
                        )
                    ),
            )

        SnsLinkDisplayFactory.createTestSnsLinkDisplay(
            profile = profile,
            snsLink = snsLinks[0],
            displayOrderNumber = 1,
            displayable = true,
        )

        user.persistAndFlush()

        val input = MoveSnsLinks.Input(userId = user.id!!, snsLinks = listOf("invalid_type"))

        val result = moveSnsLinks.execute(input)

        assertTrue(result.isErr)
        val error = result.component2()
        assertTrue(error is IllegalArgumentException)
        assertTrue(error?.message?.contains("Invalid SNS link type") == true)
    }
}
