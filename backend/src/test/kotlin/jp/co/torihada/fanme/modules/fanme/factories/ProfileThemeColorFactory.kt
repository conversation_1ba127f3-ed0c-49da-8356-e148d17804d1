package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileThemeColor

object ProfileThemeColorFactory {
    fun new(
        profile: Profile,
        themeColorId: Long = Const.ThemeColor.DARK.value,
        customColor: String? = null,
    ): ProfileThemeColor {
        return ProfileThemeColor().apply {
            this.profile = profile
            this.themeColorId = themeColorId
            this.customColor = customColor
        }
    }
}
