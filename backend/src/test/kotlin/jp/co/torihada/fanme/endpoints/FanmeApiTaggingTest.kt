package jp.co.torihada.fanme.endpoints

import jakarta.ws.rs.Path
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.reflections.Reflections
import org.reflections.scanners.Scanners
import org.reflections.util.ClasspathHelper
import org.reflections.util.ConfigurationBuilder

/** FANME APIエンドポイントのタグ付けルールを検証するテスト */
class FanmeApiTaggingTest {

    @Test
    @DisplayName("すべてのFANME APIエンドポイントに正しいタグが付与されていること")
    fun allFanmeApiEndpointsHaveCorrectTags() {
        // fanmeディレクトリ内のすべてのクラスを取得
        val reflections =
            Reflections(
                ConfigurationBuilder()
                    .setUrls(ClasspathHelper.forPackage("jp.co.torihada.fanme.endpoints.fanme"))
                    .setScanners(Scanners.TypesAnnotated)
            )

        // Pathアノテーションが付いたクラスを取得（RESTエンドポイント）
        val controllers =
            reflections.getTypesAnnotatedWith(Path::class.java).filter {
                it.name.startsWith("jp.co.torihada.fanme.endpoints.fanme")
            }

        // 少なくとも1つ以上のエンドポイントがあることを確認
        assertTrue(controllers.isNotEmpty(), "FANMEエンドポイントが見つかりません")

        // 各エンドポイントが正しいFANMEタグを持っているか確認
        controllers.forEach { controller ->
            val tags = controller.getAnnotationsByType(Tag::class.java)

            // FANMEタグが存在することを確認
            val hasCorrectTag =
                tags.any { tag -> tag.name == "FANME" && tag.description == "FANME APIサーバー" }

            assertTrue(
                hasCorrectTag,
                "${controller.simpleName}に@Tag(name = \"FANME\", description = \"FANME APIサーバー\")が付与されていません",
            )
        }
    }
}
