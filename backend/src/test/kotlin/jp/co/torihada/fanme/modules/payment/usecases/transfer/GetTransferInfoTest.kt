package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetTransferInfoTest {
    private val logger: Logger = Logger.getLogger(GetTransferInfoTest::class.java)

    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig

    @InjectMock @RestClient lateinit var extGmoTransferClient: ExtGmoTransferClient

    @Inject lateinit var mockExtGmoTransferClient: MockExtGmoTransferClient

    @Inject lateinit var getTransferInfo: GetTransferInfo

    @BeforeEach
    fun setup() {
        mockExtGmoTransferClient.setUpMocks(extGmoTransferClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test get transfer info success`() {
        val userId = "sellerUserId"
        val tenant = commonConfig.tenant()

        val monthlySellerSales =
            MonthlySellerSaleFactory.new(
                tenant = tenant,
                sellerUserId = userId,
                yearMonth = "202401",
                transactionAmount = 1000,
                approved = true,
                merged = true,
                remainingAmount = 735,
                expirationDate = LocalDateTime.of(2024, 6, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )

        val monthlySellerSales2 =
            MonthlySellerSaleFactory.new(
                tenant = tenant,
                sellerUserId = userId,
                yearMonth = "202402",
                transactionAmount = 2000,
                approved = true,
                merged = false,
                remainingAmount = 1470,
                expirationDate = LocalDateTime.of(2024, 7, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )

        val monthlySellerSales3 =
            MonthlySellerSaleFactory.new(
                tenant = tenant,
                sellerUserId = userId,
                yearMonth = "202403",
                transactionAmount = 3000,
                approved = false,
                merged = false,
                remainingAmount = 2205,
                expirationDate = LocalDateTime.of(2024, 8, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )

        monthlySellerSales.persist()
        monthlySellerSales2.persist()
        monthlySellerSales3.persist()

        val response =
            getTransferInfo.execute(GetTransferInfo.Input(userId = userId)).getOrElse {
                throw Exception("Failed to get transfer info")
            }

        assertEquals(
            response.pendingAmounts,
            listOf(
                GetTransferInfo.MonthAndAmount(
                    yearMonth = "202402",
                    amount = 1470,
                    latestExpireDate = null,
                ),
                GetTransferInfo.MonthAndAmount(
                    yearMonth = "202403",
                    amount = 2205,
                    latestExpireDate = null,
                ),
            ),
        )

        assertEquals(
            response.notAppliedAmounts,
            listOf(
                GetTransferInfo.MonthAndAmount(
                    yearMonth = "202401",
                    amount = 735,
                    latestExpireDate = LocalDateTime.of(2024, 6, 1, 9, 0, 0),
                )
            ),
        )

        assertEquals(response.balanceAmount, 4410)
        assertEquals(response.transferState, true)
    }
}
