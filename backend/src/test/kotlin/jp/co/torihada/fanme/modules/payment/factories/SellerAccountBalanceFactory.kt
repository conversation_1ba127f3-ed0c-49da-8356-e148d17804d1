package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance

object SellerAccountBalanceFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        sellerUserId: String = "sellerUserId",
        amount: Int = 0,
        accumulatedSales: Int = 0,
    ): SellerAccountBalance {
        return SellerAccountBalance().apply {
            this.id = id
            this.tenant = tenant
            this.sellerUserId = sellerUserId
            this.amount = amount
            this.accumulatedSales = accumulatedSales
        }
    }
}
