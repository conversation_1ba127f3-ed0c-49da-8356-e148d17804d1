package jp.co.torihada.fanme.modules.payment.mock.payment

import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.payment.ExtPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.MiniappWebhook
import org.mockito.Mockito

@ApplicationScoped
class MockExtPaymentClient {
    fun setupMocks(extPaymentClient: ExtPaymentClient, config: Config) {
        Mockito.`when`(
                extPaymentClient.miniappWebhook(
                    authorization =
                        "Basic ZmFubWUuYXV0aC5hcGk6ZGZnc2RqaWVycmUxMTAyNFRESGlqKWpwamdhZGZhey1mc2VmZHNnYXNkZmV3ZmtIa2xmZW93YWJtY2Znc1N0ZnNm",
                    MiniappWebhook.Request(
                        shopId = config.gmoShopId(),
                        shopPass = config.gmoShopPass(),
                        accessId = "sampleAccessId",
                        accessPass = "samplePass",
                        orderId = "sampleOrderId",
                        status = "status",
                        amount = "0",
                        tax = "0",
                        payType = "payType",
                    ),
                )
            )
            .thenReturn(Response.ok(0).build())
    }
}
