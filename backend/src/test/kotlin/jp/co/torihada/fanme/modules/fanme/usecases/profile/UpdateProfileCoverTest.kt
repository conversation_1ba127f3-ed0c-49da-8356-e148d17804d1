package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ProfileCoverFactory
import jp.co.torihada.fanme.modules.fanme.factories.ProfileFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class UpdateProfileCoverTest {

    @Inject private lateinit var updateProfileCover: UpdateProfileCover

    @Test
    @TestTransaction
    fun `execute updates profile cover when user exists`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val profile =
            ProfileFactory.new(
                user = user,
                bio = "Test bio",
                headerImage = "header.jpg",
                snsLinkColor = "ORIG",
                officialFlg = false,
            )
        profile.persistAndFlush()

        val profileCover =
            ProfileCoverFactory.new(
                profile = profile,
                brightness = Const.Brightness.NORMAL.value,
                coverVisibility = true,
            )
        profileCover.persistAndFlush()

        profile.cover = profileCover

        val newCoverVisibility = true
        val newBrightness = Const.Brightness.DARK.value

        val input =
            UpdateProfileCover.Input(
                userId = user.id!!,
                coverImageVisibility = newCoverVisibility,
                brightness = newBrightness,
            )
        val result = updateProfileCover.execute(input)

        assertTrue(result.isOk)
        val updatedProfileCover = result.unwrap()
        assertNotNull(updatedProfileCover)
        assertEquals(profile.id, updatedProfileCover.profile?.id)
        assertEquals(newCoverVisibility, updatedProfileCover.coverVisibility)
        assertEquals(newBrightness, updatedProfileCover.brightness)
    }
}
