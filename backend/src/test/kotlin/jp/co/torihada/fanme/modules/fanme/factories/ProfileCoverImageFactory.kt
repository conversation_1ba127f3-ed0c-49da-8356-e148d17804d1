package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover
import jp.co.torihada.fanme.modules.fanme.models.ProfileCoverImage

object ProfileCoverImageFactory {
    fun new(
        profileCover: ProfileCover,
        resource: String = "cover.jpg",
        resourceType: String = Const.CoverResourceType.PHOTO.value,
    ): ProfileCoverImage {
        return ProfileCoverImage().apply {
            this.profileCover = profileCover
            this.resource = resource
            this.resourceType = resourceType
        }
    }
}
