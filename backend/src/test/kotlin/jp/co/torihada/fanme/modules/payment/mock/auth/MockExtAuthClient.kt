package jp.co.torihada.fanme.modules.payment.mock.auth

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.entity.auth.GetStat
import jp.co.torihada.fanme.modules.payment.externals.entity.auth.GetUserInfo
import org.mockito.Mockito

@ApplicationScoped
class MockExtAuthClient {
    fun setupMocks(extAuthClient: ExtAuthClient, config: Config) {
        Mockito.`when`(extAuthClient.getAuthInfo(authorization = "sampleAuthorization"))
            .thenReturn(GetUserInfo.Response(name = "sampleName", email = "sampleEmail"))

        Mockito.`when`(
                extAuthClient.retrieveAuthInfo(
                    uuid = "sellerUserId",
                    authorization =
                        "Basic ZmFubWUuYXV0aC5hcGk6ZGZnc2RqaWVycmUxMTAyNFRESGlqKWpwamdhZGZhey1mc2VmZHNnYXNkZmV3ZmtIa2xmZW93YWJtY2Znc1N0ZnNm",
                )
            )
            .thenReturn(GetUserInfo.Response(name = "sampleName", email = "sampleEmail"))

        Mockito.`when`(
                extAuthClient.retrieveAuthInfo(
                    uuid = "purchaserUserId",
                    authorization =
                        "Basic ZmFubWUuYXV0aC5hcGk6ZGZnc2RqaWVycmUxMTAyNFRESGlqKWpwamdhZGZhey1mc2VmZHNnYXNkZmV3ZmtIa2xmZW93YWJtY2Znc1N0ZnNm",
                )
            )
            .thenReturn(GetUserInfo.Response(name = "sampleName", email = "sampleEmail"))

        Mockito.`when`(extAuthClient.getStat(authorization = "sampleAuthorization"))
            .thenReturn(
                GetStat.Response(status = 1, expired = true, redirectUrl = "sampleRedirectUrl")
            )
    }
}
