package jp.co.torihada.fanme.modules.payment.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale

object MonthlySellerSaleFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        sellerUserId: String? = null,
        yearMonth: String? = null,
        transactionAmount: Int = 0,
        miniappSalesAmount: Int = 0,
        transferSalesAmount: Int = 0,
        gmoSalesAmount: Int = 0,
        developerSalesAmount: Int = 0,
        gmoTransferFeeAmount: Int = 0,
        approved: Boolean = false,
        merged: Boolean = false,
        remainingAmount: Int = 0,
        expirationDate: Instant? = null,
        transferStatus: String = "0",
    ): MonthlySellerSale {
        return MonthlySellerSale().apply {
            this.id = id
            this.tenant = tenant ?: "fanme"
            this.sellerUserId = sellerUserId ?: "sellerUserId"
            this.yearMonth = yearMonth ?: "202201"
            this.transactionAmount = transactionAmount
            this.miniappSalesAmount = miniappSalesAmount
            this.transferSalesAmount = transferSalesAmount
            this.gmoSalesAmount = gmoSalesAmount
            this.developerSalesAmount = developerSalesAmount
            this.gmoTransferFeeAmount = gmoTransferFeeAmount
            this.approved = approved
            this.merged = merged
            this.remainingAmount = remainingAmount
            this.expirationDate = expirationDate
            this.transferStatus = transferStatus
        }
    }
}
