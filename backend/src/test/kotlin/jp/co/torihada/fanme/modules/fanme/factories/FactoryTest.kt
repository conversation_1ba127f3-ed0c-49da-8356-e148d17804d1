package jp.co.torihada.fanme.modules.fanme.factories

import java.time.Instant
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FactoryTest {

    @Test
    fun testUserFactory() {
        val user =
            UserFactory.new(
                uid = "test-factory-user-1",
                name = "Test Factory User",
                accountIdentity = "<EMAIL>",
            )

        assertNotNull(user)
        assertEquals("test-factory-user-1", user.uid)
    }

    @Test
    fun testUserFactoryWithAllFields() {
        val user =
            UserFactory.new(
                uid = "test-factory-user-2",
                name = "Full User",
                accountIdentity = "<EMAIL>",
                gender = "MALE",
                birthday = Instant.parse("1990-01-01T00:00:00Z"),
                birthdayConfirmed = true,
                isPublic = false,
                allowPublicSharing = true,
                filledProfile = false,
                purpose = 2,
                icon = "https://example.com/icon.png",
            )

        assertNotNull(user)
        assertEquals("test-factory-user-2", user.uid)
    }
}
