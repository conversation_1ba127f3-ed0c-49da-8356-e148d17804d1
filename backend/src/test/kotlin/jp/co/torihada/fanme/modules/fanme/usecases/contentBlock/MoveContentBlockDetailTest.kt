package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockDetailFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

@QuarkusTest
class MoveContentBlockDetailTest {

    @Inject lateinit var moveContentBlockDetail: MoveContentBlockDetail

    @Test
    @TestTransaction
    fun `test reorder content block details with three details`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.THREE_BLOCKS.id,
                displayOrderNumber = 1,
                displayable = true,
            )
        contentBlock.persistAndFlush()

        val contentBlockDetail1 = ContentBlockDetailFactory.new()
        contentBlockDetail1.persistAndFlush()

        val contentBlockDetail2 = ContentBlockDetailFactory.new()
        contentBlockDetail2.persistAndFlush()

        val contentBlockDetail3 = ContentBlockDetailFactory.new()
        contentBlockDetail3.persistAndFlush()

        val contentBlockGroup1 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail1,
                contentGroupNumber = 1,
            )
        contentBlockGroup1.persistAndFlush()

        val contentBlockGroup2 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail2,
                contentGroupNumber = 2,
            )
        contentBlockGroup2.persistAndFlush()

        val contentBlockGroup3 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail3,
                contentGroupNumber = 3,
            )
        contentBlockGroup3.persistAndFlush()

        contentBlock.contentBlockGroups.add(contentBlockGroup1)
        contentBlock.contentBlockGroups.add(contentBlockGroup2)
        contentBlock.contentBlockGroups.add(contentBlockGroup3)
        contentBlock.persistAndFlush()

        val input =
            MoveContentBlockDetail.Input(
                contentBlockDetailIds =
                    listOf(
                        contentBlockDetail3.id!!,
                        contentBlockDetail1.id!!,
                        contentBlockDetail2.id!!,
                    )
            )

        val result = moveContentBlockDetail.execute(input).unwrap()

        result.contentBlockGroups.forEach {
            when (it.contentGroupNumber) {
                1 -> assertEquals(contentBlockDetail3.id, it.contentBlockDetail?.id)
                2 -> assertEquals(contentBlockDetail1.id, it.contentBlockDetail?.id)
                3 -> assertEquals(contentBlockDetail2.id, it.contentBlockDetail?.id)
                else ->
                    throw IllegalStateException(
                        "Unexpected content group number: ${it.contentGroupNumber}"
                    )
            }
        }
    }
}
