package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.User

object ContentBlockFactory {
    fun new(
        user: User,
        contentBlockTypeId: Long = 1L,
        displayOrderNumber: Int = 1,
        displayable: Boolean = true,
    ): ContentBlock {
        return ContentBlock().apply {
            this.user = user
            this.contentBlockTypeId = contentBlockTypeId
            this.displayOrderNumber = displayOrderNumber
            this.displayable = displayable
        }
    }
}
