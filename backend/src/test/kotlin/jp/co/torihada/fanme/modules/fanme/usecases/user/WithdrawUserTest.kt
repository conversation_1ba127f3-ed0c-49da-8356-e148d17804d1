package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.unwrap
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserTokenFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserWithdrawal
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito

@QuarkusTest
class WithdrawUserTest {

    @Inject private lateinit var withdrawUser: WithdrawUser

    @InjectMock @RestClient private lateinit var mockAuthClient: ExtAuthClient

    @BeforeEach
    fun setUp() {
        Mockito.reset(mockAuthClient)
    }

    @Test
    @TestTransaction
    fun `execute successfully withdraws user when valid input provided`() {
        val user = UserFactory.createTestUser()!!
        UserTokenFactory.createTestUserToken(user)
        user.persistAndFlush()

        val token = "test-token"
        val reason = UserWithdrawal.Reason.USE.value
        val detail = "Test withdrawal reason"

        val input =
            WithdrawUser.Input(
                userUuid = user.uid!!,
                token = token,
                reason = reason,
                detail = detail,
            )

        val result = withdrawUser.execute(input)

        assertTrue(result.isOk)
        result.unwrap()

        Mockito.verify(mockAuthClient).deleteUser(token)

        val updatedUser = User.findByUuid(user.uid!!)
        assertNotNull(updatedUser)
        assertNotNull(updatedUser!!.deletedAt)
        assertNull(updatedUser.token)

        val withdrawal = UserWithdrawal.find("user", updatedUser).firstResult()!!
        assertNotNull(withdrawal)
        assertEquals(UserWithdrawal.Reason.USE, withdrawal.reason)
        assertEquals(detail, withdrawal.detail)
        assertEquals(updatedUser.id, withdrawal.user!!.id)
    }

    @Test
    @TestTransaction
    fun `execute returns error when user not found`() {
        val nonExistentUuid = "non-existent-uuid"
        val token = "Bearer test-token"
        val reason = UserWithdrawal.Reason.USE.value
        val detail = "Test withdrawal reason"

        val input =
            WithdrawUser.Input(
                userUuid = nonExistentUuid,
                token = token,
                reason = reason,
                detail = detail,
            )

        val result = withdrawUser.execute(input)

        assertTrue(result.isErr)
        val error = result.component2()
        assertTrue(error is ResourceNotFoundException)

        Mockito.verify(mockAuthClient, Mockito.never()).deleteUser(Mockito.anyString())
    }

    @Test
    @TestTransaction
    fun `execute returns error when invalid reason provided`() {
        val user = UserFactory.createTestUser()!!
        UserTokenFactory.createTestUserToken(user)
        user.persistAndFlush()

        val token = "Bearer test-token"
        val invalidReason = 999 // Invalid reason value
        val detail = "Test withdrawal reason"

        val input =
            WithdrawUser.Input(
                userUuid = user.uid!!,
                token = token,
                reason = invalidReason,
                detail = detail,
            )

        val result = withdrawUser.execute(input)

        assertTrue(result.isErr)
        val error = result.component2()
        assertTrue(error is IllegalArgumentException)
        assertTrue(error!!.message!!.contains("Invalid reason value: $invalidReason"))

        Mockito.verify(mockAuthClient, Mockito.never()).deleteUser(Mockito.anyString())

        val updatedUser = User.findByUuid(user.uid!!)
        assertNotNull(updatedUser)
        assertNull(updatedUser!!.deletedAt)
        assertNotNull(updatedUser.token)
    }

    @Test
    @TestTransaction
    fun `execute returns error when authClient throws exception`() {
        val user = UserFactory.createTestUser()!!
        UserTokenFactory.createTestUserToken(user)
        user.persistAndFlush()

        val token = "Bearer test-token"
        val reason = UserWithdrawal.Reason.USE.value
        val detail = "Test withdrawal reason"

        val input =
            WithdrawUser.Input(
                userUuid = user.uid!!,
                token = token,
                reason = reason,
                detail = detail,
            )

        val authException = RuntimeException("Auth service error")
        Mockito.doThrow(authException).`when`(mockAuthClient).deleteUser(token)

        val result = withdrawUser.execute(input)

        assertTrue(result.isErr)
        val error = result.component2()
        assertEquals(authException, error)

        Mockito.verify(mockAuthClient).deleteUser(token)

        val updatedUser = User.findByUuid(user.uid!!)
        assertNotNull(updatedUser)
        assertNull(updatedUser!!.deletedAt)
        assertNotNull(updatedUser.token)

        val withdrawal = UserWithdrawal.find("user", updatedUser).firstResult()
        assertNull(withdrawal)
    }
}
