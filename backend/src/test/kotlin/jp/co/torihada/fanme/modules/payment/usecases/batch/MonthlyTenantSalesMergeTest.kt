package jp.co.torihada.fanme.modules.payment.usecases.batch

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.batch.usecases.MonthlyTenantSalesMerge
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.factories.MonthlyTenantSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.TenantAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.models.MonthlyTenantSale
import jp.co.torihada.fanme.modules.payment.models.TenantAccountActivity
import jp.co.torihada.fanme.modules.payment.models.TenantAccountBalance
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class MonthlyTenantSalesMergeTest {

    @Inject lateinit var config: Config
    @Inject lateinit var monthlyTenantStatusMerge: MonthlyTenantSalesMerge

    @Test
    @TestTransaction
    fun `test monthly tenant sales merge`() {
        val yearMonth = "202401"

        MonthlyTenantSaleFactory.new(
                tenant = config.tenant(),
                yearMonth = yearMonth,
                transactionAmount = 1000,
                miniappSalesAmount = 265,
                merged = false,
            )
            .apply { persist() }

        TenantAccountBalanceFactory.new(tenant = config.tenant(), amount = 1000).apply { persist() }

        monthlyTenantStatusMerge.execute(yearMonth)

        // DBの確認
        val monthlyTenantSale =
            MonthlyTenantSale.findByTenantAndYearMonth(config.tenant(), yearMonth)
        val tenantAccountBalance = TenantAccountBalance.findByTenant(config.tenant())
        val tenantAccountActivity = TenantAccountActivity.findLast()

        tenantAccountBalance?.let { assertEquals(it.amount, 1265) }
        monthlyTenantSale?.let { assertEquals(it.merged, true) }
        tenantAccountActivity?.let {
            assertEquals(it.amount, 265)
            assertEquals(it.balance, 1265)
            assertEquals(it.yearMonth, yearMonth)
            assertEquals(it.activityCode, Const.AccountActivityCode.AppSales.value)
            assertEquals(it.activityType, Const.AccountActivityType.Payment.value)
        }
    }
}
