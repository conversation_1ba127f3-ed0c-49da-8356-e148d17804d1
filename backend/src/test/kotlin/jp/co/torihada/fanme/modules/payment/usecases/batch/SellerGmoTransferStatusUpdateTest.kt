package jp.co.torihada.fanme.modules.payment.usecases.batch

import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.batch.usecases.SellerGmoTransferStatusUpdate
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.factories.SellerGmoTransferFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.auth.MockExtAuthClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class SellerGmoTransferStatusUpdateTest {
    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig
    @Inject lateinit var sellerGmoTransferStatusUpdate: SellerGmoTransferStatusUpdate
    @Inject lateinit var mockExtGmoTransferClient: MockExtGmoTransferClient
    @Inject lateinit var mockExtAuthClient: MockExtAuthClient

    @InjectMock @RestClient lateinit var extGmoTransferClient: ExtGmoTransferClient
    @InjectMock @RestClient lateinit var extAuthClient: ExtAuthClient

    @BeforeEach
    fun setup() {
        mockExtGmoTransferClient.setUpMocks(extGmoTransferClient, paymentConfig)
        mockExtAuthClient.setupMocks(extAuthClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test seller gmo transfer status update`() {
        val sellerUserId = "sellerUserId"
        val yearMonth = "202401"
        val tenant = commonConfig.tenant()

        SellerGmoTransferFactory.new(
                tenant = tenant,
                sellerUserId = sellerUserId,
                depositId = "1234567",
                amount = 1000,
                status = Const.GmoTransferStatus.TransferRegistered.value,
            )
            .persist()

        SellerAccountBalanceFactory.new(
                tenant = tenant,
                sellerUserId = sellerUserId,
                amount = 1440,
                accumulatedSales = 1440,
            )
            .persist()

        MonthlySellerSaleFactory.new(
                tenant = tenant,
                sellerUserId = sellerUserId,
                yearMonth = yearMonth,
                remainingAmount = 1440,
                approved = true,
                merged = true,
                transferStatus = Const.MonthlySellerSaleStatus.NotTransferred.value,
                expirationDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )
            .persist()

        sellerGmoTransferStatusUpdate.execute()

        // DB確認
        val sellerGmoTransfer = SellerGmoTransfer.findLast()
        val sellerAccountBalance = SellerAccountBalance.findBySellerUserId(sellerUserId)
        val monthlySellerSale =
            MonthlySellerSale.findBySellerUserIdAndYearMonth(sellerUserId, yearMonth)

        sellerGmoTransfer?.let {
            assertEquals(it.status, Const.GmoTransferStatus.TransferCompleted.value)
        }
        sellerAccountBalance?.let { assertEquals(it.amount, 0) }
        monthlySellerSale?.let {
            assertEquals(it.transferStatus, Const.MonthlySellerSaleStatus.Transferred.value)
            assertEquals(it.remainingAmount, 0)
        }
    }
}
