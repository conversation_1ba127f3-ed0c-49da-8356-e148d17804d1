package jp.co.torihada.fanme.modules.payment.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Tip
import jp.co.torihada.fanme.modules.payment.models.Transaction

object TransactionFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        purchaserUserId: String? = null,
        orderId: String? = null,
        sellerUserId: String? = null,
        orderedAt: Instant? = null,
        amount: Int? = null,
        totalAmount: Int? = null,
        status: String? = null,
        gmoForward: String? = null,
        gmoMethod: String? = null,
        gmoPayTimes: Int? = null,
        checkout: Checkout? = null,
        tip: Tip? = null,
        createdAt: Instant? = null,
        updatedAt: Instant? = null,
    ): Transaction {
        return Transaction().apply {
            this.id = id
            this.tenant = tenant
            this.purchaserUserId = purchaserUserId
            this.orderId = orderId
            this.sellerUserId = sellerUserId
            this.orderedAt = orderedAt
            this.amount = amount ?: 100
            this.totalAmount = totalAmount ?: 100
            this.status = status
            this.gmoForward = gmoForward
            this.gmoMethod = gmoMethod
            this.gmoPayTimes = gmoPayTimes
            this.checkout = checkout
            this.tip = tip
            this.createdAt = createdAt
            this.updatedAt = updatedAt
        }
    }
}
