package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.MonthlyTenantSale

object MonthlyTenantSaleFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        yearMonth: String? = null,
        transactionAmount: Int = 0,
        miniappSalesAmount: Int = 0,
        sellerSalesAmount: Int = 0,
        gmoSalesAmount: Int = 0,
        developerSalesAmount: Int = 0,
        gmoTransferFeeAmount: Int = 0,
        merged: Boolean = false,
    ): MonthlyTenantSale {
        return MonthlyTenantSale().apply {
            this.id = id
            this.tenant = tenant
            this.yearMonth = yearMonth
            this.transactionAmount = transactionAmount
            this.miniappSalesAmount = miniappSalesAmount
            this.sellerSalesAmount = sellerSalesAmount
            this.gmoSalesAmount = gmoSalesAmount
            this.developerSalesAmount = developerSalesAmount
            this.gmoTransferFeeAmount = gmoTransferFeeAmount
            this.merged = merged
        }
    }
}
