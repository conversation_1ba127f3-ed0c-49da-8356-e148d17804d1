package jp.co.torihada.fanme.modules.payment.usecases

import io.quarkus.test.common.QuarkusTestResourceLifecycleManager
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import org.flywaydb.core.Flyway
import org.jboss.logging.Logger
import org.testcontainers.containers.MySQLContainer

// テスト全体のライフサイクルを記載してください
open class TestResourceLifecycleManager : QuarkusTestResourceLifecycleManager {
    private val logger: Logger = Logger.getLogger(TestResultLogger::class.java)
    private var mysqlContainer: MySQLContainer<Nothing>? = null

    override fun start(): MutableMap<String, String> {
        mysqlContainer = MySQLContainer<Nothing>("mysql:8.0").apply { startupAttempts = 1 }
        mysqlContainer!!.withDatabaseName("payment")
        mysqlContainer!!.start()

        // Flywayを使用してマイグレーションを実行
        val flyway =
            Flyway.configure()
                .dataSource(
                    mysqlContainer!!.jdbcUrl,
                    mysqlContainer!!.username,
                    mysqlContainer!!.password,
                )
                .locations(
                    "filesystem:src/main/resources/db/migration/payment"
                ) // マイグレーションファイルの場所を指定
                .load()

        // マイグレーションを実行
        flyway.migrate()

        return mutableMapOf(
            "quarkus.datasource.payment.jdbc.url" to mysqlContainer!!.jdbcUrl,
            "quarkus.datasource.payment.username" to mysqlContainer!!.username,
            "quarkus.datasource.payment.password" to mysqlContainer!!.password,
            "quarkus.hibernate-orm.payment.datasource" to "payment",
        )
    }

    override fun stop() {
        mysqlContainer!!.stop()
    }
}
