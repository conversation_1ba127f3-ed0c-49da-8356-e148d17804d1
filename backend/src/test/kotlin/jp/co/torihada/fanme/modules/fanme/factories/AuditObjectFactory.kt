package jp.co.torihada.fanme.modules.fanme.factories

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType

@ApplicationScoped
class AuditObjectFactory @Inject constructor() {
    companion object {
        fun new(
            auditGroupId: Long,
            bucket: String,
            filePath: String,
            assetType: AssetType,
        ): AuditObject {
            return AuditObject().apply {
                this.auditGroupId = auditGroupId
                this.bucket = bucket
                this.filePath = filePath
                this.assetType = assetType
            }
        }
    }
}
