package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class CreateBankIdTransferTest {

    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig

    @InjectMock @RestClient lateinit var extGmoTransferClient: ExtGmoTransferClient

    @Inject lateinit var mockExtGmoTransferClient: MockExtGmoTransferClient

    @Inject lateinit var createBankIdTransfer: CreateBankIdTransfer

    @BeforeEach
    fun setup() {
        mockExtGmoTransferClient.setUpMocks(extGmoTransferClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test create bankId transfer`() {
        val userId = "sellerUserId"

        val sellerAccountBalance =
            SellerAccountBalanceFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = userId,
                amount = 1000,
                accumulatedSales = 1000,
            )

        sellerAccountBalance.persist()

        createBankIdTransfer.execute(CreateBankIdTransfer.Input(userId = userId)).getOrElse {
            throw Exception("Failed to create bankId transfer")
        }

        // DB確認
        val sellerGmoTransfer = SellerGmoTransfer.findLast()
        sellerGmoTransfer?.let {
            assertEquals(it.status, Const.GmoTransferStatus.TransferNotRegistered.value)
            assertEquals(it.sellerUserId, userId)
            assertEquals(it.amount, 560)
        }
    }
}
