package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.TenantAccountBalance

object TenantAccountBalanceFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        amount: Int = 0,
    ): TenantAccountBalance {
        return TenantAccountBalance().apply {
            this.id = id
            this.tenant = tenant
            this.amount = amount
        }
    }
}
