package jp.co.torihada.fanme.modules.payment.usecases.payments.cvs

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.auth.MockExtAuthClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Tip
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseEntryTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.CreateCheckout
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class CvsPaymentTest {
    @Inject lateinit var commonConfig: CommonConfig
    @Inject lateinit var paymentConfig: PaymentConfig
    @Inject lateinit var mockExtGmoPaymentClient: MockExtGmoPaymentClient
    @Inject lateinit var mockExtAuthClient: MockExtAuthClient
    @Inject lateinit var createCheckout: CreateCheckout
    @Inject lateinit var entryTransaction: EntryTransaction
    @Inject lateinit var execTransaction: ExecTransaction

    @InjectMock @RestClient lateinit var extGmoPaymentClient: ExtGmoPaymentClient
    @InjectMock @RestClient lateinit var extAuthClient: ExtAuthClient

    @BeforeEach
    fun setUp() {
        mockExtGmoPaymentClient.setupMocks(extGmoPaymentClient, commonConfig, paymentConfig, "cvs")
        mockExtAuthClient.setupMocks(extAuthClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test cvs payment success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout
            .execute(
                CreateCheckout.Input(
                    sellerUserId = sellerUserId,
                    purchaserUserId = purchaserUserId,
                    totalAmount = 1100,
                    itemsAmount = 1000,
                    profit = 820,
                    fee = 280,
                    isDigital = true,
                    deliveryFee = 0,
                    tipAmount = 100,
                    paymentType = PaymentConst.PaymentType.CONVENIENCE,
                    convenience = PaymentConst.Conveniences.LAWSON,
                )
            )
            .getOrElse { throw it }

        val checkout = Checkout.findLast()

        entryTransaction
            .execute(
                BaseEntryTransaction.Input(
                    checkoutId = checkout?.id ?: throw Exception("Failed to create checkout"),
                    orderId = checkout.orderId ?: throw Exception("Failed to create checkout"),
                    totalAmount = checkout.total ?: throw Exception("Failed to create checkout"),
                )
            )
            .getOrElse { throw it }

        execTransaction
            .execute(
                ExecTransaction.Input(
                    orderId = checkout.orderId!!,
                    checkoutId = checkout.id!!,
                    customerKana = "ヤマダタロウ",
                    customerName = "山田太郎",
                    telNo = "09012345678",
                )
            )
            .getOrElse { throw it }

        // DB確認
        val tip = Tip.findLast()

        checkout.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            assertEquals(it.total, 1375)
            assertEquals(it.amount, 1000)
            assertEquals(it.cvsFee, 275)
            assertEquals(it.status, Const.CheckoutStatus.REQSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }
    }

    @Test
    @TestTransaction
    fun `test cvs payment for physical item success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout
            .execute(
                CreateCheckout.Input(
                    sellerUserId = sellerUserId,
                    purchaserUserId = purchaserUserId,
                    totalAmount = 1500,
                    itemsAmount = 1000,
                    profit = 820,
                    fee = 680,
                    isDigital = false,
                    deliveryFee = 400,
                    tipAmount = 100,
                    paymentType = PaymentConst.PaymentType.CONVENIENCE,
                    convenience = PaymentConst.Conveniences.LAWSON,
                )
            )
            .getOrElse { throw it }

        val checkout = Checkout.findLast()

        entryTransaction
            .execute(
                BaseEntryTransaction.Input(
                    checkoutId = checkout?.id ?: throw Exception("Failed to create checkout"),
                    orderId = checkout.orderId ?: throw Exception("Failed to create checkout"),
                    totalAmount = checkout.total ?: throw Exception("Failed to create checkout"),
                )
            )
            .getOrElse { throw it }

        execTransaction
            .execute(
                ExecTransaction.Input(
                    orderId = checkout.orderId!!,
                    checkoutId = checkout.id!!,
                    customerKana = "ヤマダタロウ",
                    customerName = "山田太郎",
                    telNo = "09012345678",
                )
            )
            .getOrElse { throw it }

        // DB確認
        val tip = Tip.findLast()

        checkout.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            // TODO 物販の審査が通ったら、gmoProductShopIdに変更する
            assertEquals(it.providerShopId, paymentConfig.gmoShopId())
            assertEquals(it.total, 1775)
            assertEquals(it.amount, 1000)
            assertEquals(it.cvsFee, 275)
            assertEquals(it.deliveryFee, 400)
            assertEquals(it.status, Const.CheckoutStatus.REQSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }
    }
}
