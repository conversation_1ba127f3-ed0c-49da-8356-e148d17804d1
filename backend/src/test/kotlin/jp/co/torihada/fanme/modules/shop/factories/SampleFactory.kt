package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.Sample

object SampleFactory {
    fun new(
        itemId: Long,
        name: String = "サンプルファイル",
        objectUri: String = "https://example.com/sample.mp4",
        thumbnailUri: String? = "https://example.com/thumbnail.webp",
        fileType: String = "video",
        size: Float = 1024.0f,
        duration: Int = 60,
    ): Sample {
        return Sample().apply {
            this.item = Item.findById(itemId)!!
            this.name = name
            this.objectUri = objectUri
            this.thumbnailUri = thumbnailUri
            this.fileType = fileType
            this.size = size
            this.duration = duration
        }
    }
}
