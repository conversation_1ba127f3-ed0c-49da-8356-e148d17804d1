package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserContentBlockUpdateEndpointTest {

    private var testUserId: Long = 0
    private var testUserAccountIdentity: String = ""
    private var testUserUid: String = ""
    private var testContentBlockDetailId: Long = 0

    private var otherUserId: Long = 0
    private var otherUserAccountIdentity: String = ""
    private var otherUserUid: String = ""
    private var otherContentBlockDetailId: Long = 0

    private var agentUserId: Long = 0
    private var agentUserUid: String = ""
    private var agentAgencyId: Long = 0

    private var differentAgencyId: Long = 0
    private var differentAgencyUserId: Long = 0
    private var differentAgencyUserAccountIdentity: String = ""
    private var differentAgencyUserUid: String = ""
    private var differentAgencyContentBlockDetailId: Long = 0

    @BeforeAll
    @Transactional
    fun setupAll() {
        val testAgency = AgencyFactory.new("Test Agency Update").apply { persistAndFlush() }
        agentAgencyId = testAgency.id!!

        val testUser =
            UserFactory.createTestUser(
                "ucb-update-test-user-1",
                "Test User Update",
                "ucb-update-test-user-1",
            )!!
        testUserId = testUser.id!!
        testUserAccountIdentity = testUser.accountIdentity!!
        testUserUid = testUser.uid!!

        ConsoleUserFactory.new(
                creatorId = testUserId,
                agencyId = testAgency.id,
                role = UserRole.BIZ_VALUE,
            )
            .persistAndFlush()

        val otherUser =
            UserFactory.createTestUser(
                "ucb-update-test-user-2",
                "Other User Update",
                "ucb-update-test-user-2",
            )!!
        otherUserId = otherUser.id!!
        otherUserAccountIdentity = otherUser.accountIdentity!!
        otherUserUid = otherUser.uid!!

        ConsoleUserFactory.new(
                creatorId = otherUserId,
                agencyId = testAgency.id,
                role = UserRole.BIZ_VALUE,
            )
            .persistAndFlush()

        val agentUser =
            UserFactory.createTestUser(
                "ucb-update-agent-user",
                "Agent User Update",
                "ucb-update-agent-user",
            )!!
        agentUserId = agentUser.id!!
        agentUserUid = agentUser.uid!!

        ConsoleUserFactory.new(
                creatorId = agentUserId,
                agencyId = testAgency.id,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()

        val differentAgency =
            AgencyFactory.new("Different Agency Update").apply { persistAndFlush() }
        differentAgencyId = differentAgency.id!!

        val differentAgencyUser =
            UserFactory.createTestUser(
                "ucb-update-diff-agency-user",
                "Different Agency User",
                "ucb-update-diff-agency-user",
            )!!
        differentAgencyUserId = differentAgencyUser.id!!
        differentAgencyUserAccountIdentity = differentAgencyUser.accountIdentity!!
        differentAgencyUserUid = differentAgencyUser.uid!!

        ConsoleUserFactory.new(
                creatorId = differentAgencyUserId,
                agencyId = differentAgency.id,
                role = UserRole.BIZ_VALUE,
            )
            .persistAndFlush()
    }

    @BeforeEach
    @Transactional
    fun setupEach() {

        val testContentBlock =
            ContentBlock().apply {
                user = User.findById(testUserId)
                contentBlockTypeId = 1L
                displayOrderNumber = 1
                displayable = true
                persistAndFlush()
            }

        val testContentBlockDetail =
            ContentBlockDetail().apply {
                title = "Original Title"
                description = "Original Description"
                appDescription = "Original App Description"
                url = "https://example.com/original"
                icon = "https://example.com/original-icon.png"
                persistAndFlush()
            }
        testContentBlockDetailId = testContentBlockDetail.id!!

        ContentBlockGroup().apply {
            contentBlock = testContentBlock
            contentBlockDetail = testContentBlockDetail
            contentGroupNumber = 1
            persistAndFlush()
        }

        val otherContentBlock =
            ContentBlock().apply {
                user = User.findById(otherUserId)
                contentBlockTypeId = 1L
                displayOrderNumber = 1
                displayable = true
                persistAndFlush()
            }

        val otherContentBlockDetail =
            ContentBlockDetail().apply {
                title = "Other User Title"
                description = "Other User Description"
                appDescription = "Other User App Description"
                url = "https://example.com/other"
                icon = "https://example.com/other-icon.png"
                persistAndFlush()
            }
        otherContentBlockDetailId = otherContentBlockDetail.id!!

        ContentBlockGroup().apply {
            contentBlock = otherContentBlock
            contentBlockDetail = otherContentBlockDetail
            contentGroupNumber = 1
            persistAndFlush()
        }

        val differentAgencyContentBlock =
            ContentBlock().apply {
                user = User.findById(differentAgencyUserId)
                contentBlockTypeId = 1L
                displayOrderNumber = 1
                displayable = true
                persistAndFlush()
            }

        val differentAgencyContentBlockDetail =
            ContentBlockDetail().apply {
                title = "Different Agency Title"
                description = "Different Agency Description"
                appDescription = "Different Agency App Description"
                url = "https://example.com/different-agency"
                icon = "https://example.com/different-agency-icon.png"
                persistAndFlush()
            }
        differentAgencyContentBlockDetailId = differentAgencyContentBlockDetail.id!!

        ContentBlockGroup().apply {
            contentBlock = differentAgencyContentBlock
            contentBlockDetail = differentAgencyContentBlockDetail
            contentGroupNumber = 1
            persistAndFlush()
        }
    }

    @AfterEach
    @Transactional
    fun cleanupEach() {
        ContentBlockGroup.deleteAll()
        ContentBlockDetail.deleteAll()
        ContentBlock.deleteAll()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ConsoleUser.deleteAll()
        Agency.deleteAll()
        User.delete("uid LIKE ?1", "ucb-update-%")
    }

    @Test
    @DisplayName("AGENTユーザーが同じエージェンシー内のコンテンツブロック詳細を更新できることを確認")
    @TestSecurity(
        user = "ucb-update-agent-user",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "ucb-update-agent-user")],
    )
    @TestTransaction
    fun agentUserCanUpdateContentBlockDetailForSameAgency() {
        val requestBody =
            """
            {
                "contentBlockDetailId": $testContentBlockDetailId,
                "title": "Agent Updated Title",
                "description": "Agent Updated Description",
                "appDescription": "Agent Updated App Description",
                "url": "https://example.com/agent-update",
                "iconUrl": "https://example.com/agent-update-icon.png"
            }
        """
                .trimIndent()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/users/$testUserAccountIdentity/content-blocks")
            .then()
            .statusCode(200)
            .body("data.contentBlockDetail", notNullValue())
            .body("data.contentBlockDetail.id", equalTo(testContentBlockDetailId.toInt()))
            .body("data.contentBlockDetail.title", equalTo("Agent Updated Title"))

        val detail = ContentBlockDetail.findById(testContentBlockDetailId)
        assert(detail?.title == "Agent Updated Title")
    }

    @Test
    @DisplayName("AGENTユーザーが別のエージェンシーのコンテンツブロック詳細を更新できないことを確認（403エラー）")
    @TestSecurity(
        user = "ucb-update-agent-user",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "ucb-update-agent-user")],
    )
    @TestTransaction
    fun agentUserCannotUpdateContentBlockDetailForDifferentAgency() {
        val requestBody =
            """
            {
                "contentBlockDetailId": $differentAgencyContentBlockDetailId,
                "title": "Agent Should Not Update Different Agency",
                "description": "Agent Should Not Update Different Agency",
                "appDescription": "Agent Should Not Update Different Agency",
                "url": "https://example.com/agent-not-update-diff",
                "iconUrl": "https://example.com/agent-not-update-diff-icon.png"
            }
        """
                .trimIndent()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/users/$differentAgencyUserAccountIdentity/content-blocks")
            .then()
            .statusCode(403)
            .body("errors[0].message", equalTo("Access denied to users from different agency"))

        val detail = ContentBlockDetail.findById(differentAgencyContentBlockDetailId)
        assert(detail?.title == "Different Agency Title")
    }
}
