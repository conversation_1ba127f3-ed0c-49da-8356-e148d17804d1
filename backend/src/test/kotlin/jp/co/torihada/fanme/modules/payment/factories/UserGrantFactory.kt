package jp.co.torihada.fanme.modules.payment.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.payment.models.UserGrant

object UserGrantFactory {
    fun new(
        userUid: String,
        name: String? = null,
        amount: Int = 0,
        paymentDueDate: Instant? = null,
    ): UserGrant {
        return UserGrant().apply {
            this.userUid = userUid
            this.name = name
            this.amount = amount
            this.paymentDueDate = paymentDueDate
        }
    }
}
