package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.TenantSale
import jp.co.torihada.fanme.modules.payment.models.Transaction

object TenantSaleFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        transaction: Transaction? = null,
        amount: Int? = null,
    ): TenantSale {
        return TenantSale().apply {
            this.id = id
            this.tenant = tenant
            this.transaction = transaction
            this.amount = amount ?: 1000
        }
    }
}
