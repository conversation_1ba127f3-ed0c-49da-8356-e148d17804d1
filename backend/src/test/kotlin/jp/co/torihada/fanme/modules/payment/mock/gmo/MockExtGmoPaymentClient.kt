package jp.co.torihada.fanme.modules.payment.mock.gmo

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.*
import jp.co.torihada.fanme.modules.payment.mock.CustomArgumentMatchers
import org.mockito.ArgumentMatchers
import org.mockito.Mockito

@ApplicationScoped
class MockExtGmoPaymentClient {

    fun createExecTranRequest(
        paymentType: String,
        commonConfig: CommonConfig,
        paymentConfig: PaymentConfig,
    ): ExecTransaction.Request {
        return if (paymentType == "google") {
            ExecTransaction.Request(
                siteId = paymentConfig.gmoSiteId(),
                sitePass = paymentConfig.gmoSitePass(),
                orderId = "sampleOrderId",
                accessId = "sampleAccessId",
                accessPass = "samplePass",
                memberId = "purchaserUserId@" + commonConfig.tenant(),
                method = "1",
                token = "sampleToken",
                tokenType = "2",
            )
        } else {
            ExecTransaction.Request(
                siteId = paymentConfig.gmoSiteId(),
                sitePass = paymentConfig.gmoSitePass(),
                orderId = "sampleOrderId",
                accessId = "sampleAccessId",
                accessPass = "samplePass",
                memberId = "purchaserUserId@" + commonConfig.tenant(),
                method = "1",
                cardSeq = 1,
            )
        }
    }

    fun setupMocks(
        extGmoPaymentClient: ExtGmoPaymentClient,
        commonConfig: CommonConfig,
        paymentConfig: PaymentConfig,
        paymentType: String,
    ) {
        val shopId = paymentConfig.gmoShopId()
        val shopPass = paymentConfig.gmoShopPass()
        Mockito.`when`(
                extGmoPaymentClient.entryTransaction(
                    body = CustomArgumentMatchers.any<EntryTransaction.Request>()
                )
            )
            .thenReturn(
                EntryTransaction.Response(accessId = "sampleAccessId", accessPass = "samplePass")
            )

        Mockito.`when`(
                extGmoPaymentClient.execTransaction(
                    createExecTranRequest(paymentType, commonConfig, paymentConfig)
                )
            )
            .thenReturn(
                ExecTransaction.Response(
                    acs = "sampleAcs",
                    orderId = "sampleOrderId",
                    forward = "forward",
                    method = "1",
                    payTimes = 0,
                    approve = "sampleApprove",
                    tranId = "sampleTranId",
                    tranDate = "sampleTranDate",
                    checkString = "sampleCheckString",
                    clientField1 = "sampleClientField1",
                    clientField2 = "sampleClientField2",
                    clientField3 = "sampleClientField3",
                    redirectUrl = "sampleRedirectUrl",
                )
            )

        Mockito.`when`(
                extGmoPaymentClient.execTransactionCvs(
                    ExecTransactionCvs.Request(
                        accessId = "sampleAccessId",
                        accessPass = "sample",
                        orderId = "sampleOrderId",
                        convenience = "10001", // セブンイレブン
                        customerKana = "ヤマダタロウ",
                        customerName = "山田太郎",
                        telNo = "09012345678",
                        mailAddress = "sampleEmail",
                        receiptsDisp11 = "株式会社TORIHADA",
                        receiptsDisp12 = "03-6455-4120",
                        receiptsDisp13 = "10:00-19:00",
                    )
                )
            )
            .thenReturn(
                ExecTransactionCvs.Response(
                    orderId = "sampleOrderId",
                    convenience = "sampleConvenience",
                    confNo = "sampleConfNo",
                    receiptNo = "sampleReceiptNo",
                    paymentTerm = "23001015123000",
                    tranDate = "sampleTranDate",
                    receiptUrl = "sampleReceiptUrl",
                    checkString = "sampleCheckString",
                    clientField1 = "sampleClientField1",
                    clientField2 = "sampleClientField2",
                    clientField3 = "sampleClientField3",
                )
            )

        Mockito.`when`(
                extGmoPaymentClient.entryTransactionCvs(
                    body = CustomArgumentMatchers.any<EntryTransactionCvs.Request>()
                )
            )
            .thenReturn(
                EntryTransaction.Response(accessId = "sampleAccessId", accessPass = "sample")
            )

        Mockito.`when`(
                extGmoPaymentClient.entryTranBrandToken(
                    shopId = CustomArgumentMatchers.eq<String>(shopId),
                    shopPass = CustomArgumentMatchers.eq<String>(shopPass),
                    orderId = ArgumentMatchers.anyString(),
                    amount = ArgumentMatchers.anyInt(),
                    jobCd = CustomArgumentMatchers.any<BaseEntryTransaction.JobCd>(),
                )
            )
            .thenReturn(
                listOf("AccessID" to "sampleAccessId", "AccessPass" to "sample").joinToString(
                    "&"
                ) { (key, value) ->
                    "$key=$value"
                }
            )

        Mockito.`when`(
                extGmoPaymentClient.execTransactionBrandToken(
                    shopId = shopId,
                    shopPass = shopPass,
                    accessId = "sampleAccessId",
                    accessPass = "sample",
                    orderId = "sampleOrderId",
                    tokenType = "APay",
                    token = "sampleToken",
                    siteId = paymentConfig.gmoSiteId(),
                    sitePass = paymentConfig.gmoSitePass(),
                )
            )
            .thenReturn(
                listOf(
                        "OrderID" to "sampleOrderId",
                        "Forward" to "forward",
                        "Approve" to "sampleApprove",
                        "TranID" to "sampleTranId",
                        "TranDate" to "sampleTranDate",
                        "ClientField1" to "sampleClientField1",
                        "ClientField2" to "sampleClientField2",
                        "ClientField3" to "sampleClientField3",
                    )
                    .joinToString("&") { (key, value) -> "$key=$value" }
            )

        Mockito.`when`(
                extGmoPaymentClient.entryTransactionPayPay(
                    body = CustomArgumentMatchers.any<EntryTransactionPayPay.Request>()
                )
            )
            .thenReturn(
                EntryTransaction.Response(accessId = "sampleAccessId", accessPass = "samplePass")
            )

        Mockito.`when`(
                extGmoPaymentClient.execTransactionPayPay(
                    ExecTransactionPayPay.Request(
                        shopId = shopId,
                        shopPass = shopPass,
                        accessId = "sampleAccessId",
                        accessPass = "samplePass",
                        orderId = "sampleOrderId",
                        retUrl = "https://example.com",
                    )
                )
            )
            .thenReturn(
                ExecTransactionPayPay.Response(
                    startUrl = "https://sampleStartUrl",
                    token = "sampleToken",
                    accessId = "sampleAccessId",
                    startLimitDate = "23001015123000",
                )
            )
    }
}
