package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover

object ProfileCoverFactory {
    fun new(
        profile: Profile,
        brightness: String = Const.Brightness.NORMAL.value,
        coverVisibility: Boolean = true,
    ): ProfileCover {
        return ProfileCover().apply {
            this.profile = profile
            this.brightness = brightness
            this.coverVisibility = coverVisibility
        }
    }
}
