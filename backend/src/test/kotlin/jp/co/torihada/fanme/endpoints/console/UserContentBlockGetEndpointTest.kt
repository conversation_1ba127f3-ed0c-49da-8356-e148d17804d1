package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockDetailFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserContentBlockGetEndpointTest {

    private var testUserId: Long = 0
    private var testUserAccountIdentity: String = ""
    private var agentUserId: Long = 0

    @BeforeAll
    @Transactional
    fun setupAll() {

        val testAgency = AgencyFactory.new("Test Agency").apply { persistAndFlush() }

        val testUser =
            UserFactory.createTestUser("ucb-test-user-1", "Test User", "<EMAIL>")!!
        testUserId = testUser.id!!
        testUserAccountIdentity = testUser.accountIdentity!!

        ConsoleUserFactory.new(
                creatorId = testUserId,
                agencyId = testAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
            .persistAndFlush()

        val otherAgency = AgencyFactory.new("Other Agency").apply { persistAndFlush() }

        val agentUser =
            UserFactory.createTestUser("agent-other", "Agent Other", "<EMAIL>")!!
        agentUserId = agentUser.id!!

        ConsoleUserFactory.new(
                creatorId = agentUserId,
                agencyId = otherAgency.id,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()

        createContentBlocksForUser(testUser)
    }

    private fun createContentBlocksForUser(user: User) {
        val contentBlock1 =
            ContentBlockFactory.new(
                    user = user,
                    contentBlockTypeId = 1L,
                    displayOrderNumber = 1,
                    displayable = true,
                )
                .apply { persistAndFlush() }

        val contentBlock2 =
            ContentBlockFactory.new(
                    user = user,
                    contentBlockTypeId = 2L,
                    displayOrderNumber = 2,
                    displayable = true,
                )
                .apply { persistAndFlush() }

        ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = 1L,
                displayOrderNumber = 3,
                displayable = false,
            )
            .apply { persistAndFlush() }

        val detail1 =
            ContentBlockDetailFactory.new(
                    title = "Test Content ${user.name}",
                    description = "Test Description",
                    url = "https://example.com",
                    icon = "icon1.png",
                )
                .apply { persistAndFlush() }

        val detail2 =
            ContentBlockDetailFactory.new(
                    title = "Another Content ${user.name}",
                    description = "Another Description",
                    url = "https://example2.com",
                    icon = null,
                )
                .apply { persistAndFlush() }

        ContentBlockGroupFactory.new(
                contentBlock = contentBlock1,
                contentBlockDetail = detail1,
                contentGroupNumber = 1,
            )
            .persistAndFlush()

        ContentBlockGroupFactory.new(
                contentBlock = contentBlock2,
                contentBlockDetail = detail2,
                contentGroupNumber = 1,
            )
            .persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ContentBlockGroup.deleteAll()
        ContentBlockDetail.deleteAll()
        ContentBlock.deleteAll()
        ConsoleUser.deleteAll()
        Agency.deleteAll()
        User.delete("uid LIKE ?1", "ucb-test-user-%")
        User.delete("uid LIKE ?1", "agent-other%")
    }

    @Test
    @TestSecurity(
        user = "agent-other",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-other")],
    )
    fun agentCannotAccessCreatorOfOtherAgency() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$testUserAccountIdentity/content-blocks")
            .then()
            .statusCode(403)
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun contentBlocksIncludeAllExpectedFields() {
        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/users/$testUserAccountIdentity/content-blocks")
                .then()
                .statusCode(200)
                .extract()
                .response()

        response
            .then()
            .body("data.contentBlocks", notNullValue())
            .body("data.contentBlocks.size()", equalTo(3))
            .body("data.contentBlocks[0].id", notNullValue())
            .body(
                "data.contentBlocks[0].contentBlockDetails[0].title",
                equalTo("Test Content Test User"),
            )
            .body(
                "data.contentBlocks[0].contentBlockDetails[0].description",
                equalTo("Test Description"),
            )
            .body("data.contentBlocks[0].displayOrderNumber", equalTo(1))
            .body("data.contentBlocks[0].displayable", equalTo(true))
            .body("data.contentBlocks[1].id", notNullValue())
            .body(
                "data.contentBlocks[1].contentBlockDetails[0].title",
                equalTo("Another Content Test User"),
            )
            .body(
                "data.contentBlocks[1].contentBlockDetails[0].description",
                equalTo("Another Description"),
            )
            .body("data.contentBlocks[1].displayOrderNumber", equalTo(2))
            .body("data.contentBlocks[1].displayable", equalTo(true))
            .body("data.contentBlocks[2].displayOrderNumber", equalTo(3))
            .body("data.contentBlocks[2].displayable", equalTo(false))
    }
}
