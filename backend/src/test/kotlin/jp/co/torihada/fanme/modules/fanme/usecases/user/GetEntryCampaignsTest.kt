package jp.co.torihada.fanme.modules.fanme.usecases.user

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.factories.CampaignEntryFactory
import jp.co.torihada.fanme.modules.fanme.factories.CampaignFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class GetEntryCampaignsTest {
    @Inject lateinit var getEntryCampaigns: GetEntryCampaigns

    @Test
    @TestTransaction
    fun `参加キャンペーンが存在しない場合は空リストを返す`() {
        // Given
        val user =
            UserFactory.new(
                uid = "test-user-uid-1",
                name = "Test User 1",
                accountIdentity = "test-user-account-identity-1",
            )
        user.persistAndFlush()

        // When
        val input = user.uid?.let { GetEntryCampaigns.Input(it) }
        if (input == null) throw IllegalStateException("User UID should not be null")
        val result = getEntryCampaigns.execute(input).unwrap()

        // Then
        assertEquals(0, result.size)
    }

    @Test
    @TestTransaction
    fun `有効な参加キャンペーンが返される`() {
        // Given
        val user =
            UserFactory.new(
                uid = "test-user-uid-2",
                name = "Test User 2",
                accountIdentity = "test-user-account-identity-2",
            )
        user.persistAndFlush()
        val campaign =
            CampaignFactory.new(campaignIdentity = "test-entry-campaign-1", actionDurationDays = 3)
        campaign.persistAndFlush()
        val entry =
            CampaignEntryFactory.new(
                campaign = campaign,
                user = user,
                enteredAt = Instant.now().minusSeconds(60), // 1分前
            )
        entry.persistAndFlush()

        // When
        val input = user.uid?.let { GetEntryCampaigns.Input(it) }
        if (input == null) throw IllegalStateException("User UID should not be null")
        val result = getEntryCampaigns.execute(input).unwrap()

        // Then
        assertEquals(1, result.size)
        assertEquals(campaign.campaignIdentity, result[0].campaignIdentity)
    }

    @Test
    @TestTransaction
    fun `期限切れの参加キャンペーンは返されない`() {
        // Given
        val user =
            UserFactory.new(
                uid = "test-user-uid-3",
                name = "Test User 3",
                accountIdentity = "test-user-account-identity-3",
            )
        user.persistAndFlush()
        val campaign =
            CampaignFactory.new(campaignIdentity = "test-entry-campaign-2", actionDurationDays = 1)
        campaign.persistAndFlush()
        val entry =
            CampaignEntryFactory.new(
                campaign = campaign,
                user = user,
                enteredAt = Instant.now().minusSeconds(2 * 86400), // 2日前
            )
        entry.persistAndFlush()

        // When
        val input = user.uid?.let { GetEntryCampaigns.Input(it) }
        if (input == null) throw IllegalStateException("User UID should not be null")
        val result = getEntryCampaigns.execute(input).unwrap()

        // Then
        assertTrue(result.isEmpty())
    }
}
