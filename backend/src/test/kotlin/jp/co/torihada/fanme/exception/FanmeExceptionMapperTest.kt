package jp.co.torihada.fanme.exception

import io.quarkus.test.junit.QuarkusTest
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.BaseResponseBody
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class MultipleFanmeException(errorList: List<ErrorObject>) :
    FanmeException(500, "Multiple errors") {
    init {
        val errorsField = FanmeException::class.java.getDeclaredField("errors")
        errorsField.isAccessible = true
        errorsField.set(this, errorList)
    }
}

@QuarkusTest
class FanmeExceptionMapperTest {

    private val mapper = FanmeExceptionMapper()

    @Test
    fun `test multiple errors return as array`() {
        val errors =
            listOf(
                ErrorObject(400, "Invalid field1"),
                ErrorObject(401, "Invalid field2"),
                ErrorObject(402, "Invalid field3"),
            )
        val exception = MultipleFanmeException(errors)

        val response = mapper.toResponse(exception)

        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.statusCode, response.status)

        val body = response.entity as BaseResponseBody<*>
        assertNotNull(body.errors)
        assertEquals(3, body.errors!!.size)

        assertEquals(400, body.errors!![0].code)
        assertEquals("Invalid field1", body.errors!![0].message)

        assertEquals(401, body.errors!![1].code)
        assertEquals("Invalid field2", body.errors!![1].message)

        assertEquals(402, body.errors!![2].code)
        assertEquals("Invalid field3", body.errors!![2].message)
    }
}
