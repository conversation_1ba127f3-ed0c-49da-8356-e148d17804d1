package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.models.UserState

object UserStateFactory {

    fun new(user: User, key: String, value: String, data: String? = null): UserState {
        return UserState().apply {
            this.user = user
            this.key = key
            this.value = value
            this.data = data
        }
    }
}
