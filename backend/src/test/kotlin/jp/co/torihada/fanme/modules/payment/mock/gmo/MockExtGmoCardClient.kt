package jp.co.torihada.fanme.modules.payment.mock.gmo

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoCardClient
import jp.co.torihada.fanme.modules.payment.externals.entity.card.DeleteCard
import jp.co.torihada.fanme.modules.payment.externals.entity.card.SavedCard
import jp.co.torihada.fanme.modules.payment.externals.entity.card.SearchCard
import org.mockito.Mockito

@ApplicationScoped
class MockExtGmoCardClient {
    val logger = org.jboss.logging.Logger.getLogger(MockExtGmoCardClient::class.java)

    fun setupMocks(
        extGmoCardClient: ExtGmoCardClient,
        commonConfig: CommonConfig,
        paymentConfig: PaymentConfig,
    ) {

        val shopId = paymentConfig.gmoSiteId()
        val shopPass = paymentConfig.gmoSitePass()
        val tenant = commonConfig.tenant()

        Mockito.`when`(
                extGmoCardClient.savedCard(
                    SavedCard.Request(
                        siteId = shopId,
                        sitePass = shopPass,
                        memberId = "sampleUserId@" + tenant,
                        seqMode = "0",
                        defaultFlag = "0",
                        updateType = "1",
                        token = "sampleToken",
                        cardName = "sampleCardName",
                    )
                )
            )
            .thenReturn(SavedCard.Response(seqMode = "0", cardNo = "sampleCardNo"))

        Mockito.`when`(
                extGmoCardClient.deleteCard(
                    DeleteCard.Request(
                        siteId = shopId,
                        sitePass = shopPass,
                        memberId = "sampleUserId@" + tenant,
                        cardSeq = 0,
                        seqMode = "0",
                    )
                )
            )
            .thenReturn(DeleteCard.Response(cardSeq = "0"))

        Mockito.`when`(
                extGmoCardClient.searchCard(
                    SearchCard.Request(
                        siteId = shopId,
                        sitePass = shopPass,
                        memberId = "sampleUserId@" + tenant,
                        seqMode = "0",
                    )
                )
            )
            .thenReturn(
                listOf(
                    SearchCard.Response(
                        cardSeq = 0,
                        defaultFlag = "0",
                        cardName = "sampleCardName",
                        cardNo = "sampleCardNo",
                        expire = "2300-01-01",
                        holderName = "sampleCardHolderName",
                        brand = "sampleCardBrand",
                        domesticFlag = "sampleDomesticFlag",
                        debitPrepaidIssuerName = "sampleDebitPrepaidIssuerName",
                        forwardFinal = "sampleForwardFinal",
                    )
                )
            )
    }
}
