package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.*
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserContentBlockCreateEndpointTest {

    private var testUserId: Long = 0
    private var testUserAccountIdentity: String = ""
    private var sameAgencyUserId: Long = 0
    private var sameAgencyAgentId: Long = 0
    private var differentAgencyUserId: Long = 0
    private var differentAgencyUserAccountIdentity: String = ""

    @BeforeAll
    @Transactional
    fun setupAll() {
        val testAgency = AgencyFactory.new("Test Agency").apply { persistAndFlush() }

        val testUser =
            UserFactory.createTestUser(
                "ucb-create-test-user-1",
                "Test User",
                "<EMAIL>",
            )!!
        testUserId = testUser.id!!
        testUserAccountIdentity = testUser.accountIdentity!!

        ConsoleUserFactory.new(
                creatorId = testUserId,
                agencyId = testAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
            .persistAndFlush()

        val sameAgencyUser =
            UserFactory.createTestUser(
                "ucb-create-test-user-2",
                "Same Agency User",
                "<EMAIL>",
            )!!
        sameAgencyUserId = sameAgencyUser.id!!

        ConsoleUserFactory.new(
                creatorId = sameAgencyUserId,
                agencyId = testAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
            .persistAndFlush()

        val sameAgencyAgent =
            UserFactory.createTestUser(
                "agent-same-create",
                "Same Agency Agent",
                "<EMAIL>",
            )!!
        sameAgencyAgentId = sameAgencyAgent.id!!

        ConsoleUserFactory.new(
                creatorId = sameAgencyAgentId,
                agencyId = testAgency.id,
                role = UserRole.AGENT_VALUE,
            )
            .persistAndFlush()

        val differentAgency = AgencyFactory.new("Different Agency").apply { persistAndFlush() }

        val differentAgencyUser =
            UserFactory.createTestUser(
                "ucb-diff-agency-user",
                "Different Agency User",
                "<EMAIL>",
            )!!
        differentAgencyUserId = differentAgencyUser.id!!
        differentAgencyUserAccountIdentity = differentAgencyUser.accountIdentity!!

        ConsoleUserFactory.new(
                creatorId = differentAgencyUserId,
                agencyId = differentAgency.id,
                role = UserRole.CREATOR_VALUE,
            )
            .persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ContentBlockGroup.deleteAll()
        ContentBlockDetail.deleteAll()
        ContentBlock.deleteAll()
        ConsoleUser.deleteAll()
        Agency.deleteAll()
        User.delete("uid LIKE ?1", "ucb-create-test-user-%")
        User.delete("uid LIKE ?1", "agent-same-create%")
        User.delete("uid LIKE ?1", "ucb-diff-agency-user%")
    }

    @Test
    @DisplayName("SUPERロールは全項目を指定してコンテンツブロックを作成できる")
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    @Transactional
    fun superCanCreateContentBlock() {
        val requestBody =
            """
            {
                "contentBlockType": 1,
                "title": "New Test Content",
                "description": "New Test Description",
                "appDescription": "New App Description",
                "url": "https://example.com/new",
                "iconUrl": "https://example.com/icon.png"
            }
        """
                .trimIndent()

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .body(requestBody)
                .`when`()
                .post("/console/users/$testUserAccountIdentity/content-blocks")
                .then()
                .statusCode(200)
                .body("data.contentBlock", notNullValue())
                .body("data.contentBlock.id", notNullValue())
                .body("data.contentBlock.content_block_type_id", equalTo(1))
                .body("data.contentBlock.displayable", equalTo(true))
                .extract()
                .response()

        val contentBlockId = response.jsonPath().getLong("data.contentBlock.id")

        val savedContentBlock = ContentBlock.findById(contentBlockId)
        assert(savedContentBlock != null)
        assert(savedContentBlock?.user?.id == testUserId)
        assert(savedContentBlock?.contentBlockTypeId == 1L)
        assert(savedContentBlock?.displayable == true)

        val contentBlockGroup = savedContentBlock?.contentBlockGroups?.firstOrNull()
        assert(contentBlockGroup != null)
        val detail = contentBlockGroup?.contentBlockDetail
        assert(detail?.title == "New Test Content")
        assert(detail?.description == "New Test Description")
        assert(detail?.appDescription == "New App Description")
        assert(detail?.url == "https://example.com/new")
        assert(detail?.icon == "https://example.com/icon.png")
    }

    @Test
    @DisplayName("AGENTロールは異なるエージェンシーのユーザーにコンテンツブロックを作成できない")
    @TestSecurity(
        user = "agent-same-create",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-same-create")],
    )
    @Transactional
    fun agentCannotCreateContentBlockForUserInDifferentAgency() {
        val requestBody =
            """
            {
                "contentBlockType": 1,
                "title": "Agent Attempted Content",
                "description": "Should fail",
                "appDescription": "Should fail",
                "url": "https://example.com/fail",
                "iconUrl": "https://example.com/fail-icon.png"
            }
        """
                .trimIndent()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .post("/console/users/$differentAgencyUserAccountIdentity/content-blocks")
            .then()
            .statusCode(403)

        // Verify no content block was created
        val contentBlocks = ContentBlock.list("user.id = ?1", differentAgencyUserId)
        assert(contentBlocks.isEmpty())
    }

    @Test
    @DisplayName("contentBlockTypeのみでコンテンツブロックを作成できる")
    @TestSecurity(
        user = "test-super",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-super")],
    )
    @Transactional
    fun canCreateContentBlockWithOnlyContentBlockType() {
        val requestBody =
            """
            {
                "contentBlockType": 1
            }
        """
                .trimIndent()

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .body(requestBody)
                .`when`()
                .post("/console/users/$testUserAccountIdentity/content-blocks")
                .then()
                .statusCode(200)
                .body("data.contentBlock", notNullValue())
                .body("data.contentBlock.id", notNullValue())
                .body("data.contentBlock.content_block_type_id", equalTo(1))
                .extract()
                .response()

        val contentBlockId = response.jsonPath().getLong("data.contentBlock.id")

        val savedContentBlock = ContentBlock.findById(contentBlockId)
        assert(savedContentBlock != null)
        assert(savedContentBlock?.contentBlockTypeId == 1L)

        val contentBlockGroup = savedContentBlock?.contentBlockGroups?.firstOrNull()
        assert(contentBlockGroup != null)
        val detail = contentBlockGroup?.contentBlockDetail
        assert(detail?.title == "")
        assert(detail?.url == null)
        assert(detail?.description == null)
        assert(detail?.appDescription == null)
        assert(detail?.icon == null)
    }
}
