package jp.co.torihada.fanme.modules.payment.factories

import jp.co.torihada.fanme.modules.payment.models.Tip

object TipFactory {
    fun new(
        id: Long? = null,
        purchaserUserId: String = "purchaserUserId",
        sellerUserId: String? = null,
        amount: Int? = null,
    ): Tip {
        return Tip().apply {
            this.id = id
            this.purchaserUserId = purchaserUserId ?: "purchaserUserId"
            this.sellerUserId = sellerUserId ?: "sellerUserId"
            this.amount = amount ?: 100
        }
    }
}
