package jp.co.torihada.fanme.modules.console.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.console.models.Agency

object AgencyFactory {
    fun new(name: String = "Test Agency", deletedAt: Instant? = null): Agency {
        return Agency().apply {
            this.name = name
            this.deletedAt = deletedAt
            this.createdAt = Instant.now()
            this.updatedAt = Instant.now()
        }
    }
}
