package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover
import jp.co.torihada.fanme.modules.fanme.models.ProfileThemeColor
import jp.co.torihada.fanme.modules.fanme.models.User

object ProfileFactory {

    fun new(
        user: User,
        bio: String = "Test bio",
        headerImage: String = "header.jpg",
        snsLinkColor: String = "ORIG",
        officialFlg: Boolean = false,
        themeColor: ProfileThemeColor? = null,
        profileCover: ProfileCover? = null,
    ): Profile {
        val profile =
            Profile().apply {
                this.user = user
                this.bio = bio
                this.headerImage = headerImage
                this.snsLinkColor = snsLinkColor
                this.officialFlg = officialFlg
            }

        if (themeColor != null) {
            profile.themeColor = themeColor
        }

        if (profileCover != null) {
            profile.cover = profileCover
        }

        return profile
    }
}
