package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.DisplayableCoverImage
import jp.co.torihada.fanme.modules.fanme.models.ProfileCover
import jp.co.torihada.fanme.modules.fanme.models.ProfileCoverImage

object DisplayableCoverImagesFactory {
    fun new(
        profileCover: ProfileCover,
        profileCoverImage: ProfileCoverImage,
    ): DisplayableCoverImage {
        return DisplayableCoverImage().apply {
            this.profileCover = profileCover
            this.profileCoverImage = profileCoverImage
        }
    }
}
