package jp.co.torihada.fanme.modules.payment.mock.gmo

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.AccountSearch
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.DepositRegistration
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.DepositSearch
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.LinkRedirectUrl
import org.mockito.Mockito

@ApplicationScoped
class MockExtGmoTransferClient {
    fun setUpMocks(extGmoTransferClient: ExtGmoTransferClient, config: Config) {
        Mockito.`when`(
                extGmoTransferClient.accountSearch(
                    AccountSearch.Request(
                        shopId = config.gmoTransferShopId(),
                        shopPass = config.gmoTransferShopPass(),
                        bankId = "sellerUserId",
                    )
                )
            )
            .thenReturn(
                AccountSearch.Response(
                    deleteFlag = "0",
                    bankName = "三菱東京UFJ銀行",
                    bankCode = "001",
                    branchName = "新宿支店",
                    branchCode = "001",
                    accountNumber = "1234567",
                    accountName = "トリハダジロウ",
                    branchCodeJpbank = "001",
                    accountNumberJpbank = "1234567",
                )
            )

        Mockito.`when`(
                extGmoTransferClient.depositRegistration(
                    DepositRegistration.Request(
                        shopId = config.gmoShopId(),
                        shopPass = config.gmoShopPass(),
                        bankId = "sellerUserId",
                        amount = 1000,
                        depositId = "testUUID",
                        method = "1",
                    )
                )
            )
            .thenReturn(
                DepositRegistration.Response(
                    depositId = "testUUID",
                    amount = 1000,
                    method = "1",
                    bankFee = 330,
                    bankId = "001",
                )
            )

        Mockito.`when`(
                extGmoTransferClient.linkRedirectUrl(
                    LinkRedirectUrl.Request(
                        shopId = config.gmoTransferShopId(),
                        shopPass = config.gmoTransferShopPass(),
                        depositId = "testUUID",
                        callBackUrl = "https://fanme.link",
                        amount = 560,
                        authCode = "testAuthCode",
                        remitMethodBank = "1",
                        remitMethodSevenatm = "0",
                        remitMethodAmazongift = "0",
                        method = "1",
                        bankId = "sellerUserId",
                    )
                )
            )
            .thenReturn(LinkRedirectUrl.Response(redirectUrl = "https://fanme.link"))

        Mockito.`when`(
                extGmoTransferClient.depositSearch(
                    DepositSearch.Request(
                        shopId = config.gmoTransferShopId(),
                        shopPass = config.gmoTransferShopPass(),
                        depositId = "1234567",
                    )
                )
            )
            .thenReturn(
                DepositSearch.Response(
                    depositId = "1234567",
                    amount = "1000",
                    free = "330",
                    bank =
                        DepositSearch.Bank(
                            bankId = "sellerUserId",
                            bankName = "三菱東京UFJ銀行",
                            branchName = "新宿支店",
                            accountType = "普通",
                            accountNumber = "1234567",
                            accountName = "トリハダジロウ",
                            result = "3",
                            resultDetail = "0000",
                        ),
                )
            )
    }
}
