package jp.co.torihada.fanme.modules.shop.controllers

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import jp.co.torihada.fanme.modules.shop.factories.OrderFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.odata.OData
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class OrderControllerTest {

    @Inject lateinit var orderController: OrderController

    @Test
    @TestTransaction
    fun `test getUserOrders returns empty list when no orders exist`() {
        val userId = "testUser1"
        val odata = OData(top = null, skip = null)

        val result = orderController.getUserOrders(userId = userId, odata = odata)

        assertNotNull(result)
        assertEquals(0, result.size)
    }

    @Test
    @TestTransaction
    fun `test getUserOrders returns orders for user`() {
        val userId = "testUser1"
        val shop = ShopFactory.new(creatorUid = "creator1").apply { persist() }

        OrderFactory.new(
                purchaserUid = userId,
                shopId = shop.id!!,
                transactionId = 1001L,
                checkoutId = 2001L,
            )
            .apply { persist() }

        OrderFactory.new(
                purchaserUid = userId,
                shopId = shop.id!!,
                transactionId = 1002L,
                checkoutId = 2002L,
            )
            .apply { persist() }

        OrderFactory.new(
                purchaserUid = "otherUser",
                shopId = shop.id!!,
                transactionId = 1003L,
                checkoutId = 2003L,
            )
            .apply { persist() }

        val result =
            orderController.getUserOrders(userId = userId, odata = OData(top = null, skip = null))

        assertNotNull(result)
        assertEquals(2, result.size)
        assertEquals(userId, result[0].purchaserUid)
        assertEquals(userId, result[1].purchaserUid)
    }

    @Test
    @TestTransaction
    fun `test getUserOrders with pagination`() {
        val userId = "testUser1"
        val shop = ShopFactory.new(creatorUid = "creator1").apply { persist() }

        repeat(5) { i ->
            OrderFactory.new(
                    purchaserUid = userId,
                    shopId = shop.id!!,
                    transactionId = 1000L + i,
                    checkoutId = 2000L + i,
                )
                .apply { persist() }
        }

        val resultWithLimit =
            orderController.getUserOrders(userId = userId, odata = OData(top = 3, skip = null))
        assertEquals(3, resultWithLimit.size)

        val resultWithOffset =
            orderController.getUserOrders(userId = userId, odata = OData(top = null, skip = 3))
        assertEquals(5, resultWithOffset.size)

        val resultWithBoth =
            orderController.getUserOrders(userId = userId, odata = OData(top = 2, skip = 2))
        assertEquals(2, resultWithBoth.size)
    }

    @Test
    @TestTransaction
    fun `test getOrders by transactionId returns specific order`() {
        val shop = ShopFactory.new(creatorUid = "creator1").apply { persist() }
        val transactionId = 1001L

        OrderFactory.new(
                purchaserUid = "testUser1",
                shopId = shop.id!!,
                transactionId = transactionId,
                checkoutId = 2001L,
            )
            .apply { persist() }

        OrderFactory.new(
                purchaserUid = "testUser2",
                shopId = shop.id!!,
                transactionId = 1002L,
                checkoutId = 2002L,
            )
            .apply { persist() }

        val result = orderController.getOrders(transactionId = transactionId)

        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals(transactionId, result[0].transactionId)
    }

    @Test
    @TestTransaction
    fun `test getOrders by transactionId returns empty list for non-existent transaction`() {
        val nonExistentTransactionId = 9999L

        val result = orderController.getOrders(transactionId = nonExistentTransactionId)

        assertNotNull(result)
        assertEquals(0, result.size)
    }
}
