package jp.co.torihada.fanme.modules.fanme.factories

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

annotation class TestInfo(val description: String)

/** UserFactoryのテストクラス テストユーザーの作成機能が正しく動作することを確認する */
@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserFactoryTest {

    private val testUidPrefix = "test-factory-user-"
    private val testAccountIdentityPrefix = "test.factory.user."
    private val testName = "Test Factory User"

    private fun getTestUid(suffix: String): String {
        return "$testUidPrefix$suffix"
    }

    private fun getTestAccountIdentity(suffix: String): String {
        return "$testAccountIdentityPrefix$<EMAIL>"
    }

    @Test
    @TestTransaction
    @TestInfo("新規ユーザー作成のテスト UserFactory.createTestUserが新しいユーザーを正しく作成できることを確認する")
    fun testCreateTestUserNewUser() {
        val uid = getTestUid("new")
        val accountIdentity = getTestAccountIdentity("new")

        val user = UserFactory.createTestUser(uid, testName, accountIdentity, 1)

        assertNotNull(user, "作成されたユーザーがnullでないこと")
        assertEquals(uid, user?.uid, "ユーザーのUIDが指定した値と一致すること")
        assertEquals(testName, user?.name, "ユーザーの名前が指定した値と一致すること")
        assertEquals(accountIdentity, user?.accountIdentity, "ユーザーのアカウント識別子が指定した値と一致すること")
        assertEquals("MALE", user?.gender, "ユーザーの性別がMALEに設定されていること")
        assertNotNull(user?.birthday, "ユーザーの誕生日が設定されていること")
        assertEquals(false, user?.birthdayConfirmed, "ユーザーの誕生日確認フラグがfalseに設定されていること")
        assertEquals(true, user?.isPublic, "ユーザーの公開フラグがtrueに設定されていること")
        assertEquals(false, user?.allowPublicSharing, "ユーザーの公開共有許可フラグがfalseに設定されていること")
        assertEquals(true, user?.filledProfile, "ユーザーのプロフィール入力完了フラグがtrueに設定されていること")
        assertEquals(1, user?.purpose, "ユーザーの目的が指定した値と一致すること")
    }

    @Test
    @TestTransaction
    @TestInfo("既存ユーザーの取得テスト 同じUIDで再度createTestUserを呼び出した場合、既存のユーザーが返されることを確認する")
    fun testCreateTestUserExistingUser() {
        val uid = getTestUid("existing")
        val accountIdentity = getTestAccountIdentity("existing")

        val firstUser = UserFactory.createTestUser(uid, testName, accountIdentity, 1)
        // 最初のユーザーが正常に作成されたことを確認
        assertNotNull(firstUser, "最初に作成されたユーザーがnullでないこと")

        val secondUser =
            UserFactory.createTestUser(uid, "Different Name", "<EMAIL>", 2)
        // 2回目の呼び出しで取得したユーザーが最初に作成したユーザーと同一であることを確認
        assertNotNull(secondUser, "2回目に取得したユーザーがnullでないこと")
        assertEquals(uid, secondUser?.uid, "UIDが一致すること")
        assertEquals(testName, secondUser?.name, "名前が最初のユーザーの名前と一致すること（引数の異なる名前ではない）")
        assertEquals(accountIdentity, secondUser?.accountIdentity, "アカウント識別子が一致すること")
        assertEquals(1, secondUser?.purpose, "目的が最初のユーザーの目的と一致すること（引数の異なる目的ではない）")
        assertEquals(firstUser?.id, secondUser?.id, "IDが一致し、同一のユーザーレコードであること")
    }

    @Test
    @TestTransaction
    @TestInfo("複数ユーザー作成のテスト createTestUsers関数が複数のテストユーザーを正しく作成できることを確認する")
    fun testCreateTestUsers() {
        val testUsers = createTestUsers()
        val uid1 = testUsers[0]
        val uid2 = testUsers[1]
        val uid3 = testUsers[2]

        val user1 = User.find("uid", uid1).firstResult()
        val user2 = User.find("uid", uid2).firstResult()
        val user3 = User.find("uid", uid3).firstResult()
        assertNotNull(user1, "1人目のユーザーが正常に作成されていること")
        assertEquals("fanme-user-1", user1?.name, "1人目のユーザーの名前が正しく設定されていること")
        assertEquals(1, user1?.purpose, "1人目のユーザーの目的が正しく設定されていること")

        assertNotNull(user2, "2人目のユーザーが正常に作成されていること")
        assertEquals("fanme-user-2", user2?.name, "2人目のユーザーの名前が正しく設定されていること")
        assertEquals(2, user2?.purpose, "2人目のユーザーの目的が正しく設定されていること")

        assertNotNull(user3, "3人目のユーザーが正常に作成されていること")
        assertEquals("fanme-user-3", user3?.name, "3人目のユーザーの名前が正しく設定されていること")
        assertEquals(0, user3?.purpose, "3人目のユーザーの目的が正しく設定されていること")

        // 同じUIDで再度作成しても重複しないことを確認
        createTestUsers(testUsers)

        // 各UIDのユーザーが1人ずつしか存在しないことを確認
        assertEquals(1, User.count("uid = ?1", uid1), "1人目のユーザーが重複して作成されていないこと")
        assertEquals(1, User.count("uid = ?1", uid2), "2人目のユーザーが重複して作成されていないこと")
        assertEquals(1, User.count("uid = ?1", uid3), "3人目のユーザーが重複して作成されていないこと")
    }

    /**
     * テスト用の複数ユーザーを作成するヘルパーメソッド 3種類の異なるユーザーを作成する
     *
     * @param uids 既存のユーザーIDのリスト（指定された場合は新規IDを生成せずに使用）
     * @return 作成したユーザーのUIDリスト
     */
    @TestTransaction
    protected fun createTestUsers(uids: List<String>? = null): List<String> {
        val uid1 = uids?.get(0) ?: getTestUid("multi-1")
        val uid2 = uids?.get(1) ?: getTestUid("multi-2")
        val uid3 = uids?.get(2) ?: getTestUid("multi-3")

        val userConfigs =
            listOf(
                Triple(uid1, "fanme-user-1", getTestAccountIdentity("multi-1")) to 1,
                Triple(uid2, "fanme-user-2", getTestAccountIdentity("multi-2")) to 2,
                Triple(uid3, "fanme-user-3", getTestAccountIdentity("multi-3")) to 0,
            )

        userConfigs.forEach { (userInfo, purpose) ->
            val (uid, name, accountIdentity) = userInfo
            UserFactory.createTestUser(uid, name, accountIdentity, purpose)
        }

        return listOf(uid1, uid2, uid3)
    }
}
