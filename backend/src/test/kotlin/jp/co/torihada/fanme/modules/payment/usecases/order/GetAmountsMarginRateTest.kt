package jp.co.torihada.fanme.modules.payment.usecases.order

import io.quarkus.test.junit.QuarkusTest
import java.math.BigDecimal
import jp.co.torihada.fanme.modules.fanme.controllers.CampaignEntryController
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.usecases.order.GetAmounts
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations

// GetAmounts の getMarginRate のテスト
// MEMO:
// これはプライベートな関数のテストであり、getAmounts() メソッドの保証をしたくて実装しています
// 本来ならGetAmountsの公開されているメソッドのテストを書き保証すべきですが、書くのに時間がかかりそうであったため、いったんプライベートメソッドのテストを記載した
@QuarkusTest
class GetAmountsMarginRateTest {
    @InjectMocks private lateinit var getAmounts: GetAmounts
    @Mock private lateinit var campaignEntryController: CampaignEntryController

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `DIGITAL_BUNDLE かつキャンペーン有効なら marginRate 0_05 になる`() {
        Mockito.`when`(campaignEntryController.hasActiveCampaignEntry("user-uid", "MARGIN_TYPE1"))
            .thenReturn(true)

        val item =
            Item().apply {
                itemType = ItemType.DIGITAL_BUNDLE
                marginRate = 0.265f
            }
        val result =
            getAmounts.run {
                val method =
                    this.javaClass.getDeclaredMethod(
                        "getMarginRate",
                        Item::class.java,
                        String::class.java,
                    )
                method.isAccessible = true
                method.invoke(this, item, "user-uid") as BigDecimal
            }
        assertEquals(BigDecimal("0.05"), result)
    }

    @Test
    fun `DIGITAL_BUNDLEかつキャンペーン無効なら items の marginRateになる`() {
        Mockito.`when`(campaignEntryController.hasActiveCampaignEntry("user-uid", "MARGIN_TYPE1"))
            .thenReturn(false)

        val item =
            Item().apply {
                itemType = ItemType.DIGITAL_BUNDLE
                marginRate = 0.265f
            }
        val result =
            getAmounts.run {
                val method =
                    this.javaClass.getDeclaredMethod(
                        "getMarginRate",
                        Item::class.java,
                        String::class.java,
                    )
                method.isAccessible = true
                method.invoke(this, item, "user-uid") as BigDecimal
            }
        assertEquals(BigDecimal("0.265"), result)
    }

    @Test
    fun `DIGITAL_BUNDLE以外はエントリ中でも items の marginRateになる`() {
        Mockito.`when`(campaignEntryController.hasActiveCampaignEntry("user-uid", "MARGIN_TYPE1"))
            .thenReturn(true)

        val item =
            Item().apply {
                itemType = ItemType.CHEKI
                marginRate = 0.265f
            }
        val result =
            getAmounts.run {
                val method =
                    this.javaClass.getDeclaredMethod(
                        "getMarginRate",
                        Item::class.java,
                        String::class.java,
                    )
                method.isAccessible = true
                method.invoke(this, item, "user-uid") as BigDecimal
            }
        assertEquals(BigDecimal("0.265"), result)
    }
}
