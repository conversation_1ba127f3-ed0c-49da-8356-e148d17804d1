package jp.co.torihada.fanme.modules.payment.factories

import java.time.Instant
import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Tip

object CheckoutFactory {
    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        type: String? = null,
        orderId: String? = null,
        accessId: String? = null,
        tip: Tip? = null,
        accessPass: String? = null,
        providerShopId: String? = null,
        status: String? = null,
        amount: Int? = null,
        total: Int? = null,
        convenience: String? = null,
        confNo: String? = null,
        paymentTerm: Instant? = null,
        receiptUrl: String? = null,
        gmoRequest: String? = null,
        metadata: String = "{}",
        cvsFee: Int? = null,
        deliveryFee: Int? = null,
        errorInfo: String? = null,
        sellerUserId: String? = null,
        purchaserUserId: String? = null,
        sellerSalesAmount: Int? = null,
        tenantSalesAmount: Int? = null,
        isShop: Boolean = false,
    ): Checkout {
        return Checkout().apply {
            this.id = id
            this.tenant = tenant ?: "tenant"
            this.type = type ?: "type"
            this.isShop = isShop
            this.orderId = orderId
            this.accessId = accessId
            this.tip = tip
            this.accessPass = accessPass
            this.providerShopId = providerShopId
            this.status = status
            this.amount = amount ?: 100
            this.total = total ?: 100
            this.convenience = convenience
            this.confNo = confNo
            this.paymentTerm = paymentTerm
            this.receiptUrl = receiptUrl
            this.gmoRequest = gmoRequest
            this.metadata = metadata
            this.cvsFee = cvsFee
            this.deliveryFee = deliveryFee
            this.errorInfo = errorInfo
            this.sellerUserId = sellerUserId
            this.purchaserUserId = purchaserUserId
            this.sellerSalesAmount = sellerSalesAmount
            this.tenantSalesAmount = tenantSalesAmount
        }
    }
}
