package jp.co.torihada.fanme.modules.payment.mock.gmo

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoMemberClient
import jp.co.torihada.fanme.modules.payment.externals.entity.member.SaveMember
import jp.co.torihada.fanme.modules.payment.externals.entity.member.SearchMember
import org.mockito.Mockito

@ApplicationScoped
class MockExtMemberClient {
    fun setupMocks(extGmoMemberClient: ExtGmoMemberClient, config: Config) {
        Mockito.`when`(
                extGmoMemberClient.saveMember(
                    SaveMember.Request(
                        siteId = config.gmoSiteId(),
                        sitePass = config.gmoSitePass(),
                        memberId = "sampleUserId@fanme",
                    )
                )
            )
            .thenReturn(SaveMember.Response(memberId = "sampleUserId@fanme"))

        Mockito.`when`(
                extGmoMemberClient.searchMember(
                    SearchMember.Request(
                        siteId = config.gmoSiteId(),
                        sitePass = config.gmoSitePass(),
                        memberId = "sampleUserId@fanme",
                    )
                )
            )
            .thenReturn(
                SearchMember.Response(
                    memberId = "sampleUserId@fanme",
                    memberName = "",
                    deleteFlag = "0",
                )
            )
    }
}
