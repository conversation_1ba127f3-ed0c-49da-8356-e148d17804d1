package jp.co.torihada.fanme

import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaField
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.fields
import java.io.File
import org.jboss.logging.Logger
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class LoggerDependencyInjectionTests {
    private lateinit var importedClasses: JavaClasses

    @BeforeEach
    fun setup() {
        importedClasses =
            ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .importPackages("jp.co.torihada.fanme")
    }

    @Test
    fun `Logger fields should use dependency injection`() {
        val rule =
            fields()
                .that()
                .haveRawType(Logger::class.java)
                .and()
                .areDeclaredInClassesThat()
                .resideInAnyPackage(
                    "..controllers..",
                    "..usecases..",
                    "..services..",
                    "..endpoints..",
                    "..batch..",
                )
                .should(useInjectAnnotation())
                .because(
                    "Logger fields in DI-managed classes should be injected using @Inject annotation"
                )

        rule.check(importedClasses)
    }

    @Test
    fun `Logger fields should not use fully qualified name`() {
        val srcDir = File("src/main/kotlin")
        val violations = mutableListOf<String>()

        srcDir
            .walkTopDown()
            .filter { it.isFile && it.extension == "kt" }
            .filter { file ->
                val path = file.path
                path.contains("controllers") ||
                    path.contains("usecases") ||
                    path.contains("services") ||
                    path.contains("endpoints") ||
                    path.contains("batch")
            }
            .forEach { file ->
                val content = file.readText()
                val lines = content.lines()

                lines.forEachIndexed { index, line ->
                    if (
                        line.contains("org.jboss.logging.Logger") &&
                            !line.trimStart().startsWith("import") &&
                            !line.trimStart().startsWith("*") &&
                            !line.trimStart().startsWith("//")
                    ) {

                        val relativePath = file.relativeTo(File(".")).path

                        if (!relativePath.contains("ResponseFilter")) {
                            violations.add("$relativePath:${index + 1} - $line")
                        }
                    }
                }
            }

        if (violations.isNotEmpty()) {
            val message = buildString {
                appendLine("Found ${violations.size} fully qualified Logger usage(s):")
                violations.forEach { appendLine("  $it") }
            }
            assertTrue(violations.isEmpty(), message)
        }
    }

    private fun useInjectAnnotation(): ArchCondition<JavaField> {
        return object : ArchCondition<JavaField>("use @Inject annotation properly") {
            override fun check(field: JavaField, events: ConditionEvents) {
                val ownerPackage = field.owner.packageName
                val ownerClassName = field.owner.simpleName

                if (ownerClassName == "ResponseFilter") {
                    return
                }

                val hasInjectAnnotation =
                    field.annotations.any { annotation ->
                        annotation.rawType.name == "jakarta.inject.Inject" ||
                            annotation.rawType.name == "javax.inject.Inject"
                    }

                if (!hasInjectAnnotation) {
                    val message = "${field.fullName} does not have @Inject annotation"
                    events.add(SimpleConditionEvent.violated(field, message))
                } else {
                    val modifiers = field.modifiers
                    if (modifiers.contains(com.tngtech.archunit.core.domain.JavaModifier.FINAL)) {
                        val message =
                            "${field.fullName} has @Inject but is final (val). Use 'lateinit var' instead"
                        events.add(SimpleConditionEvent.violated(field, message))
                    }
                }
            }
        }
    }
}
