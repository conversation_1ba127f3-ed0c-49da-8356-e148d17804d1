package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserEndpointTest {

    private var userId: Long = 0

    @BeforeAll
    @Transactional
    fun setupAll() {

        val user =
            UserFactory.createTestUser("test-user-1", "Test User", "<EMAIL>")
                ?: throw IllegalStateException("Failed to create test user")
        userId = user.id ?: throw IllegalStateException("User id is null")
        val consoleUser = ConsoleUserFactory.new(userId, null, UserRole.CREATOR_VALUE)
        consoleUser.persistAndFlush()
    }

    @AfterAll
    @Transactional
    fun cleanupAll() {
        ConsoleUser.deleteAll()
        ContentBlockGroup.deleteAll()
        ContentBlockDetail.deleteAll()
        ContentBlock.deleteAll()
        User.delete("uid LIKE ?1", "test-user-%")
    }

    @Test
    @DisplayName("SUPERロールでユーザー情報を取得できること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun superRoleCanGetUserInformation() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(200)
            .body("data.userResponse.user.id", equalTo(userId.toInt()))
            .body("data.userResponse.user.uid", equalTo("test-user-1"))
            .body("data.userResponse.user.name", equalTo("Test User"))
            .body("data.userResponse.role", equalTo("CREATOR"))
    }

    @Test
    @DisplayName("AGENTロールでユーザー情報を取得できないこと")
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun agentRoleCannotGetUserInformation() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(403)
            .body("data", equalTo(null))
    }

    @Test
    @DisplayName("不正なIDの場合は400エラーを返すこと")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun invalidIdReturns400Error() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/0")
            .then()
            .statusCode(400)
            .body("data", equalTo(emptyMap<String, Any>()))
    }

    @Test
    @DisplayName("存在しないユーザーIDの場合は404エラーが返されること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun nonExistentUserIdReturns404Error() {
        val nonExistentId = 99999L
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$nonExistentId")
            .then()
            .statusCode(404)
            .body("data", equalTo(emptyMap<String, Any>()))
    }

    @Test
    @DisplayName("JAX-RSフレームワークの不正な文字列入力の場合は404エラーが返されること")
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun jaxRsInvalidStringInputReturns404Error() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/abc")
            .then()
            .statusCode(404)
            .body("data", equalTo(null))
    }
}
