package jp.co.torihada.fanme.modules.fanme.usecases.audit

import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations

@QuarkusTest
class UpdateAuditStatusTest {

    @InjectMocks private lateinit var updateAuditStatus: UpdateAuditStatus

    @Mock private lateinit var auditGroupService: AuditGroupService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `監査ステータスが正常に更新されること`() {
        // Given
        val auditGroupId = 1L
        val status = AuditStatus.APPROVED
        val comment = "Test comment"
        val auditedUserUid = "test-auditor"

        // When
        updateAuditStatus.execute(
            auditGroupId = auditGroupId,
            status = status,
            comment = comment,
            auditedUserUid = auditedUserUid,
        )

        // Then
        Mockito.verify(auditGroupService)
            .updateAuditStatus(
                auditGroupId = auditGroupId,
                status = status,
                comment = comment,
                auditedUserUid = auditedUserUid,
            )
    }

    @Test
    fun `オプションパラメータなしで監査ステータスが正常に更新されること`() {
        // Given
        val auditGroupId = 1L
        val status = AuditStatus.REJECTED

        // When
        updateAuditStatus.execute(auditGroupId = auditGroupId, status = status)

        // Then
        Mockito.verify(auditGroupService)
            .updateAuditStatus(
                auditGroupId = auditGroupId,
                status = status,
                comment = null,
                auditedUserUid = null,
            )
    }
}
