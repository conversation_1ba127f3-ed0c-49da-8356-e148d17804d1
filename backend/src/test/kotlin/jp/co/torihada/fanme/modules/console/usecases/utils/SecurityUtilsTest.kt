package jp.co.torihada.fanme.modules.console.usecases.utils

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.AgencyFactory
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SecurityUtilsTest {

    @Inject lateinit var securityUtils: SecurityUtils

    private lateinit var testAgency1: Agency
    private lateinit var testAgency2: Agency
    private lateinit var testUser1: User
    private lateinit var testUser2: User
    private lateinit var testConsoleUser1: ConsoleUser
    private lateinit var testConsoleUser2: ConsoleUser

    @BeforeEach
    @Transactional
    fun setUp() {
        testAgency1 = AgencyFactory.new(name = "Security Test Agency 1")
        testAgency1.persist()

        testAgency2 = AgencyFactory.new(name = "Security Test Agency 2")
        testAgency2.persist()

        testUser1 =
            UserFactory.createTestUser(
                uid = "security-test-user-1",
                name = "Security Test User 1",
                accountIdentity = "<EMAIL>",
            )!!

        testUser2 =
            UserFactory.createTestUser(
                uid = "security-test-user-2",
                name = "Security Test User 2",
                accountIdentity = "<EMAIL>",
            )!!

        testConsoleUser1 =
            ConsoleUserFactory.new(
                creatorId = testUser1.id!!,
                agencyId = testAgency1.id,
                role = UserRole.AGENT_VALUE,
            )
        testConsoleUser1.persist()

        testConsoleUser2 =
            ConsoleUserFactory.new(
                creatorId = testUser2.id!!,
                agencyId = testAgency2.id,
                role = UserRole.AGENT_VALUE,
            )
        testConsoleUser2.persist()
    }

    @AfterEach
    @Transactional
    fun tearDown() {

        ConsoleUser.delete("user.uid in (?1, ?2)", "security-test-user-1", "security-test-user-2")

        User.delete("uid in (?1, ?2)", "security-test-user-1", "security-test-user-2")

        Agency.delete("name in (?1, ?2)", "Security Test Agency 1", "Security Test Agency 2")
    }

    @Test
    @Transactional
    fun `validateAgentAccess - エージェントロールでエージェンシーIDが一致しない場合、ForbiddenAccessExceptionを投げる`() {
        assertThrows<ForbiddenAccessException> {
            securityUtils.validateAgentAccess(
                testAgency2.id!!,
                testUser1.uid!!,
                UserRole.AGENT_VALUE,
            )
        }
    }

    @Test
    @Transactional
    fun `validateAgentAccess - コンソールユーザーが存在しない場合、ForbiddenAccessExceptionを投げる`() {
        assertThrows<ForbiddenAccessException> {
            securityUtils.validateAgentAccess(
                testAgency1.id!!,
                "non-existent-user",
                UserRole.AGENT_VALUE,
            )
        }
    }

    @Test
    @Transactional
    fun `validateAgentForUserAccess - エージェントロールで異なるエージェンシーのユーザーの場合、ForbiddenAccessExceptionを投げる`() {
        assertThrows<ForbiddenAccessException> {
            securityUtils.validateAgentForUserAccess(
                testUser2,
                UserRole.AGENT_VALUE,
                testUser1.uid!!,
            )
        }
    }
}
