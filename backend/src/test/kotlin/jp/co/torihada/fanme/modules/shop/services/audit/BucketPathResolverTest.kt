package jp.co.torihada.fanme.modules.shop.services.audit

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@QuarkusTest
class BucketPathResolverTest {

    @Inject lateinit var bucketPathResolver: BucketPathResolver

    @Test
    fun `正常なURLからバケット名とファイルパスが正しく取得できること`() {
        // Given
        val url =
            "https://s3.ap-northeast-1.amazonaws.com/prd.fanme-shop/public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564"

        // When
        val result = bucketPathResolver.resolve(url)

        // Then
        assertEquals("prd.fanme-shop", result.bucketName)
        assertEquals(
            "public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564",
            result.filePath,
        )
    }

    @Test
    fun `getBucketNameでバケット名のみが正しく取得できること`() {
        // Given
        val url =
            "https://s3.ap-northeast-1.amazonaws.com/prd.fanme-shop/public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564"

        // When
        val bucketName = bucketPathResolver.getBucketName(url)

        // Then
        assertEquals("prd.fanme-shop", bucketName)
    }

    @Test
    fun `getFilePathでファイルパスのみが正しく取得できること`() {
        // Given
        val url =
            "https://s3.ap-northeast-1.amazonaws.com/prd.fanme-shop/public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564"

        // When
        val filePath = bucketPathResolver.getFilePath(url)

        // Then
        assertEquals(
            "public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564",
            filePath,
        )
    }

    @Test
    fun `パスがない不正なURLでは例外がスローされること`() {
        // Given
        val invalidUrl = "https://s3.ap-northeast-1.amazonaws.com"

        // When, Then
        assertThrows<IllegalArgumentException> { bucketPathResolver.resolve(invalidUrl) }
    }

    @Test
    fun `isValidPathは正常なURLに対してtrueを返すこと`() {
        // Given
        val validUrl =
            "https://s3.ap-northeast-1.amazonaws.com/prd.fanme-shop/public/58bf9e4e-7541-437d-806a-2697499ae8ea/761c03ab-54ed-4bca-a0d0-7fd52ebcae8d_cover_image_1738564528564"

        // When
        val isValid = bucketPathResolver.isValidPath(validUrl)

        // Then
        assertTrue(isValid)
    }

    @Test
    fun `isValidPathは不正なURLに対してfalseを返すこと`() {
        // Given
        val invalidUrl = "invalid://url"

        // When
        val isValid = bucketPathResolver.isValidPath(invalidUrl)

        // Then
        assertFalse(isValid)
    }

    @Test
    fun `複数階層のパスを持つURLが正しく解析できること`() {
        // Given
        val url = "https://s3-domain.com/test-bucket/path1/path2/path3/image.jpg"

        // When
        val result = bucketPathResolver.resolve(url)

        // Then
        assertEquals("test-bucket", result.bucketName)
        assertEquals("path1/path2/path3/image.jpg", result.filePath)
    }
}
