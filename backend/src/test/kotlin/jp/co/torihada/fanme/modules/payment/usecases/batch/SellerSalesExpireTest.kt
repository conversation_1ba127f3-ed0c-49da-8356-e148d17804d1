package jp.co.torihada.fanme.modules.payment.usecases.batch

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.batch.usecases.SellerSalesExpire
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.factories.TenantAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.models.TenantAccountBalance
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class SellerSalesExpireTest {
    @Inject lateinit var config: Config
    @Inject lateinit var sellerSalesExpire: SellerSalesExpire

    @Test
    @TestTransaction
    fun `test seller sales expire`() {
        val sellerUserId = "sellerUserId"

        // DBの準備
        MonthlySellerSaleFactory.new(
                tenant = config.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = "202401",
                transactionAmount = 1000,
                remainingAmount = 735,
                transferStatus = Const.MonthlySellerSaleStatus.NotTransferred.value,
                expirationDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )
            .persist()

        TenantAccountBalanceFactory.new(tenant = config.tenant(), amount = 1000).persist()

        SellerAccountBalanceFactory.new(
                tenant = config.tenant(),
                sellerUserId = sellerUserId,
                amount = 735,
            )
            .persist()

        sellerSalesExpire.execute()

        // DBの確認
        val monthlySeller = MonthlySellerSale.findLast()
        val tenantAccountBalance = TenantAccountBalance.findByTenant(config.tenant())
        val sellerAccountBalance = SellerAccountBalance.findBySellerUserId(sellerUserId)

        monthlySeller?.let {
            assertEquals(it.transferStatus, Const.MonthlySellerSaleStatus.Expired.value)
            assertEquals(monthlySeller.remainingAmount, 0)
        }

        tenantAccountBalance?.let { assertEquals(it.amount, 1735) }
        sellerAccountBalance?.let { assertEquals(it.amount, 0) }
    }
}
