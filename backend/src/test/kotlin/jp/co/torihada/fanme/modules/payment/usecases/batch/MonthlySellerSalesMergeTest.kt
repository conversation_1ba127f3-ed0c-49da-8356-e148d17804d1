package jp.co.torihada.fanme.modules.payment.usecases.batch

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.batch.usecases.MonthlySellerSalesMerge
import jp.co.torihada.fanme.batch.usecases.SellerSalesExpire
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.factories.SellerAccountBalanceFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountActivity
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.jboss.logging.Logger
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class MonthlySellerSalesMergeTest {
    private val logger: Logger = Logger.getLogger(SellerSalesExpire::class.java)

    @Inject lateinit var config: Config
    @Inject lateinit var monthlySellerSalesMerge: MonthlySellerSalesMerge

    @Test
    @TestTransaction
    fun `test monthly seller sales merge`() {
        val yearMonth = "202401"

        // DBの準備
        MonthlySellerSaleFactory.new(
                tenant = config.tenant(),
                sellerUserId = "sellerUserId1",
                yearMonth = yearMonth,
                transactionAmount = 1000,
                remainingAmount = 735,
                approved = true,
                expirationDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )
            .persist()

        MonthlySellerSaleFactory.new(
                tenant = config.tenant(),
                sellerUserId = "sellerUserId2",
                yearMonth = yearMonth,
                transactionAmount = 1000,
                remainingAmount = 735,
                approved = true,
                expirationDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC),
            )
            .persist()

        SellerAccountBalanceFactory.new(
                tenant = config.tenant(),
                sellerUserId = "sellerUserId1",
                amount = 1000,
                accumulatedSales = 1000,
            )
            .persist()

        monthlySellerSalesMerge.execute(yearMonth)

        val sellerAccountBalance1 = SellerAccountBalance.findBySellerUserId("sellerUserId1")
        val sellerAccountBalance2 = SellerAccountBalance.findBySellerUserId("sellerUserId2")
        val monthlySellerSale1 =
            MonthlySellerSale.findBySellerUserIdAndYearMonth("sellerUserId1", yearMonth)
        val monthlySellerSale2 =
            MonthlySellerSale.findBySellerUserIdAndYearMonth("sellerUserId2", yearMonth)
        val sellerAccountActivity = SellerAccountActivity.findLast()

        monthlySellerSale1?.let {
            assertEquals(it.merged, true)
            assertEquals(it.approved, true)
        }

        monthlySellerSale2?.let {
            assertEquals(it.merged, true)
            assertEquals(it.approved, true)
        }

        sellerAccountBalance1?.let {
            assertEquals(it.amount, 1735)
            assertEquals(it.accumulatedSales, 1735)
        }

        sellerAccountBalance2?.let {
            assertEquals(it.amount, 735)
            assertEquals(it.accumulatedSales, 735)
        }

        sellerAccountActivity?.let {
            assertEquals(it.amount, 735)
            assertEquals(it.sellerUserId, "sellerUserId2")
            assertEquals(it.activityCode, Const.AccountActivityCode.AppSales.value)
            assertEquals(it.activityType, Const.AccountActivityType.Payment.value)
            assertEquals(it.balance, 735)
        }
    }
}
