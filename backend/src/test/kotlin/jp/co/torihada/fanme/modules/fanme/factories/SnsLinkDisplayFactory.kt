package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.SnsLink
import jp.co.torihada.fanme.modules.fanme.models.SnsLinkDisplay

object SnsLinkDisplayFactory {
    fun new(
        profile: Profile,
        snsLink: SnsLink,
        displayOrderNumber: Int = 1,
        displayable: Boolean = true,
    ): SnsLinkDisplay {
        return SnsLinkDisplay().apply {
            this.profile = profile
            this.snsLink = snsLink
            this.displayOrderNumber = displayOrderNumber
            this.displayable = displayable
        }
    }

    fun createTestSnsLinkDisplay(
        profile: Profile,
        snsLink: SnsLink,
        displayOrderNumber: Int = 1,
        displayable: Boolean = true,
    ): SnsLinkDisplay {
        return new(profile, snsLink, displayOrderNumber, displayable).apply {
            snsLink.snsLinkDisplay = this
            profile.snsLinkDisplays.add(this)
        }
    }
}
