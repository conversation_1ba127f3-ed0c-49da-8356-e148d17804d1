package jp.co.torihada.fanme.modules.fanme.usecases.snslink

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ProfileFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.SnsLink
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class CreateSnsLinksTest {

    @Inject private lateinit var createSnsLinks: CreateSnsLinks

    @Test
    @TestTransaction
    fun `should create new sns links successfully`() {
        val user =
            UserFactory.createTestUser()
                ?: throw IllegalStateException("Failed to create test user")
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val input =
            CreateSnsLinks.Input(
                userId = user.id!!,
                snsLinks =
                    listOf(
                        CreateSnsLinks.Input.SnsLinkInput(
                            type = "twitter",
                            accountIdentity = "@test_user",
                            displayOrderNumber = 1,
                            displayable = true,
                        ),
                        CreateSnsLinks.Input.SnsLinkInput(
                            type = "instagram",
                            accountIdentity = "test_instagram",
                            displayOrderNumber = 2,
                            displayable = false,
                        ),
                    ),
            )

        val result = createSnsLinks.execute(input)

        assertTrue(result.isOk)
        val response = result.unwrap()

        assertEquals(2, response.snsLinks.size)

        val savedSnsLinks = SnsLink.find("profile.id = ?1", profile.id as Any).list()
        assertEquals(2, savedSnsLinks.size)

        val twitterLink = savedSnsLinks.find { it.type == Const.SnsLinkType.TWITTER.dbStr }
        assertNotNull(twitterLink)
        assertEquals("@test_user", twitterLink!!.snsAccountId)
        assertEquals(1, twitterLink.snsLinkDisplay!!.displayOrderNumber)
        assertEquals(true, twitterLink.snsLinkDisplay!!.displayable)

        val instagramLink = savedSnsLinks.find { it.type == Const.SnsLinkType.INSTAGRAM.dbStr }
        assertNotNull(instagramLink)
        assertEquals("test_instagram", instagramLink!!.snsAccountId)
        assertEquals(2, instagramLink.snsLinkDisplay!!.displayOrderNumber)
        assertEquals(false, instagramLink.snsLinkDisplay!!.displayable)

        val responseTwitter = response.snsLinks.find { it.type == "SnsLink::Twitter" }
        assertNotNull(responseTwitter)
        assertEquals("@test_user", responseTwitter!!.snsAccountId)
        assertEquals(1, responseTwitter.snsLinkDisplay!!.displayOrderNumber)
        assertEquals(true, responseTwitter.snsLinkDisplay!!.displayable)
    }

    @Test
    @TestTransaction
    fun `should return error for invalid sns type`() {
        val user = UserFactory.createTestUser()!!
        val profile = ProfileFactory.new(user = user)
        profile.persistAndFlush()

        val input =
            CreateSnsLinks.Input(
                userId = user.id!!,
                snsLinks =
                    listOf(
                        CreateSnsLinks.Input.SnsLinkInput(
                            type = "invalid_type",
                            accountIdentity = "@test_user",
                            displayOrderNumber = 1,
                            displayable = true,
                        )
                    ),
            )

        val result = createSnsLinks.execute(input)
        val error = result.component2()

        assertTrue(result.isErr)
        assertTrue(error is IllegalArgumentException)
        assertTrue(error?.message?.contains("Invalid SNS link type") == true)
    }

    @Test
    @TestTransaction
    fun `should return error for non existent user`() {
        val input =
            CreateSnsLinks.Input(
                userId = 99999L,
                snsLinks =
                    listOf(
                        CreateSnsLinks.Input.SnsLinkInput(
                            type = "twitter",
                            accountIdentity = "@test_user",
                            displayOrderNumber = 1,
                            displayable = true,
                        )
                    ),
            )

        val result = createSnsLinks.execute(input)
        val error = result.component2()

        assertTrue(result.isErr)
        assertTrue(error is ResourceNotFoundException)
        assertTrue(error!!.message?.contains("Profile") == true)
    }
}
