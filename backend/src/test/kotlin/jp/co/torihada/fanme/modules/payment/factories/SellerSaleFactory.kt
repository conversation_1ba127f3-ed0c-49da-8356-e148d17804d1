package jp.co.torihada.fanme.modules.payment.factories

import io.github.serpro69.kfaker.Faker
import jp.co.torihada.fanme.modules.payment.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.payment.models.SellerSale
import jp.co.torihada.fanme.modules.payment.models.Transaction

object SellerSaleFactory {

    private val faker = Faker()

    fun new(
        id: Long? = null,
        tenant: String = DEFAULT_TENANT,
        transaction: Transaction? = null,
        sellerUserId: String? = null,
        amount: Int? = null,
    ): SellerSale {
        return SellerSale().apply {
            this.id = id
            this.tenant = tenant
            this.transaction = transaction
            this.sellerUserId = sellerUserId ?: faker.random.randomString()
            this.amount = amount ?: 1000
        }
    }
}
