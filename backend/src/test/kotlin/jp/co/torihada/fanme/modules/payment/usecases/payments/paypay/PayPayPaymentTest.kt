package jp.co.torihada.fanme.modules.payment.usecases.payments.paypay

import com.github.michaelbull.result.getOrElse
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.mock.auth.MockExtAuthClient
import jp.co.torihada.fanme.modules.payment.mock.gmo.MockExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Tip
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseEntryTransaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.CreateCheckout
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class PayPayPaymentTest {

    @Inject lateinit var commonConfig: Config
    @Inject lateinit var paymentConfig: jp.co.torihada.fanme.modules.payment.Config
    @Inject lateinit var mockExtGmoPaymentClient: MockExtGmoPaymentClient
    @Inject lateinit var mockExtAuthClient: MockExtAuthClient
    @Inject lateinit var createCheckout: CreateCheckout
    @Inject lateinit var entryTransaction: EntryTransaction
    @Inject lateinit var execTransaction: ExecTransaction

    @InjectMock @RestClient lateinit var extGmoPaymentClient: ExtGmoPaymentClient
    @InjectMock @RestClient lateinit var extAuthClient: ExtAuthClient

    @BeforeEach
    fun setUp() {
        mockExtGmoPaymentClient.setupMocks(
            extGmoPaymentClient,
            commonConfig,
            paymentConfig,
            "paypay",
        )
        mockExtAuthClient.setupMocks(extAuthClient, paymentConfig)
    }

    @Test
    @TestTransaction
    fun `test paypay payment success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout
            .execute(
                CreateCheckout.Input(
                    sellerUserId = sellerUserId,
                    purchaserUserId = purchaserUserId,
                    totalAmount = 1100,
                    itemsAmount = 1000,
                    profit = 820,
                    fee = 280,
                    isDigital = true,
                    deliveryFee = 0,
                    tipAmount = 100,
                    paymentType = PaymentConst.PaymentType.PAY_PAY,
                    convenience = null,
                )
            )
            .getOrElse { throw it }

        val checkout = Checkout.findLast()

        entryTransaction
            .execute(
                BaseEntryTransaction.Input(
                    checkoutId = checkout?.id ?: throw Exception("Failed to create checkout"),
                    orderId = checkout.orderId ?: throw Exception("Failed to create checkout"),
                    totalAmount = checkout.total ?: throw Exception("Failed to create checkout"),
                )
            )
            .getOrElse { throw it }

        val response =
            execTransaction
                .execute(
                    ExecTransaction.Input(
                        checkoutId = checkout.id!!,
                        orderId = checkout.orderId!!,
                        returnUrl = "https://example.com",
                    )
                )
                .getOrElse { throw it }

        // response確認
        assertEquals(
            response.url,
            "https://sampleStartUrl?Token=sampleToken&AccessID=sampleAccessId",
        )
        assertEquals(response.checkoutId, checkout.id)

        // DB確認
        val tip = Tip.findLast()

        checkout.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            assertEquals(it.total, 1100)
            assertEquals(it.amount, 1000)
            assertEquals(it.status, Const.CheckoutStatus.REQSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }
    }

    @Test
    @TestTransaction
    fun `test paypay payment for physical item success`() {
        val sellerUserId = "sellerUserId"
        val purchaserUserId = "purchaserUserId"

        createCheckout
            .execute(
                CreateCheckout.Input(
                    sellerUserId = sellerUserId,
                    purchaserUserId = purchaserUserId,
                    totalAmount = 1500,
                    itemsAmount = 1000,
                    profit = 820,
                    fee = 680,
                    isDigital = false,
                    deliveryFee = 400,
                    tipAmount = 100,
                    paymentType = PaymentConst.PaymentType.PAY_PAY,
                    convenience = null,
                )
            )
            .getOrElse { throw it }

        val checkout = Checkout.findLast()

        entryTransaction
            .execute(
                BaseEntryTransaction.Input(
                    checkoutId = checkout?.id ?: throw Exception("Failed to create checkout"),
                    orderId = checkout.orderId ?: throw Exception("Failed to create checkout"),
                    totalAmount = checkout.total ?: throw Exception("Failed to create checkout"),
                )
            )
            .getOrElse { throw it }

        val response =
            execTransaction
                .execute(
                    ExecTransaction.Input(
                        checkoutId = checkout.id!!,
                        orderId = checkout.orderId!!,
                        returnUrl = "https://example.com",
                    )
                )
                .getOrElse { throw it }

        // response確認
        assertEquals(
            response.url,
            "https://sampleStartUrl?Token=sampleToken&AccessID=sampleAccessId",
        )
        assertEquals(response.checkoutId, checkout.id)

        // DB確認
        val tip = Tip.findLast()

        checkout.let {
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
            // TODO 物販の審査が通ったら、gmoProductShopIdに変更する
            assertEquals(it.providerShopId, paymentConfig.gmoShopId())
            assertEquals(it.total, 1500)
            assertEquals(it.amount, 1000)
            assertEquals(it.deliveryFee, 400)
            assertEquals(it.status, Const.CheckoutStatus.REQSUCCESS.value)
        }

        tip?.let {
            assertEquals(it.amount, 100)
            assertEquals(it.sellerUserId, sellerUserId)
            assertEquals(it.purchaserUserId, purchaserUserId)
        }
    }
}
