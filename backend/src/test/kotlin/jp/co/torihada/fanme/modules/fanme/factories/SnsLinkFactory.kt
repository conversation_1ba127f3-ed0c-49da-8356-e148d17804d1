package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.SnsLink

object SnsLinkFactory {
    fun new(
        profile: Profile,
        type: String = Const.SnsLinkType.TWITTER.dbStr,
        snsAccountId: String = "testuser",
    ): SnsLink {
        return SnsLink().apply {
            this.profile = profile
            this.type = type
            this.snsAccountId = snsAccountId
        }
    }

    fun createMultipleTestSnsLinks(
        profile: Profile,
        snsLinksData: List<SnsLinkData> =
            listOf(
                SnsLinkData(Const.SnsLinkType.TWITTER.dbStr, "testuser", 1, true),
                SnsLinkData(Const.SnsLinkType.INSTAGRAM.dbStr, "testuser_insta", 2, true),
                SnsLinkData(Const.SnsLinkType.YOUTUBE.dbStr, "testuser_youtube", 3, false),
            ),
    ): List<SnsLink> {
        return snsLinksData.map { data ->
            new(profile = profile, type = data.type, snsAccountId = data.accountId).apply {
                profile.snsLinks.add(this)
            }
        }
    }

    data class SnsLinkData(
        val type: String,
        val accountId: String,
        val displayOrderNumber: Int,
        val displayable: Boolean,
    )
}
