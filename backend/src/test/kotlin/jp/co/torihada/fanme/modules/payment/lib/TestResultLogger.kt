package jp.co.torihada.fanme.modules.payment.lib

import org.jboss.logging.Logger
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.api.extension.TestWatcher

class TestResultLogger : TestWatcher {
    private val logger: Logger = Logger.getLogger(TestResultLogger::class.java)

    override fun testSuccessful(context: ExtensionContext?) {
        context?.let { logger.info("テスト成功: ${it.displayName}") }
    }

    override fun testFailed(context: ExtensionContext?, cause: Throwable?) {
        context?.let { logger.error("テスト失敗: ${it.displayName}", cause) }
    }
}
