package jp.co.torihada.fanme

import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ArchitectureTests {
    private val basePackage = "jp.co.torihada.fanme.modules"
    private lateinit var importedClasses: JavaClasses

    @BeforeEach
    fun setup() {
        importedClasses =
            ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .importPackages(basePackage)
    }

    private fun executeAndReport(rule: ArchRule, description: String) {
        println("\n=== アーキテクチャテスト: $description ===")
        try {
            rule.check(importedClasses)
            println("✓ 違反はありません")
        } catch (e: AssertionError) {
            println("❌ 違反が検出されました:")
            println(e.message)
            throw e
        }
    }

    @Test
    fun `エンドポイント層は他の層からアクセスされないこと`() {
        val rule =
            noClasses()
                .that()
                .resideOutsideOfPackage("$basePackage..endpoints..")
                .should()
                .accessClassesThat()
                .resideInAPackage("$basePackage..endpoints..")

        executeAndReport(rule, "エンドポイント層は他の層からアクセスされないこと")
    }

    @Test
    fun `PaymentモジュールがConsoleモジュールの不適切な層にアクセスしないこと`() {
        val rule =
            noClasses()
                .that()
                .resideInAPackage("$basePackage.payment..")
                .should()
                .accessClassesThat()
                .resideInAnyPackage(
                    "$basePackage.console.controllers..",
                    "$basePackage.console.usecases..",
                    "$basePackage.console.models..",
                )

        executeAndReport(rule, "PaymentモジュールがConsoleモジュールの不適切な層にアクセスしないこと")
    }

    @Test
    fun `consoleモジュールのmodel層はusecase層のみからアクセスされること`() {
        val rule =
            noClasses()
                .that()
                .resideInAPackage("$basePackage.console..")
                .and()
                .resideOutsideOfPackage("$basePackage.console.usecases..")
                .and()
                .resideOutsideOfPackage("$basePackage.console.models..")
                .and()
                .resideOutsideOfPackage("$basePackage.console.utils..")
                .should()
                .accessClassesThat()
                .resideInAPackage("$basePackage.console.models..")

        executeAndReport(rule, "consoleモジュールのmodel層はusecase層のみからアクセスされること")
    }
}
