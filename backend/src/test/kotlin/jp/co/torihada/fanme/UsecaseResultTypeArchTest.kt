package jp.co.torihada.fanme

import com.github.michaelbull.result.Result
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaMethod
import com.tngtech.archunit.core.domain.JavaParameterizedType
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UsecaseResultTypeArchTest {

    private val basePackage = "jp.co.torihada.fanme.modules"
    private lateinit var importedClasses: JavaClasses

    @BeforeEach
    fun setup() {
        importedClasses =
            ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .importPackages(basePackage)
    }

    @Test
    fun `all Usecase execute methods should return Result type`() {
        val rule: ArchRule =
            methods()
                .that()
                .haveName("execute")
                .and()
                .areDeclaredInClassesThat()
                .resideInAPackage("$basePackage..usecases..")
                .and()
                .areDeclaredInClassesThat()
                .doNotHaveSimpleName("Input")
                .and()
                .areDeclaredInClassesThat()
                .doNotHaveSimpleName("Output")
                .should(haveProperResultType())
                .allowEmptyShould(true)
                .because("Usecaseのexecuteメソッドは Result<SuccessType, ErrorType> 型を返す必要があります。")

        rule.check(importedClasses)
    }

    private fun haveProperResultType(): ArchCondition<JavaMethod> {
        return object : ArchCondition<JavaMethod>("Result<Value, Error> 型を返す") {

            override fun check(method: JavaMethod, events: ConditionEvents) {
                val returnType = method.returnType

                if (!returnType.toErasure().isAssignableTo(Result::class.java)) {
                    val message = buildViolationMessage(method, "は Result 型を返していません。")
                    events.add(SimpleConditionEvent.violated(method, message))
                    return
                }

                if (returnType !is JavaParameterizedType) {
                    val message =
                        buildViolationMessage(method, "の戻り値のResult型にジェネリクスが指定されていません（Raw Type）。")
                    events.add(SimpleConditionEvent.violated(method, message))
                    return
                }

                val typeArguments = returnType.actualTypeArguments
                if (typeArguments.size != 2) {
                    val message =
                        buildViolationMessage(
                            method,
                            "の戻り値のResult型は2つの型引数（成功時の型、エラー時の型）を持つ必要があります。",
                        )
                    events.add(SimpleConditionEvent.violated(method, message))
                    return
                }
            }

            private fun buildViolationMessage(method: JavaMethod, detail: String): String {
                return "メソッド `${method.owner.simpleName}.${method.name}` は規約違反です: $detail (at ${method.sourceCodeLocation})"
            }
        }
    }
}
