<mxfile host="drawio-plugin" modified="2025-02-14T06:07:47.598Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="SgkzJaDzYiTwxHpxXz0Y" version="22.1.22" type="embed" pages="2">
  <diagram id="AQS1h3ApAYywRMHjuPEP" name="module_dependencies">
    <mxGraphModel dx="1658" dy="667" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="16" value="&lt;h1&gt;&lt;br&gt;&lt;/h1&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#000000;align=center;labelPosition=center;verticalLabelPosition=middle;verticalAlign=middle;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="-60" width="770" height="680" as="geometry" />
        </mxCell>
        <mxCell id="6" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#FFFFFF;fillColor=#000000;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry y="120" width="500" height="370" as="geometry" />
        </mxCell>
        <mxCell id="8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="1" source="2" target="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="380" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2" value="fanme" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#FFFFFF;fillColor=#000000;fontColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="70" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="1" source="4" target="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="380" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="4" target="2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="270" />
              <mxPoint x="130" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="4" value="shop" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#FFFFFF;fillColor=#000000;fontColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="200" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="1" target="7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="270" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="1" source="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="510" />
              <mxPoint x="260" y="510" />
            </Array>
            <mxPoint x="260" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="1" source="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="410" y="80" />
              <mxPoint x="130" y="80" />
              <mxPoint x="130" y="170" />
            </Array>
            <mxPoint x="130" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5" value="payment" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#000000;strokeColor=#FFFFFF;fontColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="350" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7" value="auth" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#000000;strokeColor=#FFFFFF;fontColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="570" y="20" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-18" value="" style="endArrow=classic;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="570" as="sourcePoint" />
            <mxPoint x="124" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-19" value="" style="endArrow=classic;html=1;rounded=0;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="600" as="sourcePoint" />
            <mxPoint x="124" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-20" value="" style="endArrow=classic;html=1;rounded=0;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="630" as="sourcePoint" />
            <mxPoint x="124" y="630" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-23" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="50" y="557" width="440" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-21" value="&lt;font color=&quot;#ffffff&quot;&gt;モジュールの依存関係、この方向は関数呼び出しを行う&lt;/font&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="NXmy7DJg7n7d-cer6QCf-23" vertex="1">
          <mxGeometry x="61" width="360" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-24" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="50" y="587" width="490" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-25" value="&lt;font color=&quot;#ffffff&quot;&gt;モジュールの依存関係に反するため、エンドポイント使って通信を行う&lt;br&gt;&lt;/font&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="NXmy7DJg7n7d-cer6QCf-24" vertex="1">
          <mxGeometry x="75.38461538461537" width="414.6153846153846" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="NXmy7DJg7n7d-cer6QCf-24" source="NXmy7DJg7n7d-cer6QCf-25" target="NXmy7DJg7n7d-cer6QCf-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-26" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="61" y="616" width="450" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-27" value="&lt;font color=&quot;#ffffff&quot;&gt;auth-serverとの通信、共通のexternals.client.authを利用する&lt;br&gt;&lt;/font&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="NXmy7DJg7n7d-cer6QCf-26" vertex="1">
          <mxGeometry x="80" width="330" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXmy7DJg7n7d-cer6QCf-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="1" target="4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="200" as="sourcePoint" />
            <mxPoint x="200" y="345" as="targetPoint" />
            <Array as="points">
              <mxPoint x="70" y="200" />
              <mxPoint x="-30" y="200" />
              <mxPoint x="-30" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="17" value="batch" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#FFFFFF;fillColor=#000000;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="40" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" edge="1" parent="1" target="2">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="100" y="410" as="sourcePoint" />
            <mxPoint x="140" y="240" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="413" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" edge="1" parent="1" target="4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="160" y="430" as="sourcePoint" />
            <mxPoint x="140" y="240" as="targetPoint" />
            <Array as="points">
              <mxPoint x="160" y="430" />
              <mxPoint x="230" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" edge="1" parent="1" source="17" target="5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="440" as="sourcePoint" />
            <mxPoint x="240" y="390" as="targetPoint" />
            <Array as="points">
              <mxPoint x="410" y="455" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="xP0-8-R6NyzOQSMpwjbw" name="architecture">
    <mxGraphModel dx="1063" dy="667" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Vke1BftDt6dpFizIsI1W-14" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="70" y="190" width="410" height="560" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-1" target="Vke1BftDt6dpFizIsI1W-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-1" value="&lt;h2&gt;endpoints&lt;/h2&gt;APIエンドポイント&lt;br&gt;外部とのI/Fを定義する" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="60" width="350" height="110" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-2" target="Vke1BftDt6dpFizIsI1W-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-2" value="&lt;h2&gt;controllers&lt;/h2&gt;コントローラー層&lt;br&gt;モジュール間のI/Fを定義する&lt;br&gt;入力値のバリデーションはここで行う" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="250" width="350" height="120" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-3" target="Vke1BftDt6dpFizIsI1W-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-3" target="Vke1BftDt6dpFizIsI1W-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-3" value="&lt;h2&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;usecase&lt;/font&gt;&lt;/h2&gt;ユースケース層&lt;br&gt;ビジネスロジックはここに実装する&lt;br&gt;依存関係に従って別モジュールのcontrollerを呼び出せる" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="410" width="350" height="140" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-4" value="&lt;h2&gt;model&lt;/h2&gt;ORM&lt;br&gt;DBへのクエリはここに定義してusecaseで利用する" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="590" width="350" height="120" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-8" target="Vke1BftDt6dpFizIsI1W-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-8" value="controller" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="630" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Vke1BftDt6dpFizIsI1W-9" target="Vke1BftDt6dpFizIsI1W-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-9" value="usecase" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="630" y="450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-10" value="model" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="630" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Vke1BftDt6dpFizIsI1W-17" value="&lt;h2&gt;modules.{module}&lt;/h2&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="60" y="200" width="210" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
