sequenceDiagram

    participant gmo as GMO
    box backend
        participant payment as payment
        participant shop as shop
        participant fanme as fanme
        participant ps as payment-server
    end

    gmo ->>+ payment: POST /webhook
    alt checkout.isShop == true
        payment ->> payment: update Checkout
        payment ->> payment: create Transaction
        payment ->>+ shop: PUT /orders
        shop ->> shop: update Order
        shop ->>+ fanme: create YellLogs
        fanme -->>- shop: none
        shop -->>- payment: result
    else
        payment ->>+ ps: call old payment-server webhook endpoint
        ps -->>- payment: result
    end
    payment ->>+ shop: POST /email/convenience
    shop -->> shop: send email
    shop -->>- payment: 200 OK
    payment -->>- gmo: result
