sequenceDiagram

    actor purchaser as Purchaser
    participant front as frontend
    box backend
    participant shop as shop
    participant payment as payment
    participant fanme as fanme
    end
    participant gmo as GMO

    purchaser ->>+ front: click purchase button
    front ->>+ shop: POST /orders
    shop ->> shop: checkOrder
    shop ->>+ payment: createCheckout
    payment -->>- shop: checkout
    shop ->> shop: createOrder
    shop ->>+ payment: entryTransaction
    payment -->>+ gmo: call entryTran API
    gmo -->>- payment: entryTran response
    payment -->>- shop: transaction
    shop -->>+ payment: executeTransaction
    payment -->>+ gmo: call execTran API
    gmo -->>- payment: execTran response
    payment -->>- shop: transaction
    shop ->> shop: update Order
    shop ->> shop: create PurchasedItems
    shop ->> shop: clear Cart(delete CartItems)
    shop ->> shop: send email
    shop ->>+ fanme: insert YellLogs
    fanme -->>- shop: none
    shop -->>- front: result
    front -->>- purchaser: view order success page