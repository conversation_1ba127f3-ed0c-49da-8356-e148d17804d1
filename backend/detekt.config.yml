build:
  maxIssues: 0

empty-blocks:
  active: false

complexity:
  active: false

style:
  active: true
  NewLineAtEndOfFile:
    active: false
  UnusedPrivateMember:
    active: true
  UnusedParameter:
    active: false
  WildcardImport:
    active: false
  MagicNumber:
    active: false
  ReturnCount:
    active: false
  ThrowsCount:
    active: false
  MaxLineLength:
    active: false
  ExplicitItLambdaParameter:
    active: false
  FunctionOnlyReturningConstant:
    active: false
  UtilityClassWithPublicConstructor:
    active: false
  UnusedPrivateProperty:
    active: false
  ProtectedMemberInFinalClass:
    active: false
  UseCheckOrError:
    active: false
  LoopWithTooManyJumpStatements:
    active: false

formatting:
  active: false

comments:
  active: false

exceptions:
  active: false

naming:
  active: false

performance:
  active: false
  
potential-bugs:
  active: false