#!/usr/bin/env node

const fs = require('fs');
const yaml = require('js-yaml');
const path = require('path');

const openapiPath = path.join(__dirname, '../backend/META-INF/openapi.yaml');

try {
  // OpenAPIファイルを読み込み
  const fileContents = fs.readFileSync(openapiPath, 'utf8');
  const doc = yaml.load(fileContents);
  
  // 数字付きの$refを検出
  const numberedRefs = [];
  
  function checkRefs(obj, path = '') {
    if (obj && typeof obj === 'object') {
      for (const [key, value] of Object.entries(obj)) {
        const currentPath = path ? `${path}.${key}` : key;
        
        if (key === '$ref' && typeof value === 'string') {
          // スキーマ名の末尾に数字があるかチェック
          const schemaMatch = value.match(/#\/components\/schemas\/([^\/]+)/);
          if (schemaMatch && /\d+$/.test(schemaMatch[1])) {
            numberedRefs.push({
              path: currentPath,
              ref: value,
              schemaName: schemaMatch[1]
            });
          }
        } else if (typeof value === 'object') {
          checkRefs(value, currentPath);
        }
      }
    }
  }
  
  checkRefs(doc);
  
  if (numberedRefs.length > 0) {
    console.warn('⚠️  数字を含む$refが検出されました（警告のみ、処理は継続されます）:');
    numberedRefs.forEach(ref => {
      console.warn(`  - ${ref.ref}`);
      console.warn(`    場所: ${ref.path}`);
    });
    process.exit(0);
  } else {
    console.log('✅ 数字を含む$refは検出されませんでした');
  }
} catch (error) {
  console.error('エラーが発生しました:', error.message);
  process.exit(1);
}